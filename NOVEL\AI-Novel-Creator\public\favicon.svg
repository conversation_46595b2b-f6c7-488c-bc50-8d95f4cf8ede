<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="32" height="32">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="16" cy="16" r="15" fill="url(#grad1)" stroke="#fff" stroke-width="1"/>
  
  <!-- 书本图标 -->
  <rect x="8" y="10" width="16" height="12" rx="1" fill="#fff" opacity="0.9"/>
  <rect x="9" y="11" width="14" height="10" rx="0.5" fill="none" stroke="#667eea" stroke-width="1"/>
  
  <!-- 页面线条 -->
  <line x1="11" y1="13" x2="21" y2="13" stroke="#667eea" stroke-width="0.5"/>
  <line x1="11" y1="15" x2="19" y2="15" stroke="#667eea" stroke-width="0.5"/>
  <line x1="11" y1="17" x2="20" y2="17" stroke="#667eea" stroke-width="0.5"/>
  <line x1="11" y1="19" x2="18" y2="19" stroke="#667eea" stroke-width="0.5"/>
  
  <!-- AI标识 -->
  <circle cx="20" cy="8" r="3" fill="#764ba2"/>
  <text x="20" y="10" text-anchor="middle" fill="#fff" font-size="3" font-family="Arial, sans-serif" font-weight="bold">AI</text>
</svg>
