# AI网文小说创作助手 开发文档

## 项目概述
一款基于Google Gemini AI的网文小说创作工具，支持Windows桌面和安卓移动端，提供全自动和半自动两种创作模式。

## 功能需求

### 1. 全自动创作模式
**输入参数：**
- 题材（玄幻、都市、科幻、历史、军事等）
- 类型（男频、女频）
- 子类型（修仙、重生、系统、穿越、霸总、甜宠等）
- 总章数（目标章节数量）
- 单章字数（每章字数要求）

**输出内容：**
- 自动生成小说大纲
- 逐章节标题生成
- 完整章节内容创作
- 人物设定和关系图
- 世界观设定

### 2. 半自动辅助模式
**智能写作助手：**
- 选中文本智能续写
- 段落扩展功能
- 文笔润色优化
- 语调风格调整

**情节辅助工具：**
- 剧情发展建议（3-5个方向）
- 冲突设计生成
- 伏笔提醒系统
- 节奏调整建议

**人物对话优化：**
- 对话内容润色
- 人物语言特色保持
- 情感表达增强
- 对话标签丰富化

**场景描写增强：**
- 环境细节描写
- 动作场面描写
- 心理活动描写
- 感官体验描写

**创作辅助面板：**
- 实时写作建议
- 一键应用功能
- 历史记录保存
- 自定义模板管理

## 技术架构

### 前端技术栈
- **框架**：Vue.js 3.x
- **UI组件**：Element Plus
- **状态管理**：Pinia
- **路由**：Vue Router
- **构建工具**：Vite

### 后端技术栈
- **运行时**：Node.js
- **框架**：Express.js
- **数据库**：SQLite（本地存储）
- **AI接入**：Google Gemini API
- **文件处理**：fs-extra

### 跨平台部署
- **Windows桌面**：Electron
- **安卓移动**：Cordova/Capacitor
- **打包工具**：electron-builder

## 开发计划

### 阶段1：项目初始化
- [x] 需求分析和功能设计
- [x] 技术选型和架构设计
- [ ] 项目结构搭建
- [ ] 开发环境配置

### 阶段2：核心功能开发
- [ ] Google Gemini API集成
- [ ] 数据库设计和实现
- [ ] 全自动创作核心逻辑
- [ ] 半自动辅助功能实现

### 阶段3：用户界面开发
- [ ] 主界面设计
- [ ] 创作编辑器实现
- [ ] 辅助工具面板
- [ ] 项目管理界面

### 阶段4：跨平台适配
- [ ] Electron桌面端适配
- [ ] 安卓移动端适配
- [ ] 平台特性优化

### 阶段5：测试和优化
- [ ] 功能测试
- [ ] 性能优化
- [ ] 用户体验优化
- [ ] 错误处理完善

## 项目结构
```
AI-Novel-Creator/
├── src/
│   ├── main/              # 主进程（Electron）
│   ├── renderer/          # 渲染进程（Vue.js）
│   │   ├── components/    # 组件
│   │   ├── views/        # 页面
│   │   ├── store/        # 状态管理
│   │   └── utils/        # 工具函数
│   ├── api/              # API接口
│   ├── database/         # 数据库相关
│   └── assets/           # 静态资源
├── public/               # 公共文件
├── build/               # 构建配置
├── dist/                # 构建输出
└── docs/                # 文档
```

## API设计

### Google Gemini集成
- 文本生成接口
- 对话补全接口
- 内容优化接口
- 创意扩展接口

### 本地数据接口
- 项目管理CRUD
- 章节内容管理
- 用户设置管理
- 模板管理

## 数据库设计

### 项目表（Projects）
- id: 项目ID
- title: 项目标题
- genre: 题材类型
- category: 男频/女频
- sub_category: 子类型
- target_chapters: 目标章数
- words_per_chapter: 单章字数
- created_at: 创建时间
- updated_at: 更新时间

### 章节表（Chapters）
- id: 章节ID
- project_id: 项目ID
- chapter_number: 章节序号
- title: 章节标题
- content: 章节内容
- word_count: 字数统计
- status: 状态（草稿/完成）
- created_at: 创建时间
- updated_at: 更新时间

### 设置表（Settings）
- key: 设置键
- value: 设置值
- type: 数据类型

## 开发环境配置

### 必需软件
- Node.js 18+
- npm/yarn
- Git
- VS Code（推荐）

### 环境变量
```env
GEMINI_API_KEY=your_gemini_api_key
DB_PATH=./data/novel.db
PORT=3000
```

### 开发命令
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 打包桌面应用
npm run build:electron

# 打包安卓应用
npm run build:android
```

## 部署说明

### Windows桌面版
- 使用electron-builder打包
- 生成.exe安装包
- 支持自动更新

### 安卓移动版
- 使用Cordova/Capacitor打包
- 生成.apk安装包
- 适配不同屏幕尺寸

---

*本文档随开发进度持续更新*