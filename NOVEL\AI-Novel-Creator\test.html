<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI小说创作助手 - 启动页</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .container {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 20px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            max-width: 500px;
            width: 90%;
        }

        .logo {
            font-size: 48px;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #FFD700, #FFA500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        h1 {
            font-size: 28px;
            margin-bottom: 15px;
            font-weight: 300;
        }

        .subtitle {
            font-size: 16px;
            opacity: 0.8;
            margin-bottom: 30px;
        }

        .status {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4CAF50;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 30px;
        }

        .status-icon {
            font-size: 24px;
            margin-bottom: 10px;
        }

        button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 16px;
            border-radius: 25px;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
        }

        .manual-nav {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            font-size: 14px;
            opacity: 0.7;
        }

        .url {
            background: rgba(255, 255, 255, 0.1);
            padding: 8px 15px;
            border-radius: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            letter-spacing: 1px;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">📚</div>
        <h1>AI小说创作助手</h1>
        <p class="subtitle">墨韵文轩 · 智能创作平台</p>

        <div class="status">
            <div class="status-icon">✅</div>
            <div>Electron 窗口已成功启动</div>
            <div style="font-size: 12px; margin-top: 5px;">正在准备跳转到主应用...</div>
        </div>

        <button onclick="goToApp()">
            <span class="loading" id="loading" style="display: none;"></span>
            立即进入主应用
        </button>

        <button onclick="openInBrowser()">在浏览器中打开</button>

        <div class="manual-nav">
            <div>如果自动跳转失败，请手动导航：</div>
            <div class="url">http://localhost:3000</div>
            <div style="margin-top: 10px;">
                按 <strong>Ctrl+L</strong> 然后输入上述地址
            </div>
        </div>
    </div>

    <script>
        let countdown = 5;

        function goToApp() {
            document.getElementById('loading').style.display = 'inline-block';
            console.log('尝试跳转到主应用...');
            window.location.href = 'http://localhost:3000';
        }

        function openInBrowser() {
            // 在Electron中，这会在默认浏览器中打开
            if (window.require) {
                const { shell } = window.require('electron');
                shell.openExternal('http://localhost:3000');
            } else {
                window.open('http://localhost:3000', '_blank');
            }
        }

        // 倒计时自动跳转
        function updateCountdown() {
            if (countdown > 0) {
                document.querySelector('.status div:last-child').textContent =
                    `${countdown} 秒后自动跳转到主应用...`;
                countdown--;
                setTimeout(updateCountdown, 1000);
            } else {
                goToApp();
            }
        }

        // 页面加载完成后开始倒计时
        window.addEventListener('load', () => {
            setTimeout(updateCountdown, 1000);
        });

        // 显示当前状态
        console.log('启动页已加载，Electron窗口工作正常！');
    </script>
</body>
</html>
