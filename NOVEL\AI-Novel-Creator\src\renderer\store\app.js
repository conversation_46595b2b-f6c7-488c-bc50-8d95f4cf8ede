import { defineStore } from 'pinia'
import { useAIStore } from './ai'
import { useProjectStore } from './project'

export const useAppStore = defineStore('app', {
  state: () => ({
    // 应用配置
    config: {
      geminiApiKey: '',
      autoSaveInterval: 30000, // 30秒自动保存
      theme: 'light',
      language: 'zh-CN'
    },
    // 当前项目
    currentProject: null,
    // 项目列表
    projects: [],
    // 加载状态
    loading: false,
    // 错误信息
    error: null
  }),

  getters: {
    isConfigured: (state) => {
      return !!state.config.geminiApiKey
    },
    projectCount: (state) => {
      return state.projects.length
    }
  },

  actions: {
    // 初始化应用
    async initializeApp() {
      try {
        this.setLoading(true)
        
        // 加载配置
        await this.loadConfig()
        
        // 初始化数据库
        const projectStore = useProjectStore()
        await projectStore.initializeDatabase()
        
        // 如果有API Key，初始化AI服务
        if (this.config.geminiApiKey) {
          const aiStore = useAIStore()
          await aiStore.initializeAI(this.config.geminiApiKey)
        }
        
        return { success: true }
      } catch (error) {
        console.error('App initialization error:', error)
        this.setError(`应用初始化失败: ${error.message}`)
        return { success: false, message: error.message }
      } finally {
        this.setLoading(false)
      }
    },

    // 设置配置（增强版）
    async setConfig(config) {
      const oldApiKey = this.config.geminiApiKey
      this.config = { ...this.config, ...config }
      await this.saveConfig()
      
      // 如果API Key发生变化，重新初始化AI服务
      if (config.geminiApiKey && config.geminiApiKey !== oldApiKey) {
        const aiStore = useAIStore()
        await aiStore.initializeAI(config.geminiApiKey)
      }
    },

    // 保存配置到本地
    async saveConfig() {
      try {
        localStorage.setItem('novelCreatorConfig', JSON.stringify(this.config))
        
        // 同时保存到数据库（如果已初始化）
        try {
          const projectStore = useProjectStore()
          if (projectStore.isInitialized) {
            // 这里可以保存配置到数据库
          }
        } catch (error) {
          // 忽略数据库保存错误
        }
      } catch (error) {
        console.error('保存配置失败:', error)
        throw error
      }
    },

    // 加载配置
    async loadConfig() {
      try {
        const saved = localStorage.getItem('novelCreatorConfig')
        if (saved) {
          this.config = { ...this.config, ...JSON.parse(saved) }
        }
      } catch (error) {
        console.error('加载配置失败:', error)
      }
    },

    // 设置当前项目
    setCurrentProject(project) {
      this.currentProject = project
    },

    // 添加项目
    addProject(project) {
      this.projects.push(project)
    },

    // 删除项目
    removeProject(projectId) {
      const index = this.projects.findIndex(p => p.id === projectId)
      if (index !== -1) {
        this.projects.splice(index, 1)
      }
    },

    // 更新项目
    updateProject(projectId, updates) {
      const index = this.projects.findIndex(p => p.id === projectId)
      if (index !== -1) {
        this.projects[index] = { ...this.projects[index], ...updates }
      }
    },

    // 设置加载状态
    setLoading(loading) {
      this.loading = loading
    },

    // 设置错误信息
    setError(error) {
      this.error = error
    },

    // 清除错误
    clearError() {
      this.error = null
    }
  }
})