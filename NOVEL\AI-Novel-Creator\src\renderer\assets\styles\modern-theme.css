/* 水墨书香风格主题系统 - 传统文化美学设计 */

/* 全局CSS变量 - 基于中国传统文化色彩体系 */
:root {
  /* === 水墨五色系统 === */
  /* 基于传统水墨画的"焦、浓、重、淡、清"五个层次 */
  --ink-jiao: #0d0d0d;         /* 焦墨 - 最深最浓的墨色 */
  --ink-nong: #1a1a1a;         /* 浓墨 - 深浓的墨色 */
  --ink-zhong: #2d2d2d;        /* 重墨 - 中等浓度 */
  --ink-dan: #4a4a4a;          /* 淡墨 - 较淡的墨色 */
  --ink-qing: #6b6b6b;         /* 清墨 - 最淡的墨色 */
  --ink-xi: #8a8a8a;           /* 稀墨 - 极淡如烟 */

  /* === 精致色彩系统 === */
  /* 重新设计的和谐色彩，追求视觉美感 */
  --zhu-sha: #e67e22;          /* 朱砂 - 温暖橙色 */
  --dan-qing: #27ae60;         /* 丹青 - 清新绿色 */
  --huang-jin: #f39c12;        /* 黄金 - 明亮金色 */
  --qing-hua: #3498db;         /* 青花 - 清澈蓝色 */
  --zi-tan: #8e44ad;           /* 紫檀 - 优雅紫色 */
  --mo-yu: #34495e;            /* 墨玉 - 深邃灰蓝 */
  --song-lv: #2ecc71;          /* 松绿 - 生机绿色 */
  --yan-zhi: #e74c3c;          /* 胭脂 - 活力红色 */
  --mo-lan: #9b59b6;           /* 墨兰 - 神秘紫色 */
  --zhu-zi: #af7ac5;           /* 竹紫 - 柔和紫色 */

  /* === 宣纸质感系统 === */
  /* 模拟不同年代和质地的宣纸效果 */
  --paper-xuan: #fefefe;       /* 新宣纸 - 纯白微暖 */
  --paper-pi: #faf8f5;         /* 皮纸 - 温润白色 */
  --paper-mian: #f7f5f1;       /* 棉纸 - 柔和米白 */
  --paper-zhu: #f5f3ef;        /* 竹纸 - 淡雅米色 */
  --paper-chen: #f2f0ec;       /* 陈纸 - 岁月痕迹 */
  --paper-gu: #ede9e3;         /* 古纸 - 历史沉淀 */

  /* === 语义化颜色映射 === */
  --text-primary: #2c3e50;        /* 深色文字，确保可读性 */
  --text-secondary: #34495e;      /* 次要文字，清晰可见 */
  --text-tertiary: #5d6d7e;       /* 第三级文字 */
  --text-muted: #7f8c8d;          /* 辅助文字，适中对比度 */
  --text-subtle: #95a5a6;         /* 微妙文字 */
  --text-placeholder: #bdc3c7;    /* 占位符文字 */

  /* === 背景层次系统 === */
  --bg-primary: var(--paper-xuan);
  --bg-secondary: var(--paper-pi);
  --bg-tertiary: var(--paper-mian);
  --bg-elevated: var(--paper-zhu);
  --bg-overlay: rgba(254, 254, 254, 0.95);
  --bg-modal: rgba(254, 254, 254, 0.98);

  /* === 边框系统 - 毛笔笔触效果 === */
  --border-hair: rgba(13, 13, 13, 0.04);      /* 毫毛轻触 */
  --border-light: rgba(13, 13, 13, 0.08);     /* 轻笔淡描 */
  --border-medium: rgba(13, 13, 13, 0.12);    /* 中锋用笔 */
  --border-heavy: rgba(13, 13, 13, 0.18);     /* 重笔浓墨 */
  --border-accent: var(--huang-jin);           /* 金线装饰 */
  --border-elegant: var(--song-lv);           /* 雅致边框 */

  /* === 阴影系统 - 纸张层次感 === */
  --shadow-paper-xs: 0 1px 2px rgba(13, 13, 13, 0.05);
  --shadow-paper-sm: 0 1px 3px rgba(13, 13, 13, 0.08), 0 1px 2px rgba(13, 13, 13, 0.06);
  --shadow-paper-md: 0 2px 6px rgba(13, 13, 13, 0.10), 0 1px 3px rgba(13, 13, 13, 0.08);
  --shadow-paper-lg: 0 4px 12px rgba(13, 13, 13, 0.12), 0 2px 6px rgba(13, 13, 13, 0.10);
  --shadow-paper-xl: 0 8px 24px rgba(13, 13, 13, 0.15), 0 4px 12px rgba(13, 13, 13, 0.12);
  --shadow-paper-2xl: 0 16px 48px rgba(13, 13, 13, 0.18), 0 8px 24px rgba(13, 13, 13, 0.15);
  --shadow-inset-light: inset 0 1px 3px rgba(13, 13, 13, 0.04);
  --shadow-inset-medium: inset 0 2px 6px rgba(13, 13, 13, 0.08);

  /* === 圆角系统 - 传统曲线美学 === */
  --radius-hair: 1px;          /* 毫毛弧度 */
  --radius-xs: 2px;            /* 极小弧度 */
  --radius-sm: 4px;            /* 小弧度 */
  --radius-md: 6px;            /* 中等弧度 */
  --radius-lg: 8px;            /* 大弧度 */
  --radius-xl: 12px;           /* 超大弧度 */
  --radius-2xl: 16px;          /* 极大弧度 */
  --radius-3xl: 24px;          /* 书卷弧度 */
  --radius-full: 9999px;       /* 完全圆形 */

  /* === 间距系统 - 黄金比例 === */
  --spacing-hair: 2px;         /* 毫毛间距 */
  --spacing-xs: 4px;           /* 极小间距 */
  --spacing-sm: 8px;           /* 小间距 */
  --spacing-md: 16px;          /* 中等间距 */
  --spacing-lg: 24px;          /* 大间距 */
  --spacing-xl: 32px;          /* 超大间距 */
  --spacing-2xl: 48px;         /* 极大间距 */
  --spacing-3xl: 64px;         /* 书页间距 */
  --spacing-4xl: 96px;         /* 章节间距 */

  /* === 字体系统 - 传统书法美学 === */
  --font-calligraphy: 'Noto Serif SC', 'Source Han Serif SC', 'STSong', 'SimSun', serif;
  --font-elegant: 'Noto Sans SC', 'Source Han Sans SC', 'PingFang SC', 'Microsoft YaHei', sans-serif;
  --font-modern: 'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif;
  --font-mono: 'JetBrains Mono', 'SF Mono', 'Monaco', 'Consolas', monospace;

  /* === 字重系统 === */
  --font-weight-thin: 100;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-heavy: 800;
  --font-weight-black: 900;

  /* === 动画系统 - 自然流畅的缓动 === */
  --ease-ink: cubic-bezier(0.25, 0.46, 0.45, 0.94);      /* 墨水流动 */
  --ease-brush: cubic-bezier(0.23, 1, 0.32, 1);          /* 毛笔书写 */
  --ease-paper: cubic-bezier(0.4, 0, 0.2, 1);            /* 纸张翻动 */
  --ease-elegant: cubic-bezier(0.34, 1.56, 0.64, 1);     /* 优雅弹性 */

  --duration-instant: 100ms;
  --duration-fast: 200ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;
  --duration-slower: 800ms;

  /* === 特殊效果变量 === */
  --blur-subtle: blur(0.5px);
  --blur-light: blur(1px);
  --blur-medium: blur(2px);
  --blur-heavy: blur(4px);

  /* === 美观渐变系统 === */
  --gradient-ink: linear-gradient(135deg, var(--ink-nong) 0%, var(--ink-zhong) 100%);
  --gradient-paper: linear-gradient(135deg, var(--paper-xuan) 0%, var(--paper-pi) 100%);
  --gradient-gold: linear-gradient(135deg, #f39c12 0%, #f1c40f 100%);
  --gradient-jade: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);

  /* === 现代美学渐变 === */
  --gradient-warm: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);     /* 温暖粉橙 */
  --gradient-cool: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);     /* 清凉薄荷 */
  --gradient-sunset: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);   /* 日落橙黄 */
  --gradient-ocean: linear-gradient(135deg, #667eea 0%, #764ba2 100%);    /* 深海紫蓝 */
  --gradient-forest: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);   /* 森林绿意 */
  --gradient-purple: linear-gradient(135deg, #9b59b6 0%, #af7ac5 100%);   /* 优雅紫色 */
  --gradient-elegant: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);  /* 典雅金粉 */
  --gradient-royal: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);    /* 皇家紫色 */
  --gradient-nature: linear-gradient(135deg, #00b894 0%, #00cec9 100%);   /* 自然青绿 */

  /* === 精美玻璃态效果 === */
  --glass-light: rgba(255, 255, 255, 0.1);      /* 轻盈透明 */
  --glass-medium: rgba(255, 255, 255, 0.2);     /* 中等透明 */
  --glass-heavy: rgba(255, 255, 255, 0.3);      /* 厚重透明 */
  --glass-warm: rgba(255, 248, 240, 0.25);      /* 暖调玻璃 */
  --glass-cool: rgba(240, 248, 255, 0.25);      /* 冷调玻璃 */
  --glass-tinted: rgba(248, 245, 255, 0.3);     /* 有色玻璃 */
  --backdrop-blur: blur(20px);                  /* 精美模糊 */
  --backdrop-blur-light: blur(10px);            /* 轻度模糊 */
  --backdrop-blur-heavy: blur(30px);            /* 深度模糊 */

  /* === 文雅新拟态效果 === */
  --neumorphism-light:
    6px 6px 12px rgba(180, 190, 200, 0.12),
    -6px -6px 12px rgba(255, 255, 255, 0.8);
  --neumorphism-inset:
    inset 6px 6px 12px rgba(180, 190, 200, 0.12),
    inset -6px -6px 12px rgba(255, 255, 255, 0.8);
  --neumorphism-soft:
    4px 4px 8px rgba(200, 210, 220, 0.1),
    -4px -4px 8px rgba(255, 255, 255, 0.9);
}

/* === 全局重置和基础样式 === */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

*::before,
*::after {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  -webkit-text-size-adjust: 100%;
}

body {
  font-family: var(--font-elegant);
  font-weight: var(--font-weight-normal);
  line-height: 1.7;
  color: var(--text-primary);
  background: var(--bg-secondary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-feature-settings: "kern" 1, "liga" 1;
  overflow-x: hidden;
}

/* === 宣纸背景系统 === */
/* 主背景 - 宣纸质感渐变 */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    /* 古典色彩点缀 */
    radial-gradient(ellipse at 15% 20%, rgba(212, 175, 55, 0.02) 0%, transparent 40%),
    radial-gradient(ellipse at 85% 80%, rgba(74, 103, 65, 0.015) 0%, transparent 40%),
    radial-gradient(ellipse at 50% 50%, rgba(196, 125, 39, 0.01) 0%, transparent 60%),
    /* 宣纸基础渐变 */
    linear-gradient(135deg, var(--paper-xuan) 0%, var(--paper-pi) 50%, var(--paper-mian) 100%);
  pointer-events: none;
  z-index: -3;
}

/* 纸张纹理层 */
body::after {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    /* 宣纸纤维纹理 */
    url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' stroke='%23000' stroke-width='0.3' stroke-opacity='0.02'%3E%3Cpath d='M10 10c5-5 15-5 20 0s15 5 20 0M10 30c5-5 15-5 20 0s15 5 20 0M10 50c5-5 15-5 20 0s15 5 20 0'/%3E%3Cpath d='M10 20c5 5 15 5 20 0s15-5 20 0M10 40c5 5 15 5 20 0s15-5 20 0'/%3E%3C/g%3E%3C/svg%3E"),
    /* 细微的点状纹理 */
    url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23000' fill-opacity='0.008'%3E%3Ccircle cx='3' cy='3' r='0.5'/%3E%3Ccircle cx='13' cy='7' r='0.3'/%3E%3Ccircle cx='7' cy='13' r='0.4'/%3E%3Ccircle cx='17' cy='17' r='0.2'/%3E%3C/g%3E%3C/svg%3E");
  background-size: 60px 60px, 20px 20px;
  background-position: 0 0, 10px 10px;
  pointer-events: none;
  z-index: -2;
  opacity: 0.6;
}

/* === 水墨书香设计工具类 === */

/* === 文字样式系统 === */
.text-calligraphy {
  font-family: var(--font-calligraphy);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  letter-spacing: 0.02em;
}

.text-elegant {
  font-family: var(--font-elegant);
  font-weight: var(--font-weight-normal);
  color: var(--text-secondary);
  letter-spacing: 0.01em;
}

.text-title {
  font-family: var(--font-calligraphy);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  letter-spacing: 0.03em;
  line-height: 1.4;
}

.text-muted {
  color: var(--text-muted);
}

.text-subtle {
  color: var(--text-subtle);
}

.text-gold {
  color: var(--huang-jin);
  font-weight: var(--font-weight-medium);
}

.text-jade {
  color: var(--song-lv);
  font-weight: var(--font-weight-medium);
}

.text-vermillion {
  color: var(--zhu-sha);
  font-weight: var(--font-weight-medium);
}

/* === 卡片系统 - 宣纸质感 === */
.card-paper {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-paper-md);
  transition: all var(--duration-normal) var(--ease-paper);
  position: relative;
  overflow: hidden;
}

.card-paper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, var(--huang-jin) 50%, transparent 100%);
  opacity: 0.3;
}

.card-paper:hover {
  border-color: var(--border-medium);
  box-shadow: var(--shadow-paper-lg);
  transform: translateY(-3px);
}

.card-elevated {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-paper-lg);
  transition: all var(--duration-normal) var(--ease-paper);
  position: relative;
}

.card-elevated::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(212, 175, 55, 0.02) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(74, 103, 65, 0.015) 0%, transparent 50%);
  pointer-events: none;
  border-radius: inherit;
}

.card-elevated:hover {
  border-color: var(--border-accent);
  box-shadow: var(--shadow-paper-xl);
  transform: translateY(-4px);
}

/* === 书卷风格卡片 === */
.card-scroll {
  background: var(--gradient-paper);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-paper-lg);
  position: relative;
  overflow: hidden;
}

.card-scroll::before {
  content: '';
  position: absolute;
  top: 8px;
  left: 8px;
  right: 8px;
  bottom: 8px;
  border: 1px solid var(--border-hair);
  border-radius: var(--radius-xl);
  pointer-events: none;
}

.card-scroll::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: var(--gradient-gold);
  opacity: 0.6;
}

/* === 按钮系统 - 传统美学设计 === */

/* 主要按钮 - 墨色风格 */
.btn-ink {
  background: var(--gradient-ink);
  color: var(--paper-xuan);
  border: 2px solid var(--ink-nong);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md) var(--spacing-xl);
  font-family: var(--font-elegant);
  font-weight: var(--font-weight-medium);
  font-size: 14px;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-ink);
  box-shadow: var(--shadow-paper-md);
  position: relative;
  overflow: hidden;
}

.btn-ink::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.15), transparent);
  transition: left var(--duration-slow) var(--ease-brush);
}

.btn-ink:hover::before {
  left: 100%;
}

.btn-ink:hover {
  background: var(--ink-jiao);
  border-color: var(--ink-jiao);
  box-shadow: var(--shadow-paper-lg);
  transform: translateY(-2px);
}

/* 金色按钮 - 黄金装饰 */
.btn-gold {
  background: var(--gradient-gold);
  color: var(--ink-jiao);
  border: 2px solid var(--huang-jin);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md) var(--spacing-xl);
  font-family: var(--font-calligraphy);
  font-weight: var(--font-weight-semibold);
  font-size: 14px;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-elegant);
  box-shadow: var(--shadow-paper-md);
  position: relative;
}

.btn-gold:hover {
  background: #f1c40f;
  transform: translateY(-2px) scale(1.02);
  box-shadow: var(--shadow-paper-xl);
}

/* 翠玉按钮 - 自然绿色 */
.btn-jade {
  background: var(--gradient-jade);
  color: var(--paper-xuan);
  border: 2px solid var(--song-lv);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md) var(--spacing-xl);
  font-family: var(--font-elegant);
  font-weight: var(--font-weight-medium);
  font-size: 14px;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-paper);
  box-shadow: var(--shadow-paper-md);
}

.btn-jade:hover {
  background: var(--dan-qing);
  border-color: var(--dan-qing);
  transform: translateY(-2px);
  box-shadow: var(--shadow-paper-lg);
}

/* 朱砂按钮 - 传统红色 */
.btn-vermillion {
  background: var(--zhu-sha);
  color: var(--paper-xuan);
  border: 2px solid var(--zhu-sha);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md) var(--spacing-xl);
  font-family: var(--font-elegant);
  font-weight: var(--font-weight-medium);
  font-size: 14px;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-ink);
  box-shadow: var(--shadow-paper-md);
}

.btn-vermillion:hover {
  background: var(--yan-zhi);
  border-color: var(--yan-zhi);
  transform: translateY(-2px);
  box-shadow: var(--shadow-paper-lg);
}

/* 次要按钮 - 宣纸风格 */
.btn-paper {
  background: var(--bg-primary);
  color: var(--text-primary);
  border: 1px solid var(--border-medium);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md) var(--spacing-xl);
  font-family: var(--font-elegant);
  font-weight: var(--font-weight-normal);
  font-size: 14px;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-paper);
  box-shadow: var(--shadow-paper-sm);
  position: relative;
}

.btn-paper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-paper);
  opacity: 0;
  transition: opacity var(--duration-normal) var(--ease-paper);
  border-radius: inherit;
}

.btn-paper:hover::before {
  opacity: 1;
}

.btn-paper:hover {
  border-color: var(--border-accent);
  box-shadow: var(--shadow-paper-md);
  transform: translateY(-1px);
}

/* 文字按钮 - 简约风格 */
.btn-text {
  background: transparent;
  color: var(--text-muted);
  border: none;
  padding: var(--spacing-sm) var(--spacing-md);
  font-family: var(--font-elegant);
  font-weight: var(--font-weight-normal);
  font-size: 14px;
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-paper);
  border-radius: var(--radius-md);
}

.btn-text:hover {
  color: var(--text-primary);
  background: rgba(212, 175, 55, 0.08);
}

/* === 输入框系统 - 宣纸质感 === */
.input-elegant {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md) var(--spacing-lg);
  font-family: var(--font-elegant);
  font-weight: var(--font-weight-normal);
  font-size: 14px;
  color: var(--text-primary);
  transition: all var(--duration-normal) var(--ease-paper);
  box-shadow: var(--shadow-inset-light);
  position: relative;
}

.input-elegant::placeholder {
  color: var(--text-placeholder);
  font-style: italic;
}

.input-elegant:focus {
  border-color: var(--huang-jin);
  box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1), var(--shadow-inset-medium);
  outline: none;
  background: var(--paper-xuan);
}

.input-elegant:hover {
  border-color: var(--border-medium);
  box-shadow: var(--shadow-paper-sm);
}

/* === 状态指示器 - 传统色彩 === */
.status-success {
  color: var(--dan-qing);
  background: rgba(39, 174, 96, 0.08);
  border: 1px solid rgba(39, 174, 96, 0.2);
  border-radius: var(--radius-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: 12px;
  font-weight: var(--font-weight-medium);
}

.status-warning {
  color: var(--huang-jin);
  background: rgba(212, 175, 55, 0.08);
  border: 1px solid rgba(212, 175, 55, 0.2);
  border-radius: var(--radius-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: 12px;
  font-weight: var(--font-weight-medium);
}

.status-error {
  color: var(--zhu-sha);
  background: rgba(231, 76, 60, 0.08);
  border: 1px solid rgba(231, 76, 60, 0.2);
  border-radius: var(--radius-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: 12px;
  font-weight: var(--font-weight-medium);
}

.status-info {
  color: var(--qing-hua);
  background: rgba(52, 152, 219, 0.08);
  border: 1px solid rgba(52, 152, 219, 0.2);
  border-radius: var(--radius-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: 12px;
  font-weight: var(--font-weight-medium);
}

/* === 水墨书香动画系统 === */

/* 墨滴扩散效果 */
@keyframes inkDrop {
  0% {
    opacity: 0;
    transform: scale(0.6) translateY(30px);
    filter: var(--blur-medium);
  }
  30% {
    opacity: 0.6;
    filter: var(--blur-light);
  }
  70% {
    opacity: 0.9;
    filter: var(--blur-subtle);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
    filter: blur(0);
  }
}

/* 毛笔书写效果 */
@keyframes brushStroke {
  0% {
    opacity: 0;
    transform: scaleX(0) skewX(-15deg);
    transform-origin: left center;
  }
  50% {
    opacity: 0.8;
    transform: scaleX(0.7) skewX(-5deg);
  }
  100% {
    opacity: 1;
    transform: scaleX(1) skewX(0deg);
  }
}

/* 书卷展开效果 */
@keyframes scrollUnfold {
  0% {
    opacity: 0;
    transform: rotateX(-90deg) scale(0.8);
    transform-origin: top center;
  }
  50% {
    opacity: 0.7;
    transform: rotateX(-30deg) scale(0.9);
  }
  100% {
    opacity: 1;
    transform: rotateX(0deg) scale(1);
  }
}

/* 宣纸飘动效果 */
@keyframes paperFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-8px) rotate(0.5deg);
  }
  50% {
    transform: translateY(-12px) rotate(0deg);
  }
  75% {
    transform: translateY(-6px) rotate(-0.5deg);
  }
}

/* 墨水流动效果 */
@keyframes inkFlow {
  0% {
    background-position: 0% 50%;
    opacity: 0.6;
  }
  25% {
    opacity: 0.8;
  }
  50% {
    background-position: 100% 50%;
    opacity: 1;
  }
  75% {
    opacity: 0.8;
  }
  100% {
    background-position: 0% 50%;
    opacity: 0.6;
  }
}

/* 金粉闪烁效果 */
@keyframes goldShimmer {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}

/* 翠玉光泽效果 */
@keyframes jadeGlow {
  0%, 100% {
    box-shadow: 0 0 10px rgba(74, 103, 65, 0.2);
  }
  50% {
    box-shadow: 0 0 20px rgba(74, 103, 65, 0.4);
  }
}

/* 渐入效果 */
@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 渐入旋转效果 */
@keyframes fadeInRotate {
  0% {
    opacity: 0;
    transform: rotate(-10deg) scale(0.8);
  }
  100% {
    opacity: 1;
    transform: rotate(0deg) scale(1);
  }
}

/* === 动画工具类 === */
.animate-ink-drop {
  animation: inkDrop 0.8s var(--ease-ink) forwards;
}

.animate-brush-stroke {
  animation: brushStroke 0.6s var(--ease-brush) forwards;
}

.animate-scroll-unfold {
  animation: scrollUnfold 0.8s var(--ease-paper) forwards;
}

.animate-paper-float {
  animation: paperFloat 4s var(--ease-paper) infinite;
}

.animate-ink-flow {
  background: linear-gradient(-45deg, var(--ink-qing), var(--ink-dan), var(--ink-qing), var(--ink-dan));
  background-size: 400% 400%;
  animation: inkFlow 3s ease infinite;
}

.animate-gold-shimmer {
  animation: goldShimmer 2s var(--ease-elegant) infinite;
}

.animate-jade-glow {
  animation: jadeGlow 3s var(--ease-paper) infinite;
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s var(--ease-paper) forwards;
}

.animate-fade-in-rotate {
  animation: fadeInRotate 0.8s var(--ease-elegant) forwards;
}

/* 延迟动画 */
.animate-delay-100 { animation-delay: 100ms; }
.animate-delay-200 { animation-delay: 200ms; }
.animate-delay-300 { animation-delay: 300ms; }
.animate-delay-400 { animation-delay: 400ms; }
.animate-delay-500 { animation-delay: 500ms; }

/* 响应式断点 */
@media (max-width: 640px) {
  :root {
    --spacing-xl: 24px;
    --spacing-2xl: 32px;
  }
}

@media (max-width: 768px) {
  .modern-card {
    border-radius: var(--radius-xl);
  }
}

/* === Element Plus 水墨书香风格覆盖 === */

/* 按钮组件 */
.el-button--primary {
  background: var(--gradient-ink);
  border-color: var(--ink-nong);
  border-radius: var(--radius-lg);
  font-family: var(--font-elegant);
  font-weight: var(--font-weight-medium);
  transition: all var(--duration-normal) var(--ease-ink);
  box-shadow: var(--shadow-paper-md);
  border-width: 2px;
}

.el-button--primary:hover,
.el-button--primary:focus {
  background: var(--ink-jiao);
  border-color: var(--ink-jiao);
  box-shadow: var(--shadow-paper-lg);
  transform: translateY(-2px);
}

.el-button--success {
  background: var(--gradient-jade);
  border-color: var(--song-lv);
  color: var(--paper-xuan);
}

.el-button--warning {
  background: var(--gradient-gold);
  border-color: var(--huang-jin);
  color: var(--ink-jiao);
}

.el-button--danger {
  background: var(--zhu-sha);
  border-color: var(--zhu-sha);
  color: var(--paper-xuan);
}

/* 卡片组件 */
.el-card {
  border-radius: var(--radius-xl);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-paper-md);
  background: var(--bg-primary);
  transition: all var(--duration-normal) var(--ease-paper);
  position: relative;
  overflow: hidden;
}

.el-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--gradient-gold);
  opacity: 0.4;
}

.el-card:hover {
  border-color: var(--border-accent);
  box-shadow: var(--shadow-paper-lg);
  transform: translateY(-2px);
}

.el-card__header {
  background: var(--gradient-paper);
  border-bottom: 1px solid var(--border-light);
  font-family: var(--font-calligraphy);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

/* 输入框组件 */
.el-input__wrapper {
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
  background: var(--bg-primary);
  transition: all var(--duration-normal) var(--ease-paper);
  font-family: var(--font-elegant);
  box-shadow: var(--shadow-inset-light);
  border-width: 1px;
}

.el-input__wrapper:hover {
  border-color: var(--border-medium);
  box-shadow: var(--shadow-paper-sm);
}

.el-input__wrapper.is-focus {
  border-color: var(--huang-jin);
  box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1), var(--shadow-inset-medium);
}

.el-input__inner {
  font-family: var(--font-elegant);
  color: var(--text-primary);
}

.el-input__inner::placeholder {
  color: var(--text-placeholder);
  font-style: italic;
}

/* 进度条组件 */
.el-progress-bar__outer {
  border-radius: var(--radius-full);
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  overflow: hidden;
}

.el-progress-bar__inner {
  border-radius: var(--radius-full);
  background: var(--gradient-jade);
  position: relative;
}

.el-progress-bar__inner::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.2) 50%, transparent 100%);
  animation: inkFlow 2s ease infinite;
}

/* 标签组件 */
.el-tag {
  border-radius: var(--radius-md);
  font-weight: var(--font-weight-medium);
  font-family: var(--font-elegant);
  border: 1px solid var(--border-light);
  font-size: 12px;
  padding: var(--spacing-xs) var(--spacing-sm);
}

.el-tag--success {
  background: rgba(74, 103, 65, 0.08);
  color: var(--song-lv);
  border-color: rgba(74, 103, 65, 0.2);
}

.el-tag--warning {
  background: rgba(212, 175, 55, 0.08);
  color: var(--huang-jin);
  border-color: rgba(212, 175, 55, 0.2);
}

.el-tag--danger {
  background: rgba(231, 76, 60, 0.08);
  color: var(--zhu-sha);
  border-color: rgba(231, 76, 60, 0.2);
}

.el-tag--info {
  background: rgba(45, 45, 45, 0.08);
  color: var(--ink-zhong);
  border-color: rgba(45, 45, 45, 0.2);
}

/* 对话框组件 */
.el-dialog {
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-paper-2xl);
  background: var(--bg-primary);
  border: 2px solid var(--border-light);
}

.el-dialog__header {
  background: var(--gradient-paper);
  border-bottom: 1px solid var(--border-light);
  border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
}

.el-dialog__title {
  font-family: var(--font-calligraphy);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

/* 菜单组件 */
.el-menu {
  border-radius: var(--radius-lg);
  background: transparent;
  border: none;
}

.el-menu-item {
  border-radius: var(--radius-lg);
  margin: var(--spacing-xs) var(--spacing-sm);
  transition: all var(--duration-normal) var(--ease-paper);
  font-family: var(--font-elegant);
  font-weight: var(--font-weight-medium);
  position: relative;
  overflow: hidden;
}

.el-menu-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: var(--gradient-gold);
  opacity: 0.1;
  transition: left var(--duration-slow) var(--ease-brush);
}

.el-menu-item:hover::before {
  left: 0;
}

.el-menu-item:hover {
  background: rgba(212, 175, 55, 0.08);
  transform: translateX(6px);
  color: var(--huang-jin);
}

.el-menu-item.is-active {
  background: var(--gradient-gold);
  color: var(--ink-jiao);
  font-weight: var(--font-weight-semibold);
  box-shadow: var(--shadow-paper-md);
}

.el-menu-item.is-active::after {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 60%;
  background: var(--ink-jiao);
  border-radius: var(--radius-full);
}

/* === 滚动条美化 - 传统风格 === */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-hair);
}

::-webkit-scrollbar-thumb {
  background: var(--gradient-ink);
  border-radius: var(--radius-lg);
  transition: all var(--duration-fast) var(--ease-paper);
  border: 1px solid var(--border-light);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--ink-jiao);
  box-shadow: var(--shadow-paper-sm);
}

::-webkit-scrollbar-corner {
  background: var(--bg-tertiary);
}

/* === 响应式断点 === */
@media (max-width: 640px) {
  :root {
    --spacing-xl: 24px;
    --spacing-2xl: 32px;
    --spacing-3xl: 48px;
  }

  .card-paper,
  .card-elevated {
    border-radius: var(--radius-lg);
  }

  .btn-ink,
  .btn-gold,
  .btn-jade,
  .btn-vermillion {
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: 13px;
  }
}

@media (max-width: 768px) {
  :root {
    --spacing-2xl: 32px;
    --spacing-3xl: 48px;
    --spacing-4xl: 64px;
  }

  .card-scroll {
    border-radius: var(--radius-xl);
  }
}

/* === 工具类 === */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.opacity-0 { opacity: 0; }
.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }
.opacity-100 { opacity: 1; }

.pointer-events-none { pointer-events: none; }
.cursor-pointer { cursor: pointer; }

.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }

.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }

.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-30 { z-index: 30; }

/* === 特殊效果类 === */
.ink-wash-bg {
  background:
    radial-gradient(circle at 30% 20%, rgba(13, 13, 13, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 70% 80%, rgba(45, 45, 45, 0.02) 0%, transparent 50%),
    var(--gradient-paper);
}

.paper-texture {
  position: relative;
}

.paper-texture::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23000' fill-opacity='0.01'%3E%3Cpath d='M20 20c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10zm10 0c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10z'/%3E%3C/g%3E%3C/svg%3E");
  pointer-events: none;
  border-radius: inherit;
}

.gold-accent {
  position: relative;
}

.gold-accent::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--gradient-gold);
  border-radius: var(--radius-full);
}

.jade-accent {
  position: relative;
}

.jade-accent::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--gradient-jade);
  border-radius: var(--radius-full);
}

/* ===== 现代化UI组件优化 ===== */

/* 玻璃态卡片 */
.glass-card {
  background: var(--glass-medium);
  backdrop-filter: var(--backdrop-blur);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-paper-lg);
  transition: all var(--duration-normal) var(--ease-paper);
}

.glass-card:hover {
  background: var(--glass-heavy);
  transform: translateY(-2px);
  box-shadow: var(--shadow-paper-xl);
}

/* 新拟态按钮 */
.neumorphism-btn {
  background: var(--bg-primary);
  border: none;
  border-radius: var(--radius-lg);
  box-shadow: var(--neumorphism-light);
  transition: all var(--duration-normal) var(--ease-elegant);
  cursor: pointer;
}

.neumorphism-btn:hover {
  transform: translateY(-1px);
}

.neumorphism-btn:active {
  box-shadow: var(--neumorphism-inset);
  transform: translateY(0);
}

/* 优化渐变文字效果 - 提高可读性 */
.gradient-text {
  background: linear-gradient(135deg, #b8860b 0%, #daa520 100%);  /* 更深的金色渐变 */
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: var(--font-weight-semibold);
}

.gradient-text.jade {
  background: linear-gradient(135deg, #2e7d32 0%, #4caf50 100%);  /* 更深的绿色渐变 */
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-text.elegant {
  background: linear-gradient(135deg, #5d4e75 0%, #8b7d6b 100%);  /* 更深的优雅色 */
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-text.purple {
  background: linear-gradient(135deg, #7b68ee 0%, #9370db 100%);  /* 更深的紫色渐变 */
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-text.warm {
  background: linear-gradient(135deg, #cd853f 0%, #daa520 100%);  /* 更深的暖色渐变 */
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 浮动动画 */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-6px); }
}

.floating {
  animation: float 3s ease-in-out infinite;
}

/* 脉冲动画 */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.pulsing {
  animation: pulse 2s ease-in-out infinite;
}

/* 渐入动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp var(--duration-slow) var(--ease-paper);
}

/* 现代化输入框 */
.modern-input {
  background: var(--bg-primary);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md);
  font-family: var(--font-elegant);
  transition: all var(--duration-normal) var(--ease-paper);
  position: relative;
  overflow: hidden;
}

.modern-input::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.1), transparent);
  transition: left var(--duration-slow) var(--ease-paper);
}

.modern-input:focus {
  border-color: var(--huang-jin);
  box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
  outline: none;
}

.modern-input:focus::before {
  left: 100%;
}

/* 现代化按钮组 */
.btn-group {
  display: flex;
  gap: var(--spacing-sm);
  padding: var(--spacing-xs);
  background: var(--bg-elevated);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-inset-light);
}

.btn-group .btn {
  flex: 1;
  padding: var(--spacing-sm) var(--spacing-md);
  background: transparent;
  border: none;
  border-radius: var(--radius-lg);
  font-family: var(--font-elegant);
  font-weight: var(--font-weight-medium);
  color: var(--text-muted);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-paper);
}

.btn-group .btn.active {
  background: var(--bg-primary);
  color: var(--text-primary);
  box-shadow: var(--shadow-paper-sm);
}

.btn-group .btn:hover:not(.active) {
  color: var(--text-secondary);
  background: rgba(255, 255, 255, 0.5);
}

/* ===== 文雅风格组件 ===== */

/* 美观卡片系统 */
.elegant-card {
  background: var(--glass-medium);
  backdrop-filter: var(--backdrop-blur);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-xl);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 1px 0 rgba(255, 255, 255, 0.4) inset;
  transition: all var(--duration-normal) var(--ease-elegant);
  position: relative;
  overflow: hidden;
}

.elegant-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
}

.elegant-card:hover {
  background: var(--glass-heavy);
  transform: translateY(-4px);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.15),
    0 1px 0 rgba(255, 255, 255, 0.6) inset;
}

/* 美观按钮系统 */
.elegant-btn {
  background: var(--gradient-elegant);
  border: none;
  border-radius: var(--radius-lg);
  color: #2c3e50;
  font-family: var(--font-elegant);
  font-weight: var(--font-weight-medium);
  font-size: 14px;
  padding: var(--spacing-md) var(--spacing-xl);
  transition: all var(--duration-normal) var(--ease-elegant);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  box-shadow:
    0 4px 15px rgba(0, 0, 0, 0.1),
    0 1px 0 rgba(255, 255, 255, 0.3) inset;
}

.elegant-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left var(--duration-slow) var(--ease-elegant);
}

.elegant-btn:hover::before {
  left: 100%;
}

.elegant-btn:hover {
  background: var(--gradient-royal);
  color: #ffffff;
  transform: translateY(-2px);
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.15),
    0 1px 0 rgba(255, 255, 255, 0.4) inset;
}

.elegant-btn:active {
  transform: translateY(0);
  box-shadow:
    0 2px 10px rgba(0, 0, 0, 0.1),
    0 1px 0 rgba(255, 255, 255, 0.2) inset;
}

/* 文雅输入框 - 优化可读性 */
.elegant-input {
  background: rgba(255, 255, 255, 0.95);   /* 高透明度白色背景 */
  border: 1px solid rgba(180, 190, 200, 0.4);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md);
  font-family: var(--font-elegant);
  font-size: 14px;                         /* 明确字体大小 */
  color: #2c3e50;                          /* 深色文字确保可读性 */
  transition: all var(--duration-normal) var(--ease-paper);
}

.elegant-input:focus {
  border-color: #9370db;                   /* 明确的焦点颜色 */
  box-shadow: 0 0 0 2px rgba(147, 112, 219, 0.2);
  outline: none;
  background: #ffffff;                     /* 焦点时完全不透明 */
}

/* 文雅标签 */
.elegant-tag {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--gradient-warm);
  border: 1px solid rgba(180, 190, 200, 0.2);
  border-radius: var(--radius-full);
  font-family: var(--font-elegant);
  font-size: 12px;
  color: var(--zi-tan);
  font-weight: var(--font-weight-medium);
}

.elegant-tag.purple {
  background: var(--gradient-purple);
  color: var(--paper-xuan);
}

.elegant-tag.jade {
  background: var(--gradient-jade);
  color: var(--paper-xuan);
}

/* ===== 美观主题组件 ===== */

/* 精美卡片变体 */
.beautiful-card {
  background: var(--glass-tinted);
  backdrop-filter: var(--backdrop-blur);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--radius-2xl);
  box-shadow:
    0 10px 40px rgba(0, 0, 0, 0.1),
    0 1px 0 rgba(255, 255, 255, 0.5) inset,
    0 -1px 0 rgba(0, 0, 0, 0.05) inset;
  transition: all var(--duration-normal) var(--ease-elegant);
  position: relative;
  overflow: hidden;
}

.beautiful-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--gradient-royal);
}

.beautiful-card:hover {
  transform: translateY(-6px) scale(1.02);
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.15),
    0 1px 0 rgba(255, 255, 255, 0.6) inset;
}

/* 渐变按钮 */
.gradient-btn {
  background: var(--gradient-warm);
  border: none;
  border-radius: var(--radius-xl);
  color: #ffffff;
  font-family: var(--font-elegant);
  font-weight: var(--font-weight-semibold);
  font-size: 14px;
  padding: var(--spacing-md) var(--spacing-2xl);
  transition: all var(--duration-normal) var(--ease-elegant);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  box-shadow:
    0 6px 20px rgba(0, 0, 0, 0.15),
    0 1px 0 rgba(255, 255, 255, 0.2) inset;
}

.gradient-btn::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all var(--duration-normal) var(--ease-elegant);
}

.gradient-btn:hover::after {
  width: 300px;
  height: 300px;
}

.gradient-btn:hover {
  transform: translateY(-3px);
  box-shadow:
    0 12px 30px rgba(0, 0, 0, 0.2),
    0 1px 0 rgba(255, 255, 255, 0.3) inset;
}

/* 发光效果 */
.glow-effect {
  position: relative;
}

.glow-effect::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: var(--gradient-royal);
  border-radius: inherit;
  z-index: -1;
  opacity: 0;
  transition: opacity var(--duration-normal) var(--ease-elegant);
}

.glow-effect:hover::before {
  opacity: 0.7;
  animation: glow-pulse 2s ease-in-out infinite alternate;
}

@keyframes glow-pulse {
  from {
    box-shadow: 0 0 20px rgba(108, 92, 231, 0.4);
  }
  to {
    box-shadow: 0 0 30px rgba(108, 92, 231, 0.8);
  }
}

/* 彩虹边框 */
.rainbow-border {
  position: relative;
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
}

.rainbow-border::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff);
  background-size: 400% 400%;
  border-radius: inherit;
  z-index: -1;
  animation: rainbow-flow 3s ease infinite;
}

@keyframes rainbow-flow {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}
