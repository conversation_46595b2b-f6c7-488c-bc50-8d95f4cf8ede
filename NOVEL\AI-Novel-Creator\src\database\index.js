import Database from 'better-sqlite3'
import path from 'path'
import fs from 'fs-extra'

class DatabaseService {
  constructor() {
    this.db = null
    this.dbPath = path.join(process.cwd(), 'data', 'novel.db')
  }

  // 初始化数据库
  async initialize() {
    try {
      // 确保数据目录存在
      await fs.ensureDir(path.dirname(this.dbPath))
      
      // 创建数据库连接
      this.db = new Database(this.dbPath)
      
      // 创建表
      await this.createTables()
      
      return true
    } catch (error) {
      console.error('Failed to initialize database:', error)
      throw error
    }
  }

  // 创建数据表
  async createTables() {
    const tables = {
      // 项目表
      projects: `
        CREATE TABLE IF NOT EXISTS projects (
          id TEXT PRIMARY KEY,
          title TEXT NOT NULL,
          genre TEXT NOT NULL,
          category TEXT NOT NULL,
          sub_genre TEXT,
          target_chapters INTEGER,
          words_per_chapter INTEGER,
          style TEXT,
          background TEXT,
          protagonist TEXT,
          status TEXT DEFAULT 'active',
          total_words INTEGER DEFAULT 0,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `,
      
      // 章节表
      chapters: `
        CREATE TABLE IF NOT EXISTS chapters (
          id TEXT PRIMARY KEY,
          project_id TEXT NOT NULL,
          chapter_number INTEGER NOT NULL,
          title TEXT NOT NULL,
          content TEXT,
          summary TEXT,
          word_count INTEGER DEFAULT 0,
          status TEXT DEFAULT 'draft',
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE
        )
      `,
      
      // 人物表
      characters: `
        CREATE TABLE IF NOT EXISTS characters (
          id TEXT PRIMARY KEY,
          project_id TEXT NOT NULL,
          name TEXT NOT NULL,
          role TEXT,
          description TEXT,
          personality TEXT,
          background TEXT,
          relationships TEXT,
          avatar TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE
        )
      `,
      
      // 大纲表
      outlines: `
        CREATE TABLE IF NOT EXISTS outlines (
          id TEXT PRIMARY KEY,
          project_id TEXT NOT NULL,
          summary TEXT,
          world_setting TEXT,
          main_conflict TEXT,
          chapter_plan TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE
        )
      `,
      
      // 设置表
      settings: `
        CREATE TABLE IF NOT EXISTS settings (
          key TEXT PRIMARY KEY,
          value TEXT,
          type TEXT DEFAULT 'string',
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `,
      
      // 创作历史表
      writing_history: `
        CREATE TABLE IF NOT EXISTS writing_history (
          id TEXT PRIMARY KEY,
          project_id TEXT NOT NULL,
          chapter_id TEXT,
          action_type TEXT NOT NULL,
          content_before TEXT,
          content_after TEXT,
          ai_prompt TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE,
          FOREIGN KEY (chapter_id) REFERENCES chapters (id) ON DELETE CASCADE
        )
      `
    }

    for (const [tableName, sql] of Object.entries(tables)) {
      this.db.exec(sql)
    }
  }

  // 执行SQL语句
  run(sql, params = []) {
    try {
      const stmt = this.db.prepare(sql)
      const result = stmt.run(...params)
      return { id: result.lastInsertRowid, changes: result.changes }
    } catch (error) {
      throw error
    }
  }

  // 查询单条记录
  get(sql, params = []) {
    try {
      const stmt = this.db.prepare(sql)
      return stmt.get(...params)
    } catch (error) {
      throw error
    }
  }

  // 查询多条记录
  all(sql, params = []) {
    try {
      const stmt = this.db.prepare(sql)
      return stmt.all(...params)
    } catch (error) {
      throw error
    }
  }

  // 项目相关操作
  async createProject(project) {
    const sql = `
      INSERT INTO projects (
        id, title, genre, category, sub_genre, target_chapters,
        words_per_chapter, style, background, protagonist
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `
    const params = [
      project.id,
      project.title,
      project.genre,
      project.category,
      project.subGenre,
      project.targetChapters,
      project.wordsPerChapter,
      project.style,
      project.background,
      project.protagonist
    ]
    
    await this.run(sql, params)
    return this.getProject(project.id)
  }

  async getProject(id) {
    const sql = 'SELECT * FROM projects WHERE id = ?'
    return await this.get(sql, [id])
  }

  async getAllProjects() {
    const sql = 'SELECT * FROM projects ORDER BY updated_at DESC'
    return await this.all(sql)
  }

  async updateProject(id, updates) {
    const fields = Object.keys(updates).map(key => `${key} = ?`).join(', ')
    const values = Object.values(updates)
    const sql = `UPDATE projects SET ${fields}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`
    
    await this.run(sql, [...values, id])
    return this.getProject(id)
  }

  async deleteProject(id) {
    const sql = 'DELETE FROM projects WHERE id = ?'
    return await this.run(sql, [id])
  }

  // 章节相关操作
  async createChapter(chapter) {
    const sql = `
      INSERT INTO chapters (
        id, project_id, chapter_number, title, content, summary, word_count
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `
    const params = [
      chapter.id,
      chapter.projectId,
      chapter.chapterNumber,
      chapter.title,
      chapter.content || '',
      chapter.summary || '',
      chapter.wordCount || 0
    ]
    
    await this.run(sql, params)
    return this.getChapter(chapter.id)
  }

  async getChapter(id) {
    const sql = 'SELECT * FROM chapters WHERE id = ?'
    return await this.get(sql, [id])
  }

  async getProjectChapters(projectId) {
    const sql = 'SELECT * FROM chapters WHERE project_id = ? ORDER BY chapter_number'
    return await this.all(sql, [projectId])
  }

  async updateChapter(id, updates) {
    // 如果更新了内容，重新计算字数
    if (updates.content) {
      updates.word_count = updates.content.replace(/\s/g, '').length
    }
    
    const fields = Object.keys(updates).map(key => `${key} = ?`).join(', ')
    const values = Object.values(updates)
    const sql = `UPDATE chapters SET ${fields}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`
    
    await this.run(sql, [...values, id])
    
    // 更新项目总字数
    await this.updateProjectTotalWords(await this.getChapter(id).then(c => c.project_id))
    
    return this.getChapter(id)
  }

  async deleteChapter(id) {
    const chapter = await this.getChapter(id)
    const sql = 'DELETE FROM chapters WHERE id = ?'
    const result = await this.run(sql, [id])
    
    // 更新项目总字数
    if (chapter) {
      await this.updateProjectTotalWords(chapter.project_id)
    }
    
    return result
  }

  // 更新项目总字数
  async updateProjectTotalWords(projectId) {
    const sql = `
      UPDATE projects 
      SET total_words = (
        SELECT COALESCE(SUM(word_count), 0) 
        FROM chapters 
        WHERE project_id = ?
      )
      WHERE id = ?
    `
    return await this.run(sql, [projectId, projectId])
  }

  // 人物相关操作
  async createCharacter(character) {
    const sql = `
      INSERT INTO characters (
        id, project_id, name, role, description, personality, background, relationships
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `
    const params = [
      character.id,
      character.projectId,
      character.name,
      character.role,
      character.description,
      character.personality,
      character.background,
      character.relationships
    ]
    
    await this.run(sql, params)
    return this.getCharacter(character.id)
  }

  async getCharacter(id) {
    const sql = 'SELECT * FROM characters WHERE id = ?'
    return await this.get(sql, [id])
  }

  async getProjectCharacters(projectId) {
    const sql = 'SELECT * FROM characters WHERE project_id = ? ORDER BY created_at'
    return await this.all(sql, [projectId])
  }

  async updateCharacter(id, updates) {
    const fields = Object.keys(updates).map(key => `${key} = ?`).join(', ')
    const values = Object.values(updates)
    const sql = `UPDATE characters SET ${fields}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`
    
    await this.run(sql, [...values, id])
    return this.getCharacter(id)
  }

  async deleteCharacter(id) {
    const sql = 'DELETE FROM characters WHERE id = ?'
    return await this.run(sql, [id])
  }

  // 大纲相关操作
  async saveOutline(outline) {
    const existingSql = 'SELECT id FROM outlines WHERE project_id = ?'
    const existing = await this.get(existingSql, [outline.projectId])
    
    if (existing) {
      const sql = `
        UPDATE outlines 
        SET summary = ?, world_setting = ?, main_conflict = ?, chapter_plan = ?, updated_at = CURRENT_TIMESTAMP
        WHERE project_id = ?
      `
      await this.run(sql, [
        outline.summary,
        outline.worldSetting,
        outline.mainConflict,
        outline.chapterPlan,
        outline.projectId
      ])
      return this.getOutline(outline.projectId)
    } else {
      const sql = `
        INSERT INTO outlines (id, project_id, summary, world_setting, main_conflict, chapter_plan)
        VALUES (?, ?, ?, ?, ?, ?)
      `
      await this.run(sql, [
        outline.id || Date.now().toString(),
        outline.projectId,
        outline.summary,
        outline.worldSetting,
        outline.mainConflict,
        outline.chapterPlan
      ])
      return this.getOutline(outline.projectId)
    }
  }

  async getOutline(projectId) {
    const sql = 'SELECT * FROM outlines WHERE project_id = ?'
    return await this.get(sql, [projectId])
  }

  // 设置相关操作
  async setSetting(key, value, type = 'string') {
    const sql = `
      INSERT OR REPLACE INTO settings (key, value, type, updated_at)
      VALUES (?, ?, ?, CURRENT_TIMESTAMP)
    `
    return await this.run(sql, [key, value, type])
  }

  async getSetting(key) {
    const sql = 'SELECT * FROM settings WHERE key = ?'
    return await this.get(sql, [key])
  }

  async getAllSettings() {
    const sql = 'SELECT * FROM settings'
    return await this.all(sql)
  }

  // 创作历史
  async addWritingHistory(history) {
    const sql = `
      INSERT INTO writing_history (
        id, project_id, chapter_id, action_type, content_before, content_after, ai_prompt
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `
    const params = [
      history.id || Date.now().toString(),
      history.projectId,
      history.chapterId,
      history.actionType,
      history.contentBefore,
      history.contentAfter,
      history.aiPrompt
    ]
    
    return await this.run(sql, params)
  }

  async getWritingHistory(projectId, limit = 50) {
    const sql = `
      SELECT * FROM writing_history 
      WHERE project_id = ? 
      ORDER BY created_at DESC 
      LIMIT ?
    `
    return await this.all(sql, [projectId, limit])
  }

  // 关闭数据库连接
  async close() {
    return new Promise((resolve) => {
      if (this.db) {
        this.db.close((err) => {
          if (err) {
            console.error('Error closing database:', err)
          }
          resolve()
        })
      } else {
        resolve()
      }
    })
  }
}

// 创建单例实例
const dbService = new DatabaseService()

export default dbService