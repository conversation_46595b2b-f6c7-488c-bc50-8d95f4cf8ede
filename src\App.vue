<template>
  <div class="app">
    <Sidebar @navigate="handleNavigation" />
    <div class="main-content">
      <DemoContent
        v-if="currentView === 'home'"
        @navigate="handleNavigation"
      />
      <CompleteNovelSettings
        v-else-if="currentView === 'settings'"
        :visible="true"
        @close="handleNavigation('home')"
        @settings-changed="onSettingsChanged"
      />
      <MiaoBiShengHua
        v-else-if="currentView === 'create'"
        @navigate="handleNavigation"
      />
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import Sidebar from './components/Sidebar.vue'
import DemoContent from './components/DemoContent.vue'
import CompleteNovelSettings from './components/CompleteNovelSettings.vue'
import MiaoBiShengHua from './components/MiaoBiShengHua.vue'

export default {
  name: 'App',
  components: {
    Sidebar,
    DemoContent,
    CompleteNovelSettings,
    MiaoBiShengHua
  },
  setup() {
    const currentView = ref('home')
    const isDarkMode = ref(localStorage.getItem('sidebar-dark-mode') !== 'false')

    // 处理导航
    const handleNavigation = (view) => {
      currentView.value = view
    }

    // 设置变更处理
    const onSettingsChanged = (settings) => {
      console.log('设置已更新:', settings)
      // 处理主题变更
      if (settings.theme && settings.theme.mode !== undefined) {
        const newDarkMode = settings.theme.mode === 'dark' ||
          (settings.theme.mode === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches)
        if (newDarkMode !== isDarkMode.value) {
          isDarkMode.value = newDarkMode
          localStorage.setItem('sidebar-dark-mode', isDarkMode.value.toString())
        }
      }
    }

    return {
      currentView,
      isDarkMode,
      handleNavigation,
      onSettingsChanged
    }
  }
}
</script>

<style scoped>
.app {
  display: flex;
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
}

.app::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
  pointer-events: none;
}

.main-content {
  flex: 1;
  background: rgba(245, 245, 245, 0.9);
  backdrop-filter: blur(10px);
  overflow: hidden;
  height: 100vh;
  position: relative;
  z-index: 1;
}


</style>
