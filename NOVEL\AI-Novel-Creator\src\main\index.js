const { app, BrowserWindow, ipcMain } = require('electron')
const path = require('path')
const AIProxy = require('./aiProxy')
const isDev = process.env.NODE_ENV === 'development'

let mainWindow

// 检查Vite服务器是否可用
async function checkViteServer() {
  const http = require('http')

  return new Promise((resolve) => {
    const req = http.get('http://localhost:3000', (res) => {
      resolve(true)
    })
    req.on('error', () => {
      resolve(false)
    })
    req.setTimeout(2000, () => {
      req.destroy()
      resolve(false)
    })
  })
}

// 等待Vite服务器启动的函数
async function waitForViteServer(maxAttempts = 30) {
  const http = require('http')

  for (let i = 0; i < maxAttempts; i++) {
    try {
      await new Promise((resolve, reject) => {
        const req = http.get('http://localhost:3000', (res) => {
          resolve()
        })
        req.on('error', reject)
        req.setTimeout(1000, () => {
          req.destroy()
          reject(new Error('Timeout'))
        })
      })
      return true
    } catch (error) {
      console.log(`等待Vite服务器... (${i + 1}/${maxAttempts})`)
      await new Promise(resolve => setTimeout(resolve, 1000))
    }
  }
  throw new Error('Vite服务器启动超时')
}

async function createWindow() {
  // 创建浏览器窗口
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false, // 禁用Node.js集成提高安全性
      contextIsolation: true, // 启用上下文隔离
      enableRemoteModule: false,
      webSecurity: process.env.NODE_ENV === 'production', // 生产环境启用web安全，开发环境禁用
      preload: path.join(__dirname, 'preload.js'), // 添加preload脚本
      allowRunningInsecureContent: false,
      experimentalFeatures: true
    },
    icon: path.join(__dirname, '../../public/icon.png'),
    show: false,
    titleBarStyle: 'default',
    title: 'AI小说创作助手',
    autoHideMenuBar: true  // 自动隐藏菜单栏
  })

  // 隐藏菜单栏
  mainWindow.setMenuBarVisibility(false)

  // 加载应用
  if (isDev) {
    console.log('Dev mode: Try Vite server first, fallback to build version')

    // 首先尝试Vite服务器
    const viteServerAvailable = await checkViteServer()

    if (viteServerAvailable) {
      console.log('Vite server available, loading dev version')
      mainWindow.loadURL('http://localhost:3000')
    } else {
      console.log('Vite server unavailable, loading build version')
      // 使用构建后的文件
      const distPath = path.join(__dirname, '../../dist/renderer/index.html')
      console.log('Loading build file:', distPath)
      mainWindow.loadFile(distPath)
    }

    // 打开开发者工具
    mainWindow.webContents.openDevTools()
  } else {
    // 生产模式加载打包后的文件
    mainWindow.loadFile(path.join(__dirname, '../../dist/renderer/index.html'))
  }

  // 窗口准备就绪时显示
  mainWindow.once('ready-to-show', () => {
    console.log('Window ready, showing window')
    mainWindow.show()
  })

  // 添加刷新保护机制
  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
    console.log('页面加载失败:', errorCode, errorDescription, validatedURL)

    // 如果是路由相关的错误，重新加载主页
    if (errorCode === -6 && validatedURL.includes('file:///')) {
      console.log('检测到路由错误，重新加载主页')
      if (isDev) {
        const viteServerAvailable = checkViteServer()
        if (viteServerAvailable) {
          mainWindow.loadURL('http://localhost:3000')
        } else {
          mainWindow.loadFile(path.join(__dirname, '../../dist/renderer/index.html'))
        }
      } else {
        mainWindow.loadFile(path.join(__dirname, '../../dist/renderer/index.html'))
      }
    }
  })

  // 监听页面加载完成
  mainWindow.webContents.on('did-finish-load', () => {
    console.log('Page loaded successfully')
  })

  // 添加键盘快捷键处理
  mainWindow.webContents.on('before-input-event', (event, input) => {
    // 拦截Ctrl+R和F5刷新
    if ((input.control && input.key.toLowerCase() === 'r') || input.key === 'F5') {
      console.log('拦截刷新操作，重新加载应用')
      event.preventDefault()

      // 安全地重新加载
      if (isDev) {
        checkViteServer().then(available => {
          if (available) {
            mainWindow.loadURL('http://localhost:3000')
          } else {
            mainWindow.loadFile(path.join(__dirname, '../../dist/renderer/index.html'))
          }
        })
      } else {
        mainWindow.loadFile(path.join(__dirname, '../../dist/renderer/index.html'))
      }
    }
  })

  // 页面加载完成事件
  mainWindow.webContents.once('dom-ready', () => {
    console.log('DOM ready')
  })

  // 如果加载失败，也要显示窗口
  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
    console.log('Page load failed:', errorCode, errorDescription, validatedURL)
    mainWindow.show()
  })



  // 监听控制台消息
  mainWindow.webContents.on('console-message', (event, level, message, line, sourceId) => {
    console.log(`Renderer Console [${level}]:`, message)
  })

  // 当窗口关闭时触发
  mainWindow.on('closed', () => {
    mainWindow = null
  })
}

// 当 Electron 完成初始化并准备创建浏览器窗口时调用此方法
app.whenReady().then(() => {
  createWindow()

  // 初始化AI代理
  new AIProxy()
})

// 当所有窗口关闭时退出应用
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow()
  }
})

// IPC 通信处理
ipcMain.handle('get-app-version', () => {
  return app.getVersion()
})

ipcMain.handle('minimize-window', () => {
  mainWindow.minimize()
})

ipcMain.handle('maximize-window', () => {
  if (mainWindow.isMaximized()) {
    mainWindow.unmaximize()
  } else {
    mainWindow.maximize()
  }
})

ipcMain.handle('close-window', () => {
  mainWindow.close()
})
