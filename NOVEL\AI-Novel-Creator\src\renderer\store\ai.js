import { defineStore } from 'pinia'
import geminiService from '../../api/gemini'
import aiCreationService from '../services/aiCreationService'

export const useAIStore = defineStore('ai', {
  state: () => ({
    // API状态
    isInitialized: false,
    isConnected: false,
    apiKey: '',
    
    // 生成状态
    isGenerating: false,
    currentTask: '',
    progress: 0,
    
    // 生成结果
    lastGeneratedContent: null,
    generationHistory: [],
    
    // 错误信息
    error: null,
    
    // 配置
    config: {
      model: 'gemini-1.5-flash',
      temperature: 0.7,
      maxTokens: 2048,
      topP: 0.9
    }
  }),

  getters: {
    isReady: (state) => state.isInitialized && state.isConnected,
    canGenerate: (state) => state.isReady && !state.isGenerating,
    hasError: (state) => !!state.error
  },

  actions: {
    // 初始化AI服务
    async initializeAI(apiKey) {
      try {
        this.setLoading('正在初始化AI服务...')
        
        const result = await geminiService.initialize(apiKey)
        
        if (result.success) {
          this.isInitialized = true
          this.apiKey = apiKey
          
          // 测试连接
          const connected = await geminiService.testConnection()
          this.isConnected = connected
          
          if (connected) {
            this.clearError()
            this.stopLoading()
            return { success: true, message: 'AI服务初始化成功' }
          } else {
            this.setError('AI服务连接测试失败')
            return { success: false, message: 'AI服务连接测试失败' }
          }
        } else {
          this.setError(result.message)
          return result
        }
      } catch (error) {
        this.setError(`初始化失败: ${error.message}`)
        return { success: false, message: error.message }
      } finally {
        this.stopLoading()
      }
    },

    // 生成小说大纲
    async generateOutline(config) {
      if (!this.canGenerate) {
        throw new Error('AI服务未就绪或正在生成中')
      }

      try {
        this.setLoading('正在生成小说大纲...')
        this.setProgress(10)

        // 使用新的AI创作服务，支持用户设置的章节字数和动态token
        const params = {
          title: config.title,
          genre: config.genre,
          category: config.category,
          requirements: config.background || '',
          chapterCount: Math.min(config.totalChapters, 10), // 大纲只生成前10章
          wordsPerChapter: config.wordsPerChapter || 3000
        }

        const result = await aiCreationService.generateOutline(params, (progress) => {
          // 实时更新进度显示
          this.setProgress(Math.min(10 + (progress.length / 100), 80))
        })
        
        if (result) {
          this.setProgress(100)

          // 直接使用返回的结果，因为新服务已经格式化好了
          const outline = {
            title: config.title,
            content: result,
            wordCount: result.length,
            chapterCount: params.chapterCount,
            wordsPerChapter: params.wordsPerChapter
          }

          this.setLastGenerated('outline', outline)
          this.addToHistory('生成大纲', config.title, result)

          return { success: true, data: outline }
        } else {
          this.setError('大纲生成失败')
          return { success: false, message: '大纲生成失败' }
        }
      } catch (error) {
        this.setError(`生成大纲失败: ${error.message}`)
        return { success: false, message: error.message }
      } finally {
        this.stopLoading()
        this.setProgress(0)
      }
    },

    // 生成章节内容
    async generateChapter(chapterInfo, novelContext) {
      if (!this.canGenerate) {
        throw new Error('AI服务未就绪或正在生成中')
      }

      try {
        this.setLoading(`正在生成${chapterInfo.title}...`)
        this.setProgress(20)

        const result = await geminiService.generateChapterContent(chapterInfo, novelContext)
        
        if (result.success) {
          this.setProgress(100)
          this.setLastGenerated('chapter', {
            title: chapterInfo.title,
            content: result.text,
            wordCount: result.text.replace(/\s/g, '').length
          })
          this.addToHistory('生成章节', chapterInfo.title, result.text)
          
          return { success: true, data: result.text }
        } else {
          this.setError(result.error)
          return { success: false, message: result.error }
        }
      } catch (error) {
        this.setError(`生成章节失败: ${error.message}`)
        return { success: false, message: error.message }
      } finally {
        this.stopLoading()
        this.setProgress(0)
      }
    },

    // 文本续写
    async continueWriting(selectedText, context) {
      if (!this.canGenerate) {
        throw new Error('AI服务未就绪或正在生成中')
      }

      try {
        this.setLoading('正在续写文本...')
        
        const result = await geminiService.continueWriting(selectedText, context)
        
        if (result.success) {
          this.setLastGenerated('continue', result.text)
          this.addToHistory('文本续写', '续写', result.text)
          
          return { success: true, data: result.text }
        } else {
          this.setError(result.error)
          return { success: false, message: result.error }
        }
      } catch (error) {
        this.setError(`续写失败: ${error.message}`)
        return { success: false, message: error.message }
      } finally {
        this.stopLoading()
      }
    },

    // 文笔优化
    async optimizeText(text, style = '流畅自然') {
      if (!this.canGenerate) {
        throw new Error('AI服务未就绪或正在生成中')
      }

      try {
        this.setLoading('正在优化文笔...')
        
        const result = await geminiService.optimizeText(text, style)
        
        if (result.success) {
          this.setLastGenerated('optimize', result.text)
          this.addToHistory('文笔优化', style, result.text)
          
          return { success: true, data: result.text }
        } else {
          this.setError(result.error)
          return { success: false, message: result.error }
        }
      } catch (error) {
        this.setError(`优化失败: ${error.message}`)
        return { success: false, message: error.message }
      } finally {
        this.stopLoading()
      }
    },

    // 获取情节建议
    async getPlotSuggestions(currentPlot, novelContext) {
      if (!this.canGenerate) {
        throw new Error('AI服务未就绪或正在生成中')
      }

      try {
        this.setLoading('正在获取情节建议...')
        
        const result = await geminiService.getPlotSuggestions(currentPlot, novelContext)
        
        if (result.success) {
          const suggestions = this.parsePlotSuggestions(result.text)
          this.setLastGenerated('plot', suggestions)
          this.addToHistory('情节建议', '获取建议', result.text)
          
          return { success: true, data: suggestions }
        } else {
          this.setError(result.error)
          return { success: false, message: result.error }
        }
      } catch (error) {
        this.setError(`获取建议失败: ${error.message}`)
        return { success: false, message: error.message }
      } finally {
        this.stopLoading()
      }
    },

    // 对话优化
    async optimizeDialogue(dialogue, characterInfo) {
      if (!this.canGenerate) {
        throw new Error('AI服务未就绪或正在生成中')
      }

      try {
        this.setLoading('正在优化对话...')
        
        const result = await geminiService.optimizeDialogue(dialogue, characterInfo)
        
        if (result.success) {
          this.setLastGenerated('dialogue', result.text)
          this.addToHistory('对话优化', '优化', result.text)
          
          return { success: true, data: result.text }
        } else {
          this.setError(result.error)
          return { success: false, message: result.error }
        }
      } catch (error) {
        this.setError(`对话优化失败: ${error.message}`)
        return { success: false, message: error.message }
      } finally {
        this.stopLoading()
      }
    },

    // 生成场景描写
    async generateScene(sceneInfo, style = '细腻生动') {
      if (!this.canGenerate) {
        throw new Error('AI服务未就绪或正在生成中')
      }

      try {
        this.setLoading('正在生成场景描写...')
        
        const result = await geminiService.generateSceneDescription(sceneInfo, style)
        
        if (result.success) {
          this.setLastGenerated('scene', result.text)
          this.addToHistory('场景描写', style, result.text)
          
          return { success: true, data: result.text }
        } else {
          this.setError(result.error)
          return { success: false, message: result.error }
        }
      } catch (error) {
        this.setError(`生成场景失败: ${error.message}`)
        return { success: false, message: error.message }
      } finally {
        this.stopLoading()
      }
    },

    // 批量生成章节
    async generateMultipleChapters(chapters, novelContext, onProgress) {
      if (!this.canGenerate) {
        throw new Error('AI服务未就绪或正在生成中')
      }

      const results = []
      const total = chapters.length

      try {
        this.setLoading('正在批量生成章节...')

        for (let i = 0; i < chapters.length; i++) {
          const chapter = chapters[i]
          const progress = Math.round(((i + 1) / total) * 100)
          
          this.setProgress(progress)
          this.currentTask = `正在生成第${chapter.chapterNumber}章...`
          
          // 调用进度回调
          if (onProgress) {
            onProgress(i + 1, total, chapter.title)
          }

          const result = await geminiService.generateChapterContent(chapter, {
            ...novelContext,
            previousChapter: results.length > 0 ? results[results.length - 1].content : null
          })

          if (result.success) {
            results.push({
              ...chapter,
              content: result.text,
              wordCount: result.text.replace(/\s/g, '').length
            })
          } else {
            throw new Error(`生成第${chapter.chapterNumber}章失败: ${result.error}`)
          }

          // 添加间隔避免API限制
          if (i < chapters.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 1000))
          }
        }

        this.addToHistory('批量生成', `${total}个章节`, `成功生成${results.length}个章节`)
        
        return { success: true, data: results }
      } catch (error) {
        this.setError(`批量生成失败: ${error.message}`)
        return { success: false, message: error.message, data: results }
      } finally {
        this.stopLoading()
        this.setProgress(0)
        this.currentTask = ''
      }
    },

    // 解析大纲内容
    parseOutlineContent(text) {
      console.log('开始解析大纲内容:', text)
      
      // 这里应该根据实际的AI返回格式进行解析
      // 简化版本，实际应该使用更复杂的解析逻辑
      const lines = text.split('\n').filter(line => line.trim())
      console.log('分割后的行数:', lines.length)
      
      const outline = {
        summary: this.extractSection(lines, '故事简介') || text.substring(0, 200) + '...',
        characters: this.extractCharacters(lines) || [],
        worldSetting: this.extractSection(lines, '世界观设定') || '现代都市背景',
        mainConflict: this.extractSection(lines, '核心冲突') || '主角面临重重挑战',
        chapters: this.extractChapters(lines) || []
      }
      
      console.log('解析后的大纲:', outline)
      
      // 如果没有解析到章节，生成默认章节
      if (outline.chapters.length === 0) {
        console.log('没有解析到章节，生成默认章节')
        outline.chapters = this.generateDefaultChapters(10) // 生成10个默认章节
      }
      
      return outline
    },
    
    // 生成默认章节
    generateDefaultChapters(count) {
      const chapters = []
      for (let i = 1; i <= count; i++) {
        chapters.push({
          number: i,
          title: `第${i}章：新的开始`,
          summary: `第${i}章的故事内容`
        })
      }
      return chapters
    },

    // 解析情节建议
    parsePlotSuggestions(text) {
      const lines = text.split('\n').filter(line => line.trim())
      const suggestions = []
      
      lines.forEach(line => {
        const match = line.match(/^\d+\.\s*(.+?)\s*-\s*(.+)/)
        if (match) {
          suggestions.push({
            title: match[1].trim(),
            description: match[2].trim()
          })
        }
      })
      
      return suggestions
    },

    // 提取文本段落
    extractSection(lines, sectionName) {
      const startIndex = lines.findIndex(line => line.includes(sectionName))
      if (startIndex === -1) return ''
      
      let content = ''
      for (let i = startIndex + 1; i < lines.length; i++) {
        const line = lines[i]
        if (line.match(/^\d+\./)) break
        content += line + '\n'
      }
      
      return content.trim()
    },

    // 提取人物信息
    extractCharacters(lines) {
      const characters = []
      const startIndex = lines.findIndex(line => line.includes('主要人物'))
      if (startIndex === -1) return characters
      
      for (let i = startIndex + 1; i < lines.length; i++) {
        const line = lines[i]
        if (line.includes('世界观设定')) break
        
        const match = line.match(/^(.+?)[:：]\s*(.+)/)
        if (match) {
          characters.push({
            name: match[1].trim(),
            description: match[2].trim()
          })
        }
      }
      
      return characters
    },

    // 提取章节规划
    extractChapters(lines) {
      const chapters = []
      const startIndex = lines.findIndex(line => line.includes('章节规划'))
      if (startIndex === -1) return chapters
      
      for (let i = startIndex + 1; i < lines.length; i++) {
        const line = lines[i]
        const match = line.match(/^第(\d+)章[：:]\s*(.+)/)
        if (match) {
          chapters.push({
            number: parseInt(match[1]),
            title: match[2].trim(),
            summary: ''
          })
        }
      }
      
      return chapters
    },

    // 设置状态方法
    setLoading(task) {
      this.isGenerating = true
      this.currentTask = task
    },

    stopLoading() {
      this.isGenerating = false
      this.currentTask = ''
    },

    setProgress(progress) {
      this.progress = Math.max(0, Math.min(100, progress))
    },

    setError(error) {
      this.error = error
    },

    clearError() {
      this.error = null
    },

    setLastGenerated(type, content) {
      this.lastGeneratedContent = {
        type,
        content,
        timestamp: Date.now()
      }
    },

    addToHistory(action, target, content) {
      this.generationHistory.unshift({
        id: Date.now(),
        action,
        target,
        content: content.substring(0, 200) + (content.length > 200 ? '...' : ''),
        timestamp: Date.now()
      })
      
      // 保持历史记录不超过50条
      if (this.generationHistory.length > 50) {
        this.generationHistory = this.generationHistory.slice(0, 50)
      }
    },

    // 清除历史记录
    clearHistory() {
      this.generationHistory = []
    },

    // 重置AI状态
    reset() {
      this.isInitialized = false
      this.isConnected = false
      this.apiKey = ''
      this.clearError()
      this.stopLoading()
      this.setProgress(0)
      this.lastGeneratedContent = null
    }
  }
})