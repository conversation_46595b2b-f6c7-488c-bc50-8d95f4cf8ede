<template>
  <div class="settings-overlay" v-if="visible" @click="handleOverlayClick">
    <div class="settings-panel">
      <div class="settings-header">
        <h1>文房设置</h1>
        <button @click="$emit('close')">×</button>
      </div>
      <div class="settings-content">
        <p>这是一个简化的设置面板测试</p>
        <button @click="testFunction">测试按钮</button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SimpleSettings',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  emits: ['close'],
  setup(props, { emit }) {
    const handleOverlayClick = (event) => {
      if (event.target.classList.contains('settings-overlay')) {
        emit('close')
      }
    }

    const testFunction = () => {
      alert('设置面板工作正常！')
    }

    return {
      handleOverlayClick,
      testFunction
    }
  }
}
</script>

<style scoped>
.settings-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.settings-panel {
  width: 500px;
  height: 400px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
}

.settings-header {
  padding: 20px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.settings-header h1 {
  margin: 0;
  font-size: 20px;
}

.settings-header button {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
}

.settings-header button:hover {
  background: #f0f0f0;
}

.settings-content {
  flex: 1;
  padding: 20px;
}

.settings-content button {
  background: #4a90e2;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
}

.settings-content button:hover {
  background: #357abd;
}
</style>
