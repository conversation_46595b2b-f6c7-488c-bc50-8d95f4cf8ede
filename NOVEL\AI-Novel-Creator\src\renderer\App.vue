<template>
  <div id="app" class="ink-app">
    <!-- 水墨书香主容器 -->
    <div class="app-container">
      <!-- 水墨风格侧边栏 -->
      <InkSidebar />

      <!-- 主内容区域 -->
      <div class="main-container">
        <!-- 顶部导航栏 -->
        <header class="app-header">
          <div class="header-content">
            <!-- 面包屑导航 -->
            <div class="breadcrumb-section">
              <nav class="ink-breadcrumb">
                <span class="breadcrumb-item home">
                  <el-icon><HomeFilled /></el-icon>
                  文轩
                </span>
                <span class="breadcrumb-separator">·</span>
                <span class="breadcrumb-item current">
                  {{ getPageTitle() }}
                </span>
              </nav>
            </div>

            <!-- 右侧工具栏 -->
            <div class="header-actions">
              <!-- 时间显示 -->
              <div class="time-display">
                <span class="time-text">{{ currentTime }}</span>
              </div>

              <!-- 快捷操作 -->
              <div class="quick-actions">
                <button class="action-btn" @click="showAbout" title="关于应用">
                  <el-icon><InfoFilled /></el-icon>
                </button>

                <button class="action-btn" @click="toggleTheme" title="切换主题">
                  <el-icon><Sunny /></el-icon>
                </button>
              </div>
            </div>
          </div>

          <!-- 装饰性边框 -->
          <div class="header-decoration">
            <div class="ink-line"></div>
          </div>
        </header>

        <!-- 主要内容区 -->
        <main class="app-main">
          <div class="content-wrapper">
            <!-- 页面内容 -->
            <div class="page-content">
              <!-- 直接显示SimpleDashboard，绕过路由问题 -->
              <SimpleDashboard v-if="showFallback" />

              <!-- 正常的路由视图 -->
              <router-view v-else v-slot="{ Component, route }">
                <transition name="page-fade" mode="out-in">
                  <component :is="Component" v-if="Component" :key="route.path" />
                  <!-- 如果没有组件，显示SimpleDashboard -->
                  <SimpleDashboard v-else />
                </transition>
              </router-view>
            </div>
          </div>
        </main>
      </div>
    </div>

    <!-- 背景装饰元素 -->
    <div class="app-decoration">
      <!-- 水墨渲染背景 -->
      <div class="ink-background">
        <div class="ink-cloud top-left"></div>
        <div class="ink-cloud top-right"></div>
        <div class="ink-cloud bottom-left"></div>
        <div class="ink-cloud bottom-right"></div>
      </div>

      <!-- 传统纹样 -->
      <div class="traditional-pattern">
        <div class="pattern-corner top-left"></div>
        <div class="pattern-corner top-right"></div>
        <div class="pattern-corner bottom-left"></div>
        <div class="pattern-corner bottom-right"></div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import InkSidebar from '@/components/InkSidebar.vue'
import SimpleDashboard from '@/views/SimpleDashboard.vue'

export default {
  name: 'App',
  components: {
    InkSidebar,
    SimpleDashboard
  },
  setup() {
    const route = useRoute()
    const currentTime = ref('')
    const showFallback = ref(false)
    let timeInterval = null

    // 调试路由状态
    console.log('App component initialized, current route:', route.path)

    // 3秒后如果还没有正确的路由，显示备用内容
    setTimeout(() => {
      if (!route.path || route.path === '/' || route.matched.length === 0) {
        console.log('Route initialization timeout, showing fallback content')
        showFallback.value = true
      }
    }, 3000)

    // 页面标题映射
    const pageTitles = {
      '/dashboard': '文房四宝',
      '/projects': '书案管理',
      '/auto-create': '妙笔生花',
      '/editor': '挥毫泼墨',
      '/settings': '文房设置'
    }

    // 获取页面标题
    const getPageTitle = () => {
      return pageTitles[route.path] || route.meta?.title || '墨韵文轩'
    }

    // 更新时间
    const updateTime = () => {
      const now = new Date()
      const timeStr = now.toLocaleTimeString('zh-CN', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit'
      })
      const dateStr = now.toLocaleDateString('zh-CN', {
        month: 'long',
        day: 'numeric'
      })
      currentTime.value = `${dateStr} ${timeStr}`
    }

    // 显示关于信息
    const showAbout = async () => {
      try {
        await ElMessageBox.alert(
          `
          <div style="text-align: center; padding: 20px;">
            <h3 style="color: var(--huang-jin); font-family: var(--font-calligraphy); margin-bottom: 16px;">
              墨韵文轩 · AI创作助手
            </h3>
            <p style="color: var(--text-secondary); margin-bottom: 12px;">版本 v1.0.0</p>
            <p style="color: var(--text-muted); font-size: 14px; line-height: 1.6;">
              以传统文化之美，融现代科技之智<br/>
              让每一次创作都如挥毫泼墨般酣畅淋漓
            </p>
          </div>
          `,
          '关于应用',
          {
            dangerouslyUseHTMLString: true,
            confirmButtonText: '知晓',
            customClass: 'ink-message-box'
          }
        )
      } catch (error) {
        // 用户取消
      }
    }

    // 切换主题（预留功能）
    const toggleTheme = () => {
      ElMessage({
        message: '主题切换功能开发中...',
        type: 'info',
        duration: 2000
      })
    }

    // 生命周期
    onMounted(() => {
      updateTime()
      timeInterval = setInterval(updateTime, 60000) // 每分钟更新一次
    })

    onUnmounted(() => {
      if (timeInterval) {
        clearInterval(timeInterval)
      }
    })

    return {
      currentTime,
      showFallback,
      getPageTitle,
      showAbout,
      toggleTheme
    }
  }
}
</script>

<style>
/* 引入水墨书香主题 */
@import '@/assets/styles/modern-theme.css';

/* === 应用主容器样式 === */
.ink-app {
  font-family: var(--font-elegant);
  height: 100vh;
  overflow: hidden;
  position: relative;
  background: var(--bg-secondary);
}

.app-container {
  display: flex;
  height: 100vh;
  position: relative;
  z-index: 10;
  width: 100%;
}

.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-width: 0;  /* 确保可以收缩 */
}

/* === 顶部导航栏 === */
.app-header {
  background: var(--bg-primary);
  border-bottom: 2px solid var(--border-light);
  position: relative;
  z-index: 20;
  box-shadow: var(--shadow-paper-sm);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg) var(--spacing-xl);
  min-height: 72px;
}

/* 面包屑导航 */
.breadcrumb-section {
  flex: 1;
}

.ink-breadcrumb {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  font-family: var(--font-calligraphy);
  font-size: 16px;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--text-secondary);
  transition: color var(--duration-fast) var(--ease-paper);
}

.breadcrumb-item.home {
  color: var(--text-muted);
  font-size: 14px;
}

.breadcrumb-item.current {
  color: var(--text-primary);
  font-weight: var(--font-weight-semibold);
  font-size: 18px;
}

.breadcrumb-separator {
  color: var(--text-placeholder);
  font-size: 14px;
  margin: 0 var(--spacing-xs);
}

/* 右侧工具栏 */
.header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.time-display {
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-inset-light);
}

.time-text {
  font-family: var(--font-elegant);
  font-size: 13px;
  color: var(--text-muted);
  font-weight: var(--font-weight-medium);
  letter-spacing: 0.02em;
}

.quick-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.action-btn {
  width: 40px;
  height: 40px;
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-paper);
  color: var(--text-muted);
  position: relative;
  overflow: hidden;
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-gold);
  opacity: 0;
  transition: opacity var(--duration-normal) var(--ease-paper);
  border-radius: inherit;
}

.action-btn:hover::before {
  opacity: 0.1;
}

.action-btn:hover {
  border-color: var(--huang-jin);
  color: var(--huang-jin);
  transform: translateY(-2px);
  box-shadow: var(--shadow-paper-md);
}

/* 装饰性边框 */
.header-decoration {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  overflow: hidden;
}

.ink-line {
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    var(--huang-jin) 20%,
    var(--song-lv) 40%,
    var(--huang-jin) 60%,
    var(--song-lv) 80%,
    transparent 100%
  );
  opacity: 0.6;
  animation: inkFlow 8s ease infinite;
}

/* === 主要内容区 === */
.app-main {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.content-wrapper {
  height: 100%;
  overflow-y: auto;
  padding: var(--spacing-xl);
  position: relative;
}

.page-content {
  max-width: 1400px;
  margin: 0 auto;
  position: relative;
  z-index: 10;
}

/* === 页面切换动画 === */
.page-fade-enter-active,
.page-fade-leave-active {
  transition: all var(--duration-normal) var(--ease-paper);
}

.page-fade-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.page-fade-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

/* === 默认加载内容 === */
.default-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-placeholder {
  text-align: center;
  padding: var(--spacing-2xl);
  background: var(--bg-primary);
  border-radius: var(--radius-2xl);
  border: 2px solid var(--border-light);
  box-shadow: var(--shadow-paper-lg);
  max-width: 400px;
}

.ink-logo {
  width: 80px;
  height: 80px;
  background: var(--gradient-gold);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--spacing-lg);
  font-family: var(--font-calligraphy);
  font-size: 32px;
  font-weight: bold;
  color: var(--ink-jiao);
  box-shadow: var(--shadow-paper-md);
}

.loading-placeholder h2 {
  color: var(--text-primary);
  font-family: var(--font-calligraphy);
  margin-bottom: var(--spacing-md);
  font-size: 24px;
}

.loading-placeholder p {
  color: var(--text-muted);
  font-size: 14px;
  line-height: 1.6;
}

/* === 背景装饰元素 === */
.app-decoration {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

/* 水墨渲染背景 */
.ink-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.ink-cloud {
  position: absolute;
  width: 300px;
  height: 300px;
  border-radius: 50%;
  opacity: 0.02;
  animation: paperFloat 20s ease-in-out infinite;
}

.ink-cloud.top-left {
  top: -150px;
  left: -150px;
  background: radial-gradient(circle, var(--ink-nong) 0%, transparent 70%);
  animation-delay: 0s;
}

.ink-cloud.top-right {
  top: -100px;
  right: -100px;
  background: radial-gradient(circle, var(--song-lv) 0%, transparent 70%);
  animation-delay: 5s;
}

.ink-cloud.bottom-left {
  bottom: -120px;
  left: -120px;
  background: radial-gradient(circle, var(--huang-jin) 0%, transparent 70%);
  animation-delay: 10s;
}

.ink-cloud.bottom-right {
  bottom: -80px;
  right: -80px;
  background: radial-gradient(circle, var(--ink-zhong) 0%, transparent 70%);
  animation-delay: 15s;
}

/* 传统纹样 */
.traditional-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.pattern-corner {
  position: absolute;
  width: 60px;
  height: 60px;
  opacity: 0.05;
  background-image:
    radial-gradient(circle at 50% 50%, var(--huang-jin) 1px, transparent 1px),
    radial-gradient(circle at 25% 25%, var(--song-lv) 0.5px, transparent 0.5px);
  background-size: 8px 8px, 4px 4px;
}

.pattern-corner.top-left {
  top: 20px;
  left: 20px;
  border-radius: 0 0 var(--radius-2xl) 0;
}

.pattern-corner.top-right {
  top: 20px;
  right: 20px;
  border-radius: 0 0 0 var(--radius-2xl);
}

.pattern-corner.bottom-left {
  bottom: 20px;
  left: 20px;
  border-radius: 0 var(--radius-2xl) 0 0;
}

.pattern-corner.bottom-right {
  bottom: 20px;
  right: 20px;
  border-radius: var(--radius-2xl) 0 0 0;
}

/* === 响应式设计 === */
@media (max-width: 768px) {
  .app-container {
    flex-direction: row;  /* 确保水平布局 */
  }

  .header-content {
    padding: var(--spacing-md) var(--spacing-lg);
    min-height: 60px;
  }

  .breadcrumb-item.current {
    font-size: 16px;
  }

  .content-wrapper {
    padding: var(--spacing-lg);
  }

  .time-display {
    display: none;
  }
}

/* 确保在大屏幕上布局正确 */
@media (min-width: 769px) {
  .app-container {
    display: flex;
    flex-direction: row;
  }
}

/* === 自定义消息框样式 === */
:deep(.ink-message-box) {
  border-radius: var(--radius-2xl);
  border: 2px solid var(--border-light);
  box-shadow: var(--shadow-paper-2xl);
}

:deep(.ink-message-box .el-message-box__header) {
  background: var(--gradient-paper);
  border-bottom: 1px solid var(--border-light);
  border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
}

:deep(.ink-message-box .el-message-box__title) {
  font-family: var(--font-calligraphy);
  color: var(--text-primary);
}

:deep(.ink-message-box .el-button--primary) {
  background: var(--gradient-gold);
  border-color: var(--huang-jin);
  color: var(--ink-jiao);
}
</style>