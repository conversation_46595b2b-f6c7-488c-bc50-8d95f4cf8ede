<template>
  <div class="ink-settings" v-if="visible">
    <!-- 返回按钮 -->
    <div class="back-header">
      <button class="back-btn" @click="$emit('close')" title="返回">
        <svg viewBox="0 0 24 24" fill="none">
          <path d="M19 12H5M12 19l-7-7 7-7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </button>
    </div>

    <!-- 页面标题 -->
    <div class="page-header elegant-card fade-in-up">
      <div class="header-content">
        <h1 class="page-title gradient-text elegant">文房设置</h1>
        <p class="page-subtitle">配置您的创作环境</p>
      </div>
      <div class="header-decoration floating">
        <div class="decoration-icon">
          <svg viewBox="0 0 24 24" fill="none">
            <path d="M12 15l-2-5h4l-2 5zM12 6v3" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
      </div>
    </div>

    <!-- 设置内容 -->
    <div class="settings-content">
      <!-- AI配置 -->
      <div class="settings-section elegant-card fade-in-up">
        <div class="section-header">
          <h2 class="section-title gradient-text purple">AI服务配置</h2>
          <p class="section-subtitle">配置AI服务以启用智能创作功能</p>
        </div>
        
        <div class="settings-form">
          <!-- AI服务商选择 -->
          <div class="form-group">
            <label class="form-label">AI服务商</label>
            <select v-model="aiSettings.provider" @change="onProviderChange" class="form-select">
              <option value="">选择AI服务提供商</option>
              <option value="openai">🤖 OpenAI - GPT系列模型</option>
              <option value="gemini">🌟 Google Gemini - 多模态AI</option>
              <option value="claude">🧠 Anthropic Claude - 对话AI</option>
              <option value="custom">⚙️ 自定义 - 兼容OpenAI API</option>
            </select>
          </div>

          <!-- API配置 -->
          <div v-if="aiSettings.provider" class="api-config">
            <div class="form-group">
              <label class="form-label">API地址</label>
              <input 
                v-model="aiSettings.apiUrl" 
                type="text" 
                class="form-input"
                :placeholder="getApiUrlPlaceholder()"
              />
              <div class="form-hint">
                <button type="button" class="hint-btn" @click="useDefaultUrl">
                  使用默认地址
                </button>
              </div>
            </div>

            <div class="form-group">
              <label class="form-label">API密钥</label>
              <div class="api-key-input">
                <input 
                  v-model="aiSettings.apiKey" 
                  :type="showApiKey ? 'text' : 'password'"
                  class="form-input"
                  placeholder="请输入您的API Key"
                />
                <button 
                  type="button" 
                  class="toggle-btn"
                  @click="showApiKey = !showApiKey"
                >
                  <svg v-if="showApiKey" viewBox="0 0 24 24" fill="none">
                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" stroke="currentColor" stroke-width="2"/>
                    <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                  </svg>
                  <svg v-else viewBox="0 0 24 24" fill="none">
                    <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24" stroke="currentColor" stroke-width="2"/>
                    <line x1="1" y1="1" x2="23" y2="23" stroke="currentColor" stroke-width="2"/>
                  </svg>
                </button>
              </div>
              <div class="form-hint">
                <span>需要API Key？</span>
                <button type="button" class="hint-btn" @click="openApiKeyHelp">
                  获取帮助
                </button>
              </div>
            </div>

            <div class="form-group">
              <label class="form-label">模型选择</label>
              <select v-model="aiSettings.model" class="form-select">
                <option value="">选择模型</option>
                <optgroup label="推荐模型">
                  <option 
                    v-for="model in getRecommendedModels()" 
                    :key="model.id" 
                    :value="model.id"
                  >
                    {{ model.name }} - {{ model.description }}
                  </option>
                </optgroup>
              </select>
            </div>

            <!-- 测试连接 -->
            <div class="form-actions">
              <button 
                type="button" 
                class="btn btn-test" 
                @click="testConnection"
                :disabled="!aiSettings.apiKey || !aiSettings.apiUrl"
              >
                <svg viewBox="0 0 24 24" fill="none">
                  <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" stroke="currentColor" stroke-width="2"/>
                  <polyline points="22,4 12,14.01 9,11.01" stroke="currentColor" stroke-width="2"/>
                </svg>
                测试连接
              </button>
            </div>
          </div>

          <!-- 高级设置 -->
          <div v-if="aiSettings.provider" class="advanced-settings">
            <div class="divider">
              <span>高级设置</span>
            </div>

            <div class="form-group">
              <label class="form-label">温度参数 ({{ advancedSettings.temperature }})</label>
              <input 
                type="range" 
                v-model="advancedSettings.temperature"
                min="0"
                max="2"
                step="0.1"
                class="form-range"
              />
              <div class="form-hint">控制AI回复的创造性，0为最保守，2为最创新</div>
            </div>

            <div class="form-group">
              <label class="form-label">请求超时 ({{ advancedSettings.timeout }}秒)</label>
              <input 
                type="range" 
                v-model="advancedSettings.timeout"
                min="5"
                max="120"
                step="5"
                class="form-range"
              />
            </div>

            <div class="form-group">
              <label class="checkbox-label">
                <input type="checkbox" v-model="advancedSettings.stream" />
                <span class="checkmark"></span>
                启用流式输出
                <span class="hint-text">实时显示AI生成内容</span>
              </label>
            </div>
          </div>
        </div>
      </div>

      <!-- 编辑器设置 -->
      <div class="settings-section elegant-card fade-in-up">
        <div class="section-header">
          <h2 class="section-title gradient-text blue">编辑器设置</h2>
          <p class="section-subtitle">个性化您的写作环境</p>
        </div>
        
        <div class="settings-form">
          <div class="form-group">
            <label class="form-label">字体大小 ({{ editorSettings.fontSize }}px)</label>
            <input 
              type="range" 
              v-model="editorSettings.fontSize"
              min="12"
              max="24"
              class="form-range"
            />
          </div>
          
          <div class="form-group">
            <label class="form-label">行间距 ({{ editorSettings.lineHeight }})</label>
            <input 
              type="range" 
              v-model="editorSettings.lineHeight"
              min="1.2"
              max="2.5"
              step="0.1"
              class="form-range"
            />
          </div>
          
          <div class="form-group">
            <label class="checkbox-label">
              <input type="checkbox" v-model="editorSettings.autoSave" />
              <span class="checkmark"></span>
              自动保存
              <span class="hint-text">开启后将自动保存您的创作内容</span>
            </label>
          </div>
          
          <div class="form-group">
            <label class="checkbox-label">
              <input type="checkbox" v-model="editorSettings.darkMode" />
              <span class="checkmark"></span>
              夜间模式
              <span class="hint-text">护眼的深色主题</span>
            </label>
          </div>
        </div>
      </div>

      <!-- 保存按钮 -->
      <div class="settings-footer">
        <button type="button" class="btn btn-primary" @click="saveAllSettings">
          <svg viewBox="0 0 24 24" fill="none">
            <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z" stroke="currentColor" stroke-width="2"/>
            <polyline points="17,21 17,13 7,13 7,21" stroke="currentColor" stroke-width="2"/>
            <polyline points="7,3 7,8 15,8" stroke="currentColor" stroke-width="2"/>
          </svg>
          保存所有设置
        </button>
        <button type="button" class="btn btn-secondary" @click="resetAllSettings">
          重置默认
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, watch, computed } from 'vue'

export default {
  name: 'NovelSettingsPanel',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    isDarkMode: {
      type: Boolean,
      default: false
    }
  },
  emits: ['close', 'settings-changed'],
  setup(props, { emit }) {
    const showApiKey = ref(false)
    
    // AI设置
    const aiSettings = reactive({
      provider: '',
      apiUrl: '',
      apiKey: '',
      model: ''
    })

    // 高级设置
    const advancedSettings = reactive({
      temperature: 0.7,
      timeout: 30,
      maxRetries: 3,
      stream: true
    })

    // 编辑器设置
    const editorSettings = reactive({
      fontSize: 16,
      lineHeight: 1.6,
      autoSave: true,
      darkMode: false
    })

    // AI服务商配置
    const AI_PROVIDERS = {
      openai: {
        name: 'OpenAI',
        defaultUrl: 'https://api.openai.com/v1',
        models: [
          { id: 'gpt-4', name: 'GPT-4', description: '最强大的模型' },
          { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', description: '快速且经济' }
        ]
      },
      gemini: {
        name: 'Google Gemini',
        defaultUrl: 'https://generativelanguage.googleapis.com/v1',
        models: [
          { id: 'gemini-pro', name: 'Gemini Pro', description: '多模态AI模型' },
          { id: 'gemini-pro-vision', name: 'Gemini Pro Vision', description: '支持图像理解' }
        ]
      },
      claude: {
        name: 'Anthropic Claude',
        defaultUrl: 'https://api.anthropic.com/v1',
        models: [
          { id: 'claude-3-opus', name: 'Claude 3 Opus', description: '最强推理能力' },
          { id: 'claude-3-sonnet', name: 'Claude 3 Sonnet', description: '平衡性能' }
        ]
      },
      custom: {
        name: '自定义',
        defaultUrl: '',
        models: [
          { id: 'custom-model', name: '自定义模型', description: '兼容OpenAI API' }
        ]
      }
    }

    // 获取API URL占位符
    const getApiUrlPlaceholder = () => {
      const provider = AI_PROVIDERS[aiSettings.provider]
      return provider ? provider.defaultUrl || '输入自定义API地址' : '请先选择AI服务商'
    }

    // 获取推荐模型
    const getRecommendedModels = () => {
      const provider = AI_PROVIDERS[aiSettings.provider]
      return provider ? provider.models : []
    }

    // 服务商变更处理
    const onProviderChange = () => {
      const provider = AI_PROVIDERS[aiSettings.provider]
      if (provider && provider.defaultUrl) {
        aiSettings.apiUrl = provider.defaultUrl
      }
      aiSettings.model = ''
    }

    // 使用默认URL
    const useDefaultUrl = () => {
      const provider = AI_PROVIDERS[aiSettings.provider]
      if (provider && provider.defaultUrl) {
        aiSettings.apiUrl = provider.defaultUrl
      }
    }

    // 打开API Key帮助
    const openApiKeyHelp = () => {
      const helpUrls = {
        openai: 'https://platform.openai.com/api-keys',
        gemini: 'https://aistudio.google.com/app/apikey',
        claude: 'https://console.anthropic.com/',
        custom: 'https://github.com/topics/openai-api'
      }
      const url = helpUrls[aiSettings.provider]
      if (url) {
        window.open(url, '_blank')
      }
    }

    // 测试连接
    const testConnection = async () => {
      if (!aiSettings.apiKey || !aiSettings.apiUrl) {
        alert('请先填写API地址和密钥')
        return
      }
      
      try {
        // 这里应该实现实际的API测试逻辑
        alert('连接测试成功！')
      } catch (error) {
        alert('连接测试失败：' + error.message)
      }
    }

    // 保存所有设置
    const saveAllSettings = () => {
      const allSettings = {
        ai: aiSettings,
        advanced: advancedSettings,
        editor: editorSettings
      }
      localStorage.setItem('novel-settings', JSON.stringify(allSettings))
      emit('settings-changed', allSettings)
      alert('设置已保存')
    }

    // 重置所有设置
    const resetAllSettings = () => {
      if (confirm('确定要重置所有设置吗？')) {
        Object.assign(aiSettings, {
          provider: '',
          apiUrl: '',
          apiKey: '',
          model: ''
        })
        Object.assign(advancedSettings, {
          temperature: 0.7,
          timeout: 30,
          maxRetries: 3,
          stream: true
        })
        Object.assign(editorSettings, {
          fontSize: 16,
          lineHeight: 1.6,
          autoSave: true,
          darkMode: false
        })
      }
    }

    // 加载设置
    const loadSettings = () => {
      const saved = localStorage.getItem('novel-settings')
      if (saved) {
        try {
          const parsedSettings = JSON.parse(saved)
          if (parsedSettings.ai) Object.assign(aiSettings, parsedSettings.ai)
          if (parsedSettings.advanced) Object.assign(advancedSettings, parsedSettings.advanced)
          if (parsedSettings.editor) Object.assign(editorSettings, parsedSettings.editor)
        } catch (error) {
          console.error('加载设置失败:', error)
        }
      }
    }

    // 初始化
    loadSettings()

    return {
      showApiKey,
      aiSettings,
      advancedSettings,
      editorSettings,
      getApiUrlPlaceholder,
      getRecommendedModels,
      onProviderChange,
      useDefaultUrl,
      openApiKeyHelp,
      testConnection,
      saveAllSettings,
      resetAllSettings
    }
  }
}
</script>

<style scoped>
.ink-settings {
  height: 100%;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  overflow-y: auto;
  padding: 20px;
}

.back-header {
  margin-bottom: 20px;
}

.back-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.back-btn:hover {
  background: rgba(74, 144, 226, 0.1);
  color: #4a90e2;
  transform: translateX(-2px);
}

.back-btn svg {
  width: 20px;
  height: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 30px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 8px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-subtitle {
  font-size: 16px;
  color: #666;
  margin: 0;
}

.header-decoration {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.settings-content {
  max-width: 800px;
  margin: 0 auto;
}

.settings-section {
  margin-bottom: 30px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.section-header {
  margin-bottom: 25px;
  text-align: center;
}

.section-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.section-title.purple {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-title.blue {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-subtitle {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.settings-form {
  max-width: 600px;
  margin: 0 auto;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.form-select,
.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  transition: all 0.3s ease;
}

.form-select:focus,
.form-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  background: rgba(255, 255, 255, 0.95);
}

.api-config {
  background: rgba(102, 126, 234, 0.05);
  border-radius: 16px;
  padding: 20px;
  margin: 20px 0;
}

.api-key-input {
  display: flex;
  align-items: center;
}

.api-key-input .form-input {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: none;
}

.toggle-btn {
  padding: 12px;
  border: 2px solid rgba(0, 0, 0, 0.1);
  border-left: none;
  border-top-right-radius: 12px;
  border-bottom-right-radius: 12px;
  background: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.3s ease;
}

.toggle-btn:hover {
  background: rgba(0, 0, 0, 0.05);
}

.toggle-btn svg {
  width: 16px;
  height: 16px;
}

.form-hint {
  margin-top: 6px;
  font-size: 12px;
  color: #666;
  display: flex;
  align-items: center;
  gap: 8px;
}

.hint-btn {
  background: none;
  border: none;
  color: #667eea;
  cursor: pointer;
  text-decoration: underline;
  font-size: 12px;
}

.hint-btn:hover {
  color: #764ba2;
}

.form-range {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: #ddd;
  outline: none;
  -webkit-appearance: none;
}

.form-range::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.form-range::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  font-size: 14px;
  color: #333;
}

.checkbox-label input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(0, 0, 0, 0.3);
  border-radius: 6px;
  position: relative;
  transition: all 0.3s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
  content: '';
  position: absolute;
  left: 6px;
  top: 2px;
  width: 4px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.hint-text {
  font-size: 12px;
  color: #666;
  margin-left: auto;
}

.divider {
  text-align: center;
  margin: 25px 0;
  position: relative;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, #ddd, transparent);
}

.divider span {
  background: white;
  padding: 0 20px;
  color: #666;
  font-size: 14px;
  font-weight: 500;
}

.advanced-settings {
  background: rgba(116, 75, 162, 0.05);
  border-radius: 16px;
  padding: 20px;
  margin-top: 20px;
}

.form-actions {
  text-align: center;
  margin-top: 20px;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  margin: 0 8px;
}

.btn svg {
  width: 16px;
  height: 16px;
}

.btn-test {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
}

.btn-test:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(79, 172, 254, 0.4);
}

.btn-test:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
  background: rgba(0, 0, 0, 0.1);
  color: #666;
}

.btn-secondary:hover {
  background: rgba(0, 0, 0, 0.15);
}

.settings-footer {
  text-align: center;
  margin-top: 40px;
  padding-top: 30px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.elegant-card {
  position: relative;
  overflow: hidden;
}

.elegant-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #667eea, #764ba2, #4facfe, #00f2fe);
  background-size: 200% 100%;
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { background-position: 200% 0; }
  50% { background-position: -200% 0; }
}
</style>
