/**
 * 修改历史记录服务
 * 管理用户的所有文本修改操作，支持历史版本查看和回滚
 */

class ModificationHistoryService {
  constructor() {
    this.history = []
    this.currentIndex = -1
    this.maxHistorySize = 100 // 最多保存100个历史记录
    this.sessionId = this.generateSessionId()
    this.loadHistory()
  }

  /**
   * 生成会话ID
   */
  generateSessionId() {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 添加修改记录
   * @param {Object} modification 修改信息
   */
  addModification(modification) {
    const record = {
      id: this.generateId(),
      sessionId: this.sessionId,
      timestamp: new Date(),
      type: modification.type || 'text_edit',
      originalText: modification.originalText,
      modifiedText: modification.modifiedText,
      position: modification.position || null,
      question: modification.question || '',
      aiAnalysis: modification.aiAnalysis || '',
      suggestionType: modification.suggestionType || '',
      reason: modification.reason || '',
      context: modification.context || {},
      metadata: {
        textLength: modification.originalText?.length || 0,
        changeLength: (modification.modifiedText?.length || 0) - (modification.originalText?.length || 0),
        wordCount: this.countWords(modification.modifiedText || ''),
        category: this.categorizeModification(modification)
      }
    }

    // 如果当前不在历史记录的末尾，删除后续记录
    if (this.currentIndex < this.history.length - 1) {
      this.history = this.history.slice(0, this.currentIndex + 1)
    }

    // 添加新记录
    this.history.push(record)
    this.currentIndex = this.history.length - 1

    // 限制历史记录大小
    if (this.history.length > this.maxHistorySize) {
      this.history = this.history.slice(-this.maxHistorySize)
      this.currentIndex = this.history.length - 1
    }

    this.saveHistory()
    return record
  }

  /**
   * 生成唯一ID
   */
  generateId() {
    return `mod_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 统计词数
   */
  countWords(text) {
    if (!text) return 0
    // 中文按字符计算，英文按单词计算
    const chineseChars = (text.match(/[\u4e00-\u9fa5]/g) || []).length
    const englishWords = (text.match(/[a-zA-Z]+/g) || []).length
    return chineseChars + englishWords
  }

  /**
   * 分类修改类型
   */
  categorizeModification(modification) {
    const { originalText, modifiedText, suggestionType } = modification

    if (suggestionType) {
      return suggestionType
    }

    if (!originalText && modifiedText) {
      return '新增内容'
    }

    if (originalText && !modifiedText) {
      return '删除内容'
    }

    const originalLength = originalText?.length || 0
    const modifiedLength = modifiedText?.length || 0
    const lengthDiff = modifiedLength - originalLength

    if (Math.abs(lengthDiff) < originalLength * 0.1) {
      return '微调修改'
    } else if (lengthDiff > 0) {
      return '扩展内容'
    } else {
      return '精简内容'
    }
  }

  /**
   * 获取历史记录列表
   */
  getHistory(options = {}) {
    const {
      limit = 50,
      offset = 0,
      category = null,
      dateRange = null,
      searchText = null
    } = options

    let filteredHistory = [...this.history]

    // 按类别筛选
    if (category) {
      filteredHistory = filteredHistory.filter(record => 
        record.metadata.category === category
      )
    }

    // 按日期范围筛选
    if (dateRange && dateRange.start && dateRange.end) {
      filteredHistory = filteredHistory.filter(record => 
        record.timestamp >= dateRange.start && record.timestamp <= dateRange.end
      )
    }

    // 按文本搜索
    if (searchText) {
      const searchLower = searchText.toLowerCase()
      filteredHistory = filteredHistory.filter(record => 
        record.originalText?.toLowerCase().includes(searchLower) ||
        record.modifiedText?.toLowerCase().includes(searchLower) ||
        record.question?.toLowerCase().includes(searchLower)
      )
    }

    // 排序（最新的在前）
    filteredHistory.sort((a, b) => b.timestamp - a.timestamp)

    // 分页
    const total = filteredHistory.length
    const records = filteredHistory.slice(offset, offset + limit)

    return {
      records,
      total,
      hasMore: offset + limit < total,
      currentIndex: this.currentIndex,
      canUndo: this.canUndo(),
      canRedo: this.canRedo()
    }
  }

  /**
   * 获取统计信息
   */
  getStatistics() {
    const stats = {
      totalModifications: this.history.length,
      todayModifications: 0,
      weekModifications: 0,
      categories: {},
      averageChangeLength: 0,
      totalWordsAdded: 0,
      totalWordsRemoved: 0,
      mostActiveHour: null,
      modificationTrend: []
    }

    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)

    let totalChangeLength = 0
    const hourCounts = new Array(24).fill(0)

    this.history.forEach(record => {
      const recordDate = new Date(record.timestamp)
      
      // 今日修改
      if (recordDate >= today) {
        stats.todayModifications++
      }
      
      // 本周修改
      if (recordDate >= weekAgo) {
        stats.weekModifications++
      }

      // 分类统计
      const category = record.metadata.category
      stats.categories[category] = (stats.categories[category] || 0) + 1

      // 变更长度统计
      totalChangeLength += Math.abs(record.metadata.changeLength)
      
      if (record.metadata.changeLength > 0) {
        stats.totalWordsAdded += record.metadata.changeLength
      } else {
        stats.totalWordsRemoved += Math.abs(record.metadata.changeLength)
      }

      // 活跃时间统计
      hourCounts[recordDate.getHours()]++
    })

    stats.averageChangeLength = this.history.length > 0 
      ? Math.round(totalChangeLength / this.history.length) 
      : 0

    // 找出最活跃的小时
    const maxHourIndex = hourCounts.indexOf(Math.max(...hourCounts))
    stats.mostActiveHour = maxHourIndex

    return stats
  }

  /**
   * 撤销操作
   */
  undo() {
    if (!this.canUndo()) {
      return null
    }

    this.currentIndex--
    const record = this.history[this.currentIndex + 1]
    this.saveHistory()
    
    return {
      action: 'undo',
      record,
      newIndex: this.currentIndex
    }
  }

  /**
   * 重做操作
   */
  redo() {
    if (!this.canRedo()) {
      return null
    }

    this.currentIndex++
    const record = this.history[this.currentIndex]
    this.saveHistory()
    
    return {
      action: 'redo',
      record,
      newIndex: this.currentIndex
    }
  }

  /**
   * 检查是否可以撤销
   */
  canUndo() {
    return this.currentIndex >= 0
  }

  /**
   * 检查是否可以重做
   */
  canRedo() {
    return this.currentIndex < this.history.length - 1
  }

  /**
   * 获取当前状态
   */
  getCurrentState() {
    return {
      currentIndex: this.currentIndex,
      totalRecords: this.history.length,
      canUndo: this.canUndo(),
      canRedo: this.canRedo(),
      sessionId: this.sessionId
    }
  }

  /**
   * 清空历史记录
   */
  clearHistory() {
    this.history = []
    this.currentIndex = -1
    this.saveHistory()
  }

  /**
   * 保存历史记录到本地存储
   */
  saveHistory() {
    try {
      const data = {
        history: this.history,
        currentIndex: this.currentIndex,
        sessionId: this.sessionId,
        lastSaved: new Date()
      }
      localStorage.setItem('modificationHistory', JSON.stringify(data))
    } catch (error) {
      console.error('保存修改历史失败:', error)
    }
  }

  /**
   * 从本地存储加载历史记录
   */
  loadHistory() {
    try {
      const data = localStorage.getItem('modificationHistory')
      if (data) {
        const parsed = JSON.parse(data)
        this.history = parsed.history || []
        this.currentIndex = parsed.currentIndex || -1
        
        // 如果是新会话，重置索引
        if (parsed.sessionId !== this.sessionId) {
          this.currentIndex = this.history.length - 1
        }
      }
    } catch (error) {
      console.error('加载修改历史失败:', error)
      this.history = []
      this.currentIndex = -1
    }
  }

  /**
   * 导出历史记录
   */
  exportHistory(format = 'json') {
    const data = {
      exportTime: new Date(),
      sessionId: this.sessionId,
      statistics: this.getStatistics(),
      history: this.history
    }

    if (format === 'json') {
      return JSON.stringify(data, null, 2)
    } else if (format === 'csv') {
      return this.convertToCSV(data.history)
    }

    return data
  }

  /**
   * 转换为CSV格式
   */
  convertToCSV(records) {
    const headers = [
      'ID', '时间', '类型', '原文', '修改后', '问题', '分类', '字数变化'
    ]

    const rows = records.map(record => [
      record.id,
      record.timestamp.toISOString(),
      record.type,
      `"${(record.originalText || '').replace(/"/g, '""')}"`,
      `"${(record.modifiedText || '').replace(/"/g, '""')}"`,
      `"${(record.question || '').replace(/"/g, '""')}"`,
      record.metadata.category,
      record.metadata.changeLength
    ])

    return [headers, ...rows].map(row => row.join(',')).join('\n')
  }
}

// 创建单例实例
export const modificationHistoryService = new ModificationHistoryService()
export default modificationHistoryService
