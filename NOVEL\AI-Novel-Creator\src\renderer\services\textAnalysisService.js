// 文本分析服务
export class TextAnalysisService {
  constructor() {
    this.config = null
    this.loadConfig()
    this.isElectronEnv = !!(window.electronAPI && typeof window.electronAPI.invoke === 'function')
    this.corsErrorDetected = false // 标记是否检测到CORS错误
  }

  // 加载配置
  loadConfig() {
    try {
      // 使用与aiCreationService相同的配置键名
      const configStr = localStorage.getItem('aiSettings')
      if (configStr) {
        this.config = JSON.parse(configStr)
        console.log('文本分析服务配置加载成功:', this.config)
      } else {
        console.log('未找到AI配置')
      }
    } catch (error) {
      console.error('加载配置失败:', error)
    }
  }

  // 分析选中的文本
  async analyzeSelectedText(selectedText, question, context, options = {}) {
    // 检查配置状态
    const configStatus = this.checkConfiguration()
    if (!configStatus.isValid) {
      // 返回备用分析而不是抛出错误
      return this.generateFallbackAnalysis(selectedText, question, configStatus.message)
    }

    const prompt = this.buildAnalysisPrompt(selectedText, question, context)

    try {
      const response = await this.callAI(prompt, {
        temperature: 0.7,
        maxTokens: 4000
      })
      return this.parseAnalysisResponse(response)
    } catch (error) {
      console.error('文本分析失败:', error)

      // 根据错误类型提供不同的降级处理
      let errorMessage = '网络连接或API调用失败'
      if (error.message === 'CORS_ERROR') {
        errorMessage = '浏览器环境下无法直接调用AI服务，建议使用Electron桌面版本'
      } else if (error.message === 'NETWORK_ERROR') {
        errorMessage = '网络连接超时，请检查网络连接或稍后重试'
      } else if (error.message.includes('API请求失败')) {
        errorMessage = 'AI服务暂时不可用，请检查API配置或稍后重试'
      } else if (error.message.includes('API返回数据格式错误')) {
        errorMessage = 'AI服务返回异常，请检查API配置'
      } else if (error.message.includes('网络连接问题') || error.message.includes('超时')) {
        errorMessage = '网络连接不稳定，请检查网络设置或稍后重试'
      }

      // 返回备用分析
      return this.generateFallbackAnalysis(selectedText, question, errorMessage)
    }
  }

  // 生成修改建议
  async generateModificationSuggestions(analysis, selectedText, context, options = {}) {
    // 检查配置状态
    const configStatus = this.checkConfiguration()
    if (!configStatus.isValid) {
      // 返回备用建议
      return this.generateFallbackSuggestions(selectedText, configStatus.message)
    }

    const prompt = this.buildSuggestionPrompt(analysis, selectedText, context)

    try {
      const response = await this.callAI(prompt, {
        temperature: 0.7,
        maxTokens: 4000
      })
      return this.parseSuggestionResponse(response)
    } catch (error) {
      console.error('生成建议失败:', error)

      // 根据错误类型提供不同的降级处理
      let errorMessage = '网络连接或API调用失败'
      if (error.message === 'CORS_ERROR') {
        errorMessage = '浏览器环境下无法直接调用AI服务，建议使用Electron桌面版本'
      } else if (error.message.includes('API请求失败')) {
        errorMessage = 'AI服务暂时不可用，请检查API配置或稍后重试'
      }

      // 返回备用建议
      return this.generateFallbackSuggestions(selectedText, errorMessage)
    }
  }

  // 构建分析prompt
  buildAnalysisPrompt(selectedText, question, context) {
    return `作为专业的小说编辑，请分析以下文本片段：

## 选中文本
"${selectedText}"

## 用户疑问
${question}

## 上下文信息
- 章节号：第${context.chapterNumber}章
- 前一段：${context.previous || '无'}
- 当前段：${context.current}
- 后一段：${context.next || '无'}

## 分析要求
请从以下角度进行专业分析：

1. **文学质量**：语言表达、文笔风格、修辞手法
2. **情节逻辑**：情节发展的合理性、与前后文的连贯性
3. **角色塑造**：角色行为是否符合性格设定
4. **节奏把控**：这段文字在整体节奏中的作用
5. **读者体验**：是否能引起读者兴趣和共鸣

## 回答格式
请用专业但易懂的语言回答，重点针对用户的疑问，并指出可能存在的问题和优点。

分析结果：`
  }

  // 构建建议prompt
  buildSuggestionPrompt(analysis, selectedText, context) {
    return `基于以下分析结果，请生成具体的修改建议：

## 分析结果
${analysis}

## 原文
"${selectedText}"

## 上下文
- 章节号：第${context.chapterNumber}章
- 前一段：${context.previous || '无'}
- 当前段：${context.current}
- 后一段：${context.next || '无'}

## 建议要求
请生成2-3个具体的修改方案，每个方案包括：

1. **修改标题**：简洁描述修改的重点
2. **修改类型**：文笔优化/情节调整/角色优化/逻辑修正/语言优化等
3. **修改后文本**：具体的修改结果
4. **修改理由**：详细说明为什么这样修改
5. **预期效果**：修改后能达到什么效果

## 注意事项
- 保持原文的核心意思和情节发展方向
- 修改要符合小说的整体风格和基调
- 考虑与前后文的连贯性
- 优先解决用户提出的具体问题

## 回答格式
请按以下JSON格式返回：

\`\`\`json
[
  {
    "title": "修改标题",
    "type": "修改类型",
    "originalText": "原文",
    "modifiedText": "修改后的文本",
    "reason": "修改理由",
    "expectedEffect": "预期效果"
  }
]
\`\`\`

修改建议：`
  }

  // 获取流式输出设置
  getStreamSetting() {
    try {
      const advancedSettings = localStorage.getItem('advancedSettings')
      if (advancedSettings) {
        const settings = JSON.parse(advancedSettings)
        return settings.stream || false
      }
    } catch (error) {
      console.error('获取流式设置失败:', error)
    }
    return false
  }

  // 调用AI服务（通过Electron IPC）
  async callAI(prompt, options = {}) {
    // 如果已经检测到CORS错误且不在Electron环境中，直接抛出CORS错误
    if (this.corsErrorDetected && !this.isElectronEnv) {
      console.log('已知CORS限制，直接使用离线分析')
      throw new Error('CORS_ERROR')
    }

    // 使用Gemini API格式
    const requestBody = {
      contents: [
        {
          parts: [
            {
              text: `你是专业的小说编辑，请简洁分析以下文本：

${prompt}

请控制回答在1500字以内，重点突出关键问题和建议。`
            }
          ]
        }
      ],
      generationConfig: {
        temperature: options.temperature || 0.7,
        maxOutputTokens: options.maxTokens || 4000,
        topP: 0.8,
        topK: 40
      }
    }

    try {
      // 直接使用fetch调用，就像AI创作服务一样
      console.log('使用直接fetch调用AI API')

      // 暂时禁用流式输出，直接使用非流式处理
      {
        // 非流式输出处理 - 使用Gemini API
        const baseUrl = this.config.apiUrl || 'https://generativelanguage.googleapis.com/v1beta'
        const model = this.config.model || 'gemini-2.5-flash'
        const url = `${baseUrl}/models/${model}:generateContent?key=${this.config.apiKey}`

        console.log('调用URL:', url)

        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(requestBody)
        })

        if (!response.ok) {
          throw new Error(`API请求失败: ${response.status}`)
        }

        const data = await response.json()

        console.log('Gemini API详细响应:', data)

        // 解析Gemini API响应
        if (!data.candidates || !data.candidates[0]) {
          console.error('API返回数据格式错误，完整响应:', data)
          throw new Error('API返回数据格式错误')
        }

        const candidate = data.candidates[0]
        console.log('候选响应:', candidate)

        if (!candidate.content || !candidate.content.parts || !candidate.content.parts[0]) {
          console.error('API返回内容格式错误，候选内容:', candidate)
          throw new Error('API返回内容格式错误')
        }

        const fullText = candidate.content.parts[0].text
        console.log('完整AI响应文本长度:', fullText.length)
        console.log('完整AI响应文本内容:', fullText)

        return fullText
      }
    } catch (error) {
      console.error('AI API调用失败:', error)
      throw error
    }
  }

  // 解析分析响应
  parseAnalysisResponse(response) {
    // 简单的文本清理和格式化
    return response.trim()
  }

  // 解析建议响应
  parseSuggestionResponse(response) {
    try {
      // 尝试提取JSON部分
      const jsonMatch = response.match(/```json\s*([\s\S]*?)\s*```/)
      if (jsonMatch) {
        const suggestions = JSON.parse(jsonMatch[1])
        return suggestions.map(suggestion => ({
          title: suggestion.title || '修改建议',
          type: suggestion.type || '文本优化',
          originalText: suggestion.originalText || '',
          modifiedText: suggestion.modifiedText || '',
          reason: suggestion.reason || '改进文本质量',
          expectedEffect: suggestion.expectedEffect || '提升阅读体验'
        }))
      }
      
      // 如果没有JSON格式，尝试解析普通文本
      return this.parseTextSuggestions(response)
    } catch (error) {
      console.error('解析建议失败:', error)
      // 返回默认建议
      return this.generateFallbackSuggestions(response)
    }
  }

  // 解析文本格式的建议
  parseTextSuggestions(response) {
    const suggestions = []
    const lines = response.split('\n').filter(line => line.trim())
    
    let currentSuggestion = null
    
    for (const line of lines) {
      if (line.includes('方案') || line.includes('建议')) {
        if (currentSuggestion) {
          suggestions.push(currentSuggestion)
        }
        currentSuggestion = {
          title: line.trim(),
          type: '文本优化',
          originalText: '',
          modifiedText: '',
          reason: '',
          expectedEffect: ''
        }
      } else if (currentSuggestion) {
        if (line.includes('修改为') || line.includes('改为')) {
          currentSuggestion.modifiedText = line.replace(/.*[修改为|改为][:：]?\s*/, '').trim()
        } else if (line.includes('理由') || line.includes('原因')) {
          currentSuggestion.reason = line.replace(/.*[理由|原因][:：]?\s*/, '').trim()
        }
      }
    }
    
    if (currentSuggestion) {
      suggestions.push(currentSuggestion)
    }
    
    return suggestions.length > 0 ? suggestions : this.generateFallbackSuggestions(response)
  }

  // 重新加载配置
  reloadConfig() {
    this.loadConfig()
  }

  // 流式响应处理已移除，统一使用非流式处理

  // 网络连接诊断
  async diagnoseNetwork() {
    if (!this.isElectronEnv || !this.config?.apiUrl) {
      return { success: false, message: '无法进行网络诊断' }
    }

    try {
      const urlObj = new URL(this.config.apiUrl)
      const result = await window.electronAPI.invoke('network-diagnose', urlObj.hostname)
      return result
    } catch (error) {
      return { success: false, message: `诊断失败: ${error.message}` }
    }
  }

  // 检查配置状态
  checkConfiguration() {
    // 每次检查时重新加载配置，确保获取最新状态
    this.reloadConfig()

    console.log('检查配置状态:', this.config)

    if (!this.config) {
      return {
        isValid: false,
        message: 'AI服务未配置，请前往设置页面配置API密钥'
      }
    }

    if (!this.config.apiKey) {
      return {
        isValid: false,
        message: 'API密钥未设置，请在设置中配置您的API密钥'
      }
    }

    if (!this.config.apiUrl) {
      return {
        isValid: false,
        message: 'API地址未设置，请检查AI服务配置'
      }
    }

    return {
      isValid: true,
      message: '配置正常'
    }
  }

  // 生成备用分析
  generateFallbackAnalysis(selectedText, question, reason) {
    return `📝 离线文本分析

⚠️ ${reason}

针对您选中的文本"${selectedText.substring(0, 50)}${selectedText.length > 50 ? '...' : ''}"和问题"${question}"，这里是基于规则的基础分析：

## 文本特征分析
${this.analyzeTextFeatures(selectedText)}

## 常见问题检查
${this.checkCommonIssues(selectedText)}

## 改进建议
${this.getBasicSuggestions(selectedText)}

💡 提示：配置AI服务后可获得更专业的分析结果。请前往设置页面配置您的API密钥。`
  }

  // 分析文本特征
  analyzeTextFeatures(text) {
    const features = []

    if (text.length > 100) {
      features.push('• 文本较长，注意保持读者注意力')
    } else if (text.length < 20) {
      features.push('• 文本较短，可考虑增加细节描写')
    }

    if (text.match(/["""].*?["""]/)) {
      features.push('• 包含对话，注意角色语言风格一致性')
    }

    if (text.includes('突然') || text.includes('忽然')) {
      features.push('• 包含转折词，注意情节发展的自然性')
    }

    if (text.includes('非常') || text.includes('很') || text.includes('特别')) {
      features.push('• 包含程度副词，可考虑用更具体的描述替代')
    }

    return features.length > 0 ? features.join('\n') : '• 文本结构基本正常'
  }

  // 检查常见问题
  checkCommonIssues(text) {
    const issues = []

    // 检查重复词汇
    const words = text.split(/\s+/)
    const wordCount = {}
    words.forEach(word => {
      if (word.length > 1) {
        wordCount[word] = (wordCount[word] || 0) + 1
      }
    })

    const repeatedWords = Object.entries(wordCount)
      .filter(([word, count]) => count > 2)
      .map(([word]) => word)

    if (repeatedWords.length > 0) {
      issues.push(`• 词汇重复：${repeatedWords.slice(0, 3).join('、')}`)
    }

    // 检查句子长度
    const sentences = text.split(/[。！？]/).filter(s => s.trim())
    const longSentences = sentences.filter(s => s.length > 50)
    if (longSentences.length > 0) {
      issues.push('• 存在过长句子，建议适当断句')
    }

    return issues.length > 0 ? issues.join('\n') : '• 未发现明显问题'
  }

  // 获取基础建议
  getBasicSuggestions(text) {
    const suggestions = []

    if (text.includes('的的') || text.includes('了了')) {
      suggestions.push('• 检查是否有重复字词')
    }

    if (text.length > 80 && !text.includes('，') && !text.includes('。')) {
      suggestions.push('• 考虑添加标点符号，改善阅读节奏')
    }

    if (text.match(/[a-zA-Z]/)) {
      suggestions.push('• 检查英文字符是否需要中文替代')
    }

    suggestions.push('• 朗读检查语言流畅性')
    suggestions.push('• 确认与前后文的连贯性')

    return suggestions.join('\n')
  }

  // 生成备用建议（重写原方法）
  generateFallbackSuggestions(selectedText, reason) {
    return [
      {
        title: '语言精炼',
        type: '文笔优化',
        originalText: selectedText,
        modifiedText: '建议：删除冗余词汇，使表达更简洁有力',
        reason: '基于文本长度和结构的基础分析',
        expectedEffect: '提升阅读流畅性'
      },
      {
        title: '细节增强',
        type: '内容丰富',
        originalText: selectedText,
        modifiedText: '建议：增加具体的感官描写或情感细节',
        reason: '丰富文本内容，增强读者代入感',
        expectedEffect: '提升文本生动性'
      },
      {
        title: '逻辑检查',
        type: '结构优化',
        originalText: selectedText,
        modifiedText: '建议：检查与前后文的逻辑关系',
        reason: '确保情节发展的合理性',
        expectedEffect: '提升故事连贯性'
      }
    ].map(suggestion => ({
      ...suggestion,
      reason: `${suggestion.reason}\n\n⚠️ ${reason}\n💡 配置AI服务可获得更精准的建议`
    }))
  }

  // 生成智能问题建议
  generateSmartQuestions(selectedText) {
    const questions = []
    
    // 基于文本特征生成问题
    if (selectedText.includes('突然') || selectedText.includes('忽然')) {
      questions.push('这个转折是否过于突兀？')
    }
    
    if (selectedText.includes('强大') || selectedText.includes('厉害')) {
      questions.push('这个描述是否需要更具体？')
    }
    
    if (selectedText.match(/["""].*?["""]/)) {
      questions.push('这段对话是否符合角色性格？')
    }
    
    if (selectedText.length > 100) {
      questions.push('这段描述是否过于冗长？')
    }
    
    if (selectedText.length < 20) {
      questions.push('这段描述是否过于简略？')
    }
    
    // 通用问题
    questions.push('这段内容与前文的连贯性如何？')
    questions.push('这段文字的表达是否清晰？')
    questions.push('这里的情节发展是否合理？')
    questions.push('角色的反应是否符合性格？')
    
    return questions.slice(0, 6) // 最多返回6个问题
  }

  // 分析文本类型
  analyzeTextType(text) {
    if (text.match(/["""].*?["""]/)) {
      return 'dialogue' // 对话
    }
    
    if (text.includes('想到') || text.includes('心想') || text.includes('暗想')) {
      return 'thought' // 内心独白
    }
    
    if (text.includes('动作') || text.includes('走') || text.includes('跑') || text.includes('站')) {
      return 'action' // 动作描写
    }
    
    if (text.includes('风景') || text.includes('环境') || text.includes('景色')) {
      return 'description' // 环境描写
    }
    
    return 'narrative' // 叙述
  }

  // 获取针对性建议
  getTargetedSuggestions(textType, text) {
    const suggestions = {
      dialogue: [
        '对话是否符合角色身份和性格？',
        '语言风格是否一致？',
        '对话是否推进了情节？'
      ],
      thought: [
        '内心独白是否真实可信？',
        '是否揭示了角色的内在冲突？',
        '思考过程是否逻辑清晰？'
      ],
      action: [
        '动作描写是否生动具体？',
        '动作的节奏是否合适？',
        '是否体现了角色特点？'
      ],
      description: [
        '描写是否过于冗长？',
        '是否营造了合适的氛围？',
        '细节是否恰到好处？'
      ],
      narrative: [
        '叙述节奏是否合适？',
        '信息传达是否清晰？',
        '是否保持了读者兴趣？'
      ]
    }
    
    return suggestions[textType] || suggestions.narrative
  }
}

// 创建单例实例
export const textAnalysisService = new TextAnalysisService()
