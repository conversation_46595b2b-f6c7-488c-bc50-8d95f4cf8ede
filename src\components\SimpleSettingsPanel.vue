<template>
  <div class="simple-settings" v-if="visible">
    <!-- 设置头部 -->
    <div class="settings-header">
      <button class="back-btn" @click="$emit('close')" title="返回">
        <svg viewBox="0 0 24 24" fill="none">
          <path d="M19 12H5M12 19l-7-7 7-7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </button>
      <div class="header-title">
        <h1>设置</h1>
        <p>配置您的创作环境</p>
      </div>
    </div>

    <!-- 设置内容 -->
    <div class="settings-content">
      <!-- AI配置 -->
      <div class="setting-section">
        <h2 class="section-title">
          <svg viewBox="0 0 24 24" fill="none">
            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" stroke="currentColor" stroke-width="2"/>
          </svg>
          AI 配置
        </h2>
        
        <div class="form-group">
          <label>AI 服务商</label>
          <select v-model="settings.aiProvider" class="form-control">
            <option value="">选择服务商</option>
            <option value="openai">OpenAI</option>
            <option value="gemini">Google Gemini</option>
            <option value="claude">Anthropic Claude</option>
            <option value="custom">自定义</option>
          </select>
        </div>

        <div class="form-group">
          <label>API 地址</label>
          <input
            v-model="settings.apiUrl"
            type="text"
            class="form-control"
            :placeholder="getApiUrlPlaceholder()"
            :disabled="!settings.aiProvider"
          />
        </div>

        <div class="form-group">
          <label>API 密钥</label>
          <div class="input-group">
            <input
              v-model="settings.apiKey"
              :type="showApiKey ? 'text' : 'password'"
              class="form-control"
              placeholder="输入您的 API Key"
              :disabled="!settings.aiProvider"
            />
            <button
              type="button"
              class="input-btn"
              @click="showApiKey = !showApiKey"
              :disabled="!settings.aiProvider"
            >
              <svg v-if="showApiKey" viewBox="0 0 24 24" fill="none">
                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" stroke="currentColor" stroke-width="2"/>
                <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
              </svg>
              <svg v-else viewBox="0 0 24 24" fill="none">
                <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24" stroke="currentColor" stroke-width="2"/>
                <line x1="1" y1="1" x2="23" y2="23" stroke="currentColor" stroke-width="2"/>
              </svg>
            </button>
          </div>
        </div>

        <div class="form-group">
          <label>模型</label>
          <select v-model="settings.aiModel" class="form-control" :disabled="!settings.aiProvider">
            <option value="">选择模型</option>
            <option v-for="model in getAvailableModels()" :key="model.value" :value="model.value">
              {{ model.label }}
            </option>
          </select>
        </div>

        <div class="form-actions">
          <button
            type="button"
            class="btn btn-test"
            @click="testConnection"
            :disabled="!settings.aiProvider || !settings.apiKey || !settings.apiUrl"
          >
            测试连接
          </button>
        </div>
      </div>

      <!-- 编辑器配置 -->
      <div class="setting-section">
        <h2 class="section-title">
          <svg viewBox="0 0 24 24" fill="none">
            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" stroke="currentColor" stroke-width="2"/>
            <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" stroke="currentColor" stroke-width="2"/>
          </svg>
          编辑器
        </h2>
        
        <div class="form-group">
          <label>字体大小</label>
          <select v-model="settings.fontSize" class="form-control">
            <option value="14">14px</option>
            <option value="16">16px</option>
            <option value="18">18px</option>
            <option value="20">20px</option>
          </select>
        </div>

        <div class="form-group">
          <label>主题模式</label>
          <select v-model="settings.theme" class="form-control">
            <option value="light">浅色模式</option>
            <option value="dark">深色模式</option>
            <option value="auto">跟随系统</option>
          </select>
        </div>

        <div class="form-group">
          <label class="checkbox-label">
            <input type="checkbox" v-model="settings.autoSave" />
            <span class="checkmark"></span>
            自动保存
          </label>
        </div>
      </div>

      <!-- 保存按钮 -->
      <div class="settings-footer">
        <button type="button" class="btn btn-primary" @click="saveSettings">
          保存设置
        </button>
        <button type="button" class="btn btn-secondary" @click="resetSettings">
          重置默认
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, watch } from 'vue'

export default {
  name: 'SimpleSettingsPanel',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    isDarkMode: {
      type: Boolean,
      default: false
    }
  },
  emits: ['close', 'settings-changed'],
  setup(props, { emit }) {
    const showApiKey = ref(false)
    
    const settings = reactive({
      aiProvider: '',
      apiUrl: '',
      apiKey: '',
      aiModel: '',
      fontSize: '16',
      theme: 'light',
      autoSave: true
    })

    // 获取API URL占位符
    const getApiUrlPlaceholder = () => {
      if (!settings.aiProvider) {
        return '请先选择AI服务商'
      }
      const placeholders = {
        openai: 'https://api.openai.com/v1',
        gemini: 'https://generativelanguage.googleapis.com/v1',
        claude: 'https://api.anthropic.com/v1',
        custom: '输入自定义API地址'
      }
      return placeholders[settings.aiProvider] || '输入API地址'
    }

    // 获取可用模型
    const getAvailableModels = () => {
      const models = {
        openai: [
          { value: 'gpt-4', label: 'GPT-4' },
          { value: 'gpt-3.5-turbo', label: 'GPT-3.5 Turbo' }
        ],
        gemini: [
          { value: 'gemini-pro', label: 'Gemini Pro' },
          { value: 'gemini-pro-vision', label: 'Gemini Pro Vision' }
        ],
        claude: [
          { value: 'claude-3-opus', label: 'Claude 3 Opus' },
          { value: 'claude-3-sonnet', label: 'Claude 3 Sonnet' }
        ],
        custom: [
          { value: 'custom-model', label: '自定义模型' }
        ]
      }
      return models[settings.aiProvider] || []
    }

    // 测试连接
    const testConnection = async () => {
      if (!settings.apiKey || !settings.apiUrl) {
        alert('请先填写API地址和密钥')
        return
      }
      
      try {
        // 这里应该实现实际的API测试逻辑
        alert('连接测试成功！')
      } catch (error) {
        alert('连接测试失败：' + error.message)
      }
    }

    // 保存设置
    const saveSettings = () => {
      localStorage.setItem('app-settings', JSON.stringify(settings))
      emit('settings-changed', settings)
      alert('设置已保存')
    }

    // 重置设置
    const resetSettings = () => {
      Object.assign(settings, {
        aiProvider: '',
        apiUrl: '',
        apiKey: '',
        aiModel: '',
        fontSize: '16',
        theme: 'light',
        autoSave: true
      })
    }

    // 加载设置
    const loadSettings = () => {
      const saved = localStorage.getItem('app-settings')
      if (saved) {
        try {
          const parsedSettings = JSON.parse(saved)
          Object.assign(settings, parsedSettings)
        } catch (error) {
          console.error('加载设置失败:', error)
        }
      }
    }

    // 监听提供商变化，自动填充API URL
    watch(() => settings.aiProvider, (newProvider) => {
      if (newProvider && !settings.apiUrl) {
        settings.apiUrl = getApiUrlPlaceholder()
      }
      settings.aiModel = '' // 重置模型选择
    })

    // 初始化
    loadSettings()

    return {
      showApiKey,
      settings,
      getApiUrlPlaceholder,
      getAvailableModels,
      testConnection,
      saveSettings,
      resetSettings
    }
  }
}
</script>

<style scoped>
.simple-settings {
  height: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  display: flex;
  flex-direction: column;
}

.settings-header {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 24px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.8);
}

.back-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(0, 0, 0, 0.05);
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.back-btn:hover {
  background: rgba(74, 144, 226, 0.1);
  color: #4a90e2;
}

.back-btn svg {
  width: 20px;
  height: 20px;
}

.header-title h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.header-title p {
  margin: 4px 0 0 0;
  font-size: 14px;
  color: #666;
}

.settings-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

.setting-section {
  margin-bottom: 32px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.section-title svg {
  width: 20px;
  height: 20px;
  color: #4a90e2;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.form-control {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  transition: all 0.2s ease;
}

.form-control:focus {
  outline: none;
  border-color: #4a90e2;
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.form-control:disabled {
  background: rgba(0, 0, 0, 0.05);
  color: #999;
  cursor: not-allowed;
}

.input-group {
  display: flex;
  align-items: center;
}

.input-group .form-control {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: none;
}

.input-btn {
  padding: 12px;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-left: none;
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
  background: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.2s ease;
}

.input-btn:hover {
  background: rgba(0, 0, 0, 0.05);
}

.input-btn:disabled {
  background: rgba(0, 0, 0, 0.05);
  color: #999;
  cursor: not-allowed;
}

.input-btn svg {
  width: 16px;
  height: 16px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
}

.checkbox-label input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 18px;
  height: 18px;
  border: 2px solid rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  position: relative;
  transition: all 0.2s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
  background: #4a90e2;
  border-color: #4a90e2;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
  content: '';
  position: absolute;
  left: 5px;
  top: 2px;
  width: 4px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.form-actions {
  margin-top: 16px;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-test {
  background: #34c759;
  color: white;
}

.btn-test:hover {
  background: #28a745;
}

.btn:disabled {
  background: rgba(0, 0, 0, 0.1);
  color: #999;
  cursor: not-allowed;
}

.btn-primary {
  background: #4a90e2;
  color: white;
}

.btn-primary:hover {
  background: #357abd;
}

.btn-secondary {
  background: rgba(0, 0, 0, 0.1);
  color: #666;
  margin-left: 12px;
}

.btn-secondary:hover {
  background: rgba(0, 0, 0, 0.15);
}

.settings-footer {
  padding-top: 20px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  text-align: center;
}
</style>
