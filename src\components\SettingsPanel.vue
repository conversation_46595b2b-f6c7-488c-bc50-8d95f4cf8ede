<template>
  <div class="settings-overlay" v-if="visible">
    <div class="settings-panel" :class="{ 'settings-panel--dark': isDarkMode }">
      <!-- 设置面板头部 -->
      <div class="settings-header">
        <div class="header-left">
          <button class="back-btn" @click="closeSettings" title="返回主界面">
            <svg viewBox="0 0 24 24" fill="none">
              <path d="M19 12H5M12 19l-7-7 7-7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </button>
          <div class="header-content">
            <h1 class="settings-title">文房设置</h1>
            <p class="settings-subtitle">配置您的创作环境</p>
          </div>
        </div>
        <button class="close-btn" @click="closeSettings">
          <svg viewBox="0 0 24 24" fill="none">
            <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
          </svg>
        </button>
      </div>

      <!-- 设置内容区域 -->
      <div class="settings-content">
        <!-- 设置导航 -->
        <div class="settings-nav">
          <div
            v-for="section in settingSections"
            :key="section.key"
            class="nav-item"
            :class="{ 'nav-item--active': activeSection === section.key }"
            @click="setActiveSection(section.key)"
          >
            <div class="nav-icon">
              <SvgIcon :name="section.icon" :size="20" />
            </div>
            <span class="nav-text">{{ section.name }}</span>
          </div>
        </div>

        <!-- 设置面板内容 -->
        <div class="settings-main">
          <!-- AI配置 -->
          <div v-show="activeSection === 'ai'" class="settings-section">
            <AIConfigSection
              v-model="settings.ai"
              @save="saveSettings"
              @test="testAIConnection"
            />
          </div>

          <!-- 编辑器设置 -->
          <div v-show="activeSection === 'editor'" class="settings-section">
            <EditorConfigSection
              v-model="settings.editor"
              @save="saveSettings"
            />
          </div>

          <!-- 主题设置 -->
          <div v-show="activeSection === 'theme'" class="settings-section">
            <ThemeConfigSection
              v-model="settings.theme"
              @save="saveSettings"
            />
          </div>

          <!-- 导出设置 -->
          <div v-show="activeSection === 'export'" class="settings-section">
            <ExportConfigSection
              v-model="settings.export"
              @save="saveSettings"
            />
          </div>

          <!-- 关于信息 -->
          <div v-show="activeSection === 'about'" class="settings-section">
            <AboutSection />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import SvgIcon from './SvgIcon.vue'
import AIConfigSection from './settings/AIConfigSection.vue'
import EditorConfigSection from './settings/EditorConfigSection.vue'
import ThemeConfigSection from './settings/ThemeConfigSection.vue'
import ExportConfigSection from './settings/ExportConfigSection.vue'
import AboutSection from './settings/AboutSection.vue'

export default {
  name: 'SettingsPanel',
  components: {
    SvgIcon,
    AIConfigSection,
    EditorConfigSection,
    ThemeConfigSection,
    ExportConfigSection,
    AboutSection
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    isDarkMode: {
      type: Boolean,
      default: true
    }
  },
  emits: ['close', 'settings-changed'],
  setup(props, { emit }) {
    const activeSection = ref('ai')
    
    // 设置分类
    const settingSections = [
      { key: 'ai', name: 'AI配置', icon: 'robot' },
      { key: 'editor', name: '编辑器', icon: 'edit' },
      { key: 'theme', name: '主题', icon: 'palette' },
      { key: 'export', name: '导出', icon: 'download' },
      { key: 'about', name: '关于', icon: 'info' }
    ]

    // 设置数据
    const settings = reactive({
      ai: {
        provider: '',
        apiKey: '',
        model: '',
        temperature: 0.7,
        timeout: 30,
        maxRetries: 3,
        stream: true
      },
      editor: {
        fontSize: 16,
        lineHeight: 1.6,
        fontFamily: 'Microsoft YaHei',
        autoSave: true,
        autoSaveInterval: 30,
        wordCount: true
      },
      theme: {
        mode: 'dark',
        primaryColor: '#4a90e2',
        accentColor: '#667eea'
      },
      export: {
        defaultFormat: 'txt',
        includeChapterNumbers: true,
        includeTableOfContents: true
      }
    })

    // 切换设置分类
    const setActiveSection = (section) => {
      activeSection.value = section
    }

    // 关闭设置面板
    const closeSettings = () => {
      emit('close')
    }

    // 处理遮罩点击
    const handleOverlayClick = (event) => {
      if (event.target.classList.contains('settings-overlay')) {
        closeSettings()
      }
    }

    // 保存设置
    const saveSettings = () => {
      localStorage.setItem('app-settings', JSON.stringify(settings))
      emit('settings-changed', settings)
    }

    // 加载设置
    const loadSettings = () => {
      const saved = localStorage.getItem('app-settings')
      if (saved) {
        const parsedSettings = JSON.parse(saved)
        Object.assign(settings, parsedSettings)
      }
    }

    // 测试AI连接
    const testAIConnection = (config) => {
      console.log('测试AI连接:', config)
      // TODO: 实现AI连接测试
    }

    onMounted(() => {
      loadSettings()
    })

    return {
      activeSection,
      settingSections,
      settings,
      setActiveSection,
      closeSettings,
      handleOverlayClick,
      saveSettings,
      testAIConnection
    }
  }
}
</script>

<style scoped>
.settings-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.settings-panel {
  width: 100%;
  max-width: none;
  height: 100%;
  position: relative;
  top: 0;
  right: 0;
  width: 800px;
  height: 100vh;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-left: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
}

.settings-panel--dark {
  background: rgba(45, 45, 45, 0.95);
  border-left: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.3);
  color: #ffffff;
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(0, 0, 0, 0.05);
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.back-btn:hover {
  background: rgba(74, 144, 226, 0.1);
  color: #4a90e2;
  transform: translateX(-2px);
}

.back-btn svg {
  width: 20px;
  height: 20px;
}

.settings-panel--dark .back-btn {
  background: rgba(255, 255, 255, 0.1);
}

.settings-panel--dark .back-btn:hover {
  background: rgba(74, 144, 226, 0.2);
}

.settings-panel--dark .settings-header {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.settings-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 4px 0;
}

.settings-subtitle {
  font-size: 14px;
  opacity: 0.7;
  margin: 0;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.settings-panel--dark .close-btn {
  background: rgba(255, 255, 255, 0.1);
}

.close-btn:hover {
  background: rgba(0, 0, 0, 0.1);
  transform: scale(1.05);
}

.settings-panel--dark .close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.close-btn svg {
  width: 16px;
  height: 16px;
}

.settings-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.settings-nav {
  width: 200px;
  padding: 16px 0;
  border-right: 1px solid rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.02);
}

.settings-panel--dark .settings-nav {
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.02);
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin: 2px 8px;
  border-radius: 8px;
}

.nav-item:hover {
  background: rgba(0, 0, 0, 0.05);
}

.settings-panel--dark .nav-item:hover {
  background: rgba(255, 255, 255, 0.05);
}

.nav-item--active {
  background: rgba(74, 144, 226, 0.1);
  color: #4a90e2;
}

.nav-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.nav-text {
  font-size: 14px;
  font-weight: 500;
}

.settings-main {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

.settings-section {
  max-width: 600px;
}
</style>
