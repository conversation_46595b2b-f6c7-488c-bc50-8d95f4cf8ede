// 使用CDN加载的Vue应用入口文件
// 这个文件不使用ES模块，而是使用全局变量

console.log('开始初始化Vue应用 (CDN版本)')

// 等待所有CDN资源加载完成
function waitForDependencies() {
  return new Promise((resolve) => {
    const checkDependencies = () => {
      if (window.Vue && window.ElementPlus && window.ElementPlusIconsVue) {
        console.log('所有依赖已加载完成')
        resolve()
      } else {
        console.log('等待依赖加载...', {
          Vue: !!window.Vue,
          ElementPlus: !!window.ElementPlus,
          Icons: !!window.ElementPlusIconsVue
        })
        setTimeout(checkDependencies, 100)
      }
    }
    checkDependencies()
  })
}

// 简化的路由系统
const routes = {
  '/': 'Dashboard',
  '/projects': 'ProjectsSimple', 
  '/create': 'AutoCreateSimple',
  '/editor': 'EditorSimple',
  '/settings': 'SettingsSimple',
  '/ui-showcase': 'UIShowcase'
}

let currentRoute = '/'

// 简化的路由器
const router = {
  push(path) {
    currentRoute = path
    updateView()
  },
  currentRoute: {
    value: currentRoute
  }
}

// 更新视图
function updateView() {
  const app = document.querySelector('#app')
  const componentName = routes[currentRoute] || 'Dashboard'
  
  // 这里可以根据路由加载不同的组件
  // 暂时显示一个简单的界面
  app.innerHTML = `
    <div style="display: flex; height: 100vh; font-family: 'Microsoft YaHei', Arial, sans-serif;">
      <!-- 侧边栏 -->
      <div style="width: 280px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; box-shadow: 2px 0 10px rgba(0,0,0,0.1);">
        <div style="text-align: center; margin-bottom: 30px;">
          <div style="width: 60px; height: 60px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 15px; font-size: 24px; font-weight: bold;">墨</div>
          <h2 style="margin: 0; font-size: 18px;">AI小说创作助手</h2>
        </div>
        
        <nav>
          <div onclick="router.push('/')" style="padding: 12px 16px; margin: 5px 0; border-radius: 8px; cursor: pointer; background: ${currentRoute === '/' ? 'rgba(255,255,255,0.2)' : 'transparent'}; transition: all 0.3s;">
            📊 仪表盘
          </div>
          <div onclick="router.push('/projects')" style="padding: 12px 16px; margin: 5px 0; border-radius: 8px; cursor: pointer; background: ${currentRoute === '/projects' ? 'rgba(255,255,255,0.2)' : 'transparent'}; transition: all 0.3s;">
            📚 项目管理
          </div>
          <div onclick="router.push('/create')" style="padding: 12px 16px; margin: 5px 0; border-radius: 8px; cursor: pointer; background: ${currentRoute === '/create' ? 'rgba(255,255,255,0.2)' : 'transparent'}; transition: all 0.3s;">
            ✍️ 智能创作
          </div>
          <div onclick="router.push('/editor')" style="padding: 12px 16px; margin: 5px 0; border-radius: 8px; cursor: pointer; background: ${currentRoute === '/editor' ? 'rgba(255,255,255,0.2)' : 'transparent'}; transition: all 0.3s;">
            📝 编辑器
          </div>
          <div onclick="router.push('/settings')" style="padding: 12px 16px; margin: 5px 0; border-radius: 8px; cursor: pointer; background: ${currentRoute === '/settings' ? 'rgba(255,255,255,0.2)' : 'transparent'}; transition: all 0.3s;">
            ⚙️ 设置
          </div>
          <div onclick="router.push('/ui-showcase')" style="padding: 12px 16px; margin: 5px 0; border-radius: 8px; cursor: pointer; background: ${currentRoute === '/ui-showcase' ? 'rgba(255,255,255,0.2)' : 'transparent'}; transition: all 0.3s;">
            🎨 UI展示
          </div>
        </nav>
      </div>
      
      <!-- 主内容区域 -->
      <div style="flex: 1; padding: 30px; background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); overflow-y: auto;">
        <div style="max-width: 1200px; margin: 0 auto;">
          ${getPageContent(componentName)}
        </div>
      </div>
    </div>
  `
}

// 获取页面内容
function getPageContent(componentName) {
  switch(componentName) {
    case 'Dashboard':
      return `
        <div style="text-align: center; margin-bottom: 40px;">
          <h1 style="font-size: 36px; color: #2c3e50; margin-bottom: 10px;">欢迎使用AI小说创作助手</h1>
          <p style="font-size: 18px; color: #7f8c8d;">让AI助力您的创作之旅</p>
        </div>
        
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px;">
          <div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); text-align: center;">
            <div style="font-size: 48px; margin-bottom: 15px;">📚</div>
            <h3 style="color: #2c3e50; margin-bottom: 10px;">项目总数</h3>
            <p style="font-size: 24px; color: #e67e22; font-weight: bold;">12</p>
          </div>
          
          <div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); text-align: center;">
            <div style="font-size: 48px; margin-bottom: 15px;">✍️</div>
            <h3 style="color: #2c3e50; margin-bottom: 10px;">今日创作</h3>
            <p style="font-size: 24px; color: #27ae60; font-weight: bold;">2,580字</p>
          </div>
          
          <div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); text-align: center;">
            <div style="font-size: 48px; margin-bottom: 15px;">🎯</div>
            <h3 style="color: #2c3e50; margin-bottom: 10px;">完成进度</h3>
            <p style="font-size: 24px; color: #8e44ad; font-weight: bold;">68%</p>
          </div>
        </div>
        
        <div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
          <h2 style="color: #2c3e50; margin-bottom: 20px;">快速开始</h2>
          <div style="display: flex; gap: 15px; flex-wrap: wrap;">
            <button onclick="router.push('/create')" style="background: linear-gradient(45deg, #667eea, #764ba2); color: white; border: none; padding: 15px 25px; border-radius: 10px; cursor: pointer; font-size: 16px; transition: transform 0.3s;" onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
              开始新创作
            </button>
            <button onclick="router.push('/projects')" style="background: linear-gradient(45deg, #f093fb, #f5576c); color: white; border: none; padding: 15px 25px; border-radius: 10px; cursor: pointer; font-size: 16px; transition: transform 0.3s;" onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
              管理项目
            </button>
            <button onclick="router.push('/ui-showcase')" style="background: linear-gradient(45deg, #4facfe, #00f2fe); color: white; border: none; padding: 15px 25px; border-radius: 10px; cursor: pointer; font-size: 16px; transition: transform 0.3s;" onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
              查看UI展示
            </button>
          </div>
        </div>
      `
    
    case 'ProjectsSimple':
      return `
        <h1 style="color: #2c3e50; margin-bottom: 30px;">📚 项目管理</h1>
        <div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
          <p style="font-size: 18px; color: #7f8c8d; text-align: center; margin: 50px 0;">项目管理功能正在开发中...</p>
        </div>
      `
    
    case 'AutoCreateSimple':
      return `
        <h1 style="color: #2c3e50; margin-bottom: 30px;">✍️ 智能创作</h1>
        <div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
          <p style="font-size: 18px; color: #7f8c8d; text-align: center; margin: 50px 0;">AI智能创作功能正在开发中...</p>
        </div>
      `
    
    case 'EditorSimple':
      return `
        <h1 style="color: #2c3e50; margin-bottom: 30px;">📝 编辑器</h1>
        <div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
          <textarea style="width: 100%; height: 400px; border: 1px solid #ddd; border-radius: 8px; padding: 15px; font-size: 16px; font-family: 'Microsoft YaHei', Arial, sans-serif;" placeholder="在这里开始您的创作..."></textarea>
        </div>
      `
    
    case 'SettingsSimple':
      return `
        <h1 style="color: #2c3e50; margin-bottom: 30px;">⚙️ 设置</h1>
        <div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
          <p style="font-size: 18px; color: #7f8c8d; text-align: center; margin: 50px 0;">设置功能正在开发中...</p>
        </div>
      `
    
    case 'UIShowcase':
      return `
        <h1 style="color: #2c3e50; margin-bottom: 30px;">🎨 UI展示</h1>
        <div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); margin-bottom: 20px;">
          <h2 style="color: #2c3e50; margin-bottom: 20px;">现代化按钮</h2>
          <div style="display: flex; gap: 15px; flex-wrap: wrap; margin-bottom: 30px;">
            <button style="background: linear-gradient(45deg, #667eea, #764ba2); color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer;">主要按钮</button>
            <button style="background: linear-gradient(45deg, #f093fb, #f5576c); color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer;">次要按钮</button>
            <button style="background: linear-gradient(45deg, #4facfe, #00f2fe); color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer;">信息按钮</button>
          </div>
          
          <h2 style="color: #2c3e50; margin-bottom: 20px;">卡片展示</h2>
          <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 12px; text-align: center;">
              <h3>渐变卡片</h3>
              <p>美观的渐变效果</p>
            </div>
            <div style="background: white; padding: 20px; border-radius: 12px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); text-align: center;">
              <h3 style="color: #2c3e50;">阴影卡片</h3>
              <p style="color: #7f8c8d;">柔和的阴影效果</p>
            </div>
          </div>
        </div>
      `
    
    default:
      return `
        <div style="text-align: center; margin: 50px 0;">
          <h2 style="color: #2c3e50;">页面未找到</h2>
          <p style="color: #7f8c8d;">请检查URL或返回首页</p>
          <button onclick="router.push('/')" style="background: #3498db; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-top: 20px;">返回首页</button>
        </div>
      `
  }
}

// 初始化应用
async function initApp() {
  try {
    console.log('等待CDN依赖加载...')
    await waitForDependencies()
    
    console.log('CDN依赖加载完成，初始化应用')
    
    // 将router暴露到全局，供HTML中的onclick使用
    window.router = router
    
    // 初始化视图
    updateView()
    
    console.log('Vue应用初始化成功 (CDN版本)')
  } catch (error) {
    console.error('应用初始化失败:', error)
    
    // 显示错误信息
    document.getElementById('app').innerHTML = `
      <div style="padding: 40px; text-align: center; font-family: Arial, sans-serif;">
        <h1 style="color: #e74c3c;">应用加载失败</h1>
        <p style="color: #666;">请检查网络连接或刷新页面重试。</p>
        <button onclick="location.reload()" style="padding: 10px 20px; background: #e67e22; color: white; border: none; border-radius: 5px; cursor: pointer; margin-top: 20px;">
          刷新页面
        </button>
        <details style="margin-top: 20px; text-align: left; max-width: 600px; margin-left: auto; margin-right: auto;">
          <summary style="cursor: pointer; color: #3498db;">查看错误详情</summary>
          <pre style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 10px; overflow: auto;">${error.stack || error.message}</pre>
        </details>
      </div>
    `
  }
}

// 页面加载完成后初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initApp)
} else {
  initApp()
}
