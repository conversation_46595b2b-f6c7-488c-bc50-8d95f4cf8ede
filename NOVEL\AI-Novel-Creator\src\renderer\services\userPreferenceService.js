/**
 * 用户偏好学习服务
 * 分析用户的修改模式，学习个人写作偏好，提供个性化建议
 */

import { modificationHistoryService } from './modificationHistoryService'

class UserPreferenceService {
  constructor() {
    this.preferences = this.loadPreferences()
    this.learningData = this.loadLearningData()
    this.analysisCache = new Map()
    this.initializeDefaultPreferences()
  }

  /**
   * 初始化默认偏好
   */
  initializeDefaultPreferences() {
    if (!this.preferences.initialized) {
      this.preferences = {
        initialized: true,
        writingStyle: {
          preferredLength: 'medium', // short, medium, long
          sentenceComplexity: 'balanced', // simple, balanced, complex
          descriptiveLevel: 'moderate', // minimal, moderate, detailed
          dialogueStyle: 'natural', // formal, natural, casual
          pacing: 'steady' // slow, steady, fast
        },
        contentPreferences: {
          characterFocus: 0.7, // 0-1, 角色描写偏好
          plotFocus: 0.8, // 0-1, 情节推进偏好
          atmosphereFocus: 0.6, // 0-1, 氛围营造偏好
          actionFocus: 0.7, // 0-1, 动作描写偏好
          emotionFocus: 0.8 // 0-1, 情感表达偏好
        },
        modificationPatterns: {
          frequentTypes: [], // 常用修改类型
          avoidedTypes: [], // 避免的修改类型
          preferredSuggestions: [], // 偏好的建议类型
          rejectedSuggestions: [] // 拒绝的建议类型
        },
        qualityStandards: {
          minTextLength: 20,
          maxTextLength: 500,
          preferredWordDensity: 0.7,
          grammarStrictness: 'moderate', // loose, moderate, strict
          styleConsistency: 'high' // low, medium, high
        },
        learningMetrics: {
          totalModifications: 0,
          acceptedSuggestions: 0,
          rejectedSuggestions: 0,
          manualModifications: 0,
          lastLearningUpdate: new Date()
        }
      }
      this.savePreferences()
    }
  }

  /**
   * 学习用户修改行为
   * @param {Object} modification 修改记录
   * @param {string} action 用户行为 (accept, reject, modify, manual)
   */
  learnFromModification(modification, action = 'accept') {
    try {
      // 更新学习指标
      this.updateLearningMetrics(action)
      
      // 分析修改模式
      this.analyzeModificationPattern(modification, action)
      
      // 更新内容偏好
      this.updateContentPreferences(modification, action)
      
      // 更新写作风格偏好
      this.updateWritingStylePreferences(modification, action)
      
      // 更新质量标准
      this.updateQualityStandards(modification, action)
      
      // 保存学习数据
      this.saveLearningData()
      this.savePreferences()
      
      console.log('用户偏好学习完成:', action, modification.type)
    } catch (error) {
      console.error('用户偏好学习失败:', error)
    }
  }

  /**
   * 更新学习指标
   */
  updateLearningMetrics(action) {
    const metrics = this.preferences.learningMetrics
    
    metrics.totalModifications++
    
    switch (action) {
      case 'accept':
        metrics.acceptedSuggestions++
        break
      case 'reject':
        metrics.rejectedSuggestions++
        break
      case 'manual':
        metrics.manualModifications++
        break
    }
    
    metrics.lastLearningUpdate = new Date()
  }

  /**
   * 分析修改模式
   */
  analyzeModificationPattern(modification, action) {
    const patterns = this.preferences.modificationPatterns
    const modType = modification.suggestionType || modification.type
    
    if (action === 'accept') {
      // 增加偏好类型
      if (!patterns.frequentTypes.includes(modType)) {
        patterns.frequentTypes.push(modType)
      }
      
      // 从避免类型中移除
      const avoidIndex = patterns.avoidedTypes.indexOf(modType)
      if (avoidIndex > -1) {
        patterns.avoidedTypes.splice(avoidIndex, 1)
      }
      
      // 记录偏好建议
      if (modification.suggestionType && !patterns.preferredSuggestions.includes(modification.suggestionType)) {
        patterns.preferredSuggestions.push(modification.suggestionType)
      }
    } else if (action === 'reject') {
      // 增加避免类型
      if (!patterns.avoidedTypes.includes(modType)) {
        patterns.avoidedTypes.push(modType)
      }
      
      // 记录拒绝建议
      if (modification.suggestionType && !patterns.rejectedSuggestions.includes(modification.suggestionType)) {
        patterns.rejectedSuggestions.push(modification.suggestionType)
      }
    }
  }

  /**
   * 更新内容偏好
   */
  updateContentPreferences(modification, action) {
    const content = this.preferences.contentPreferences
    const weight = action === 'accept' ? 0.1 : -0.05
    
    // 根据修改类型调整偏好
    if (modification.originalText && modification.modifiedText) {
      const analysis = this.analyzeTextContent(modification.originalText, modification.modifiedText)
      
      if (analysis.hasCharacterFocus) {
        content.characterFocus = Math.max(0, Math.min(1, content.characterFocus + weight))
      }
      
      if (analysis.hasPlotFocus) {
        content.plotFocus = Math.max(0, Math.min(1, content.plotFocus + weight))
      }
      
      if (analysis.hasAtmosphereFocus) {
        content.atmosphereFocus = Math.max(0, Math.min(1, content.atmosphereFocus + weight))
      }
      
      if (analysis.hasActionFocus) {
        content.actionFocus = Math.max(0, Math.min(1, content.actionFocus + weight))
      }
      
      if (analysis.hasEmotionFocus) {
        content.emotionFocus = Math.max(0, Math.min(1, content.emotionFocus + weight))
      }
    }
  }

  /**
   * 更新写作风格偏好
   */
  updateWritingStylePreferences(modification, action) {
    if (action !== 'accept') return
    
    const style = this.preferences.writingStyle
    
    if (modification.originalText && modification.modifiedText) {
      const analysis = this.analyzeStyleChange(modification.originalText, modification.modifiedText)
      
      // 更新长度偏好
      if (analysis.lengthChange > 20) {
        style.preferredLength = 'long'
      } else if (analysis.lengthChange < -20) {
        style.preferredLength = 'short'
      }
      
      // 更新复杂度偏好
      if (analysis.complexityChange > 0.2) {
        style.sentenceComplexity = 'complex'
      } else if (analysis.complexityChange < -0.2) {
        style.sentenceComplexity = 'simple'
      }
      
      // 更新描述级别偏好
      if (analysis.descriptiveChange > 0.3) {
        style.descriptiveLevel = 'detailed'
      } else if (analysis.descriptiveChange < -0.3) {
        style.descriptiveLevel = 'minimal'
      }
    }
  }

  /**
   * 更新质量标准
   */
  updateQualityStandards(modification, action) {
    if (action !== 'accept') return
    
    const standards = this.preferences.qualityStandards
    
    if (modification.originalText) {
      const length = modification.originalText.length
      
      // 动态调整长度标准
      if (length < standards.minTextLength) {
        standards.minTextLength = Math.max(10, length - 5)
      }
      
      if (length > standards.maxTextLength) {
        standards.maxTextLength = Math.min(1000, length + 50)
      }
    }
  }

  /**
   * 分析文本内容
   */
  analyzeTextContent(originalText, modifiedText) {
    const analysis = {
      hasCharacterFocus: false,
      hasPlotFocus: false,
      hasAtmosphereFocus: false,
      hasActionFocus: false,
      hasEmotionFocus: false
    }
    
    const combinedText = originalText + ' ' + modifiedText
    
    // 角色相关关键词
    if (/[角色|人物|性格|特点|主角|配角]/.test(combinedText)) {
      analysis.hasCharacterFocus = true
    }
    
    // 情节相关关键词
    if (/[情节|剧情|故事|发展|转折|冲突]/.test(combinedText)) {
      analysis.hasPlotFocus = true
    }
    
    // 氛围相关关键词
    if (/[氛围|环境|背景|场景|气氛]/.test(combinedText)) {
      analysis.hasAtmosphereFocus = true
    }
    
    // 动作相关关键词
    if (/[动作|行为|移动|跑|走|跳|打]/.test(combinedText)) {
      analysis.hasActionFocus = true
    }
    
    // 情感相关关键词
    if (/[情感|感情|情绪|心理|喜悦|悲伤|愤怒]/.test(combinedText)) {
      analysis.hasEmotionFocus = true
    }
    
    return analysis
  }

  /**
   * 分析风格变化
   */
  analyzeStyleChange(originalText, modifiedText) {
    const analysis = {
      lengthChange: modifiedText.length - originalText.length,
      complexityChange: 0,
      descriptiveChange: 0
    }
    
    // 计算复杂度变化
    const originalComplexity = this.calculateComplexity(originalText)
    const modifiedComplexity = this.calculateComplexity(modifiedText)
    analysis.complexityChange = modifiedComplexity - originalComplexity
    
    // 计算描述性变化
    const originalDescriptive = this.calculateDescriptiveness(originalText)
    const modifiedDescriptive = this.calculateDescriptiveness(modifiedText)
    analysis.descriptiveChange = modifiedDescriptive - originalDescriptive
    
    return analysis
  }

  /**
   * 计算文本复杂度
   */
  calculateComplexity(text) {
    const sentences = text.split(/[。！？.!?]/).filter(s => s.trim())
    if (sentences.length === 0) return 0
    
    const avgLength = text.length / sentences.length
    const complexWords = (text.match(/[\u4e00-\u9fa5]{4,}/g) || []).length
    
    return Math.min((avgLength + complexWords) / 50, 1.0)
  }

  /**
   * 计算描述性程度
   */
  calculateDescriptiveness(text) {
    const descriptiveWords = (text.match(/[美丽|漂亮|高大|宽阔|明亮|黑暗|温暖|寒冷|细致|精美]/g) || []).length
    const adjectives = (text.match(/[的|地]/g) || []).length
    
    return Math.min((descriptiveWords + adjectives) / text.length, 1.0)
  }

  /**
   * 获取个性化建议
   * @param {string} textType 文本类型
   * @param {Object} context 上下文
   */
  getPersonalizedSuggestions(textType, context = {}) {
    const suggestions = []
    const prefs = this.preferences
    
    // 基于内容偏好生成建议
    if (prefs.contentPreferences.characterFocus > 0.7) {
      suggestions.push({
        type: '角色优化',
        priority: 'high',
        reason: '基于您的偏好，建议加强角色描写'
      })
    }
    
    if (prefs.contentPreferences.plotFocus > 0.8) {
      suggestions.push({
        type: '情节推进',
        priority: 'high',
        reason: '基于您的偏好，建议加强情节推进'
      })
    }
    
    // 基于写作风格偏好生成建议
    if (prefs.writingStyle.preferredLength === 'short') {
      suggestions.push({
        type: '精简表达',
        priority: 'medium',
        reason: '基于您的偏好，建议精简文字表达'
      })
    }
    
    if (prefs.writingStyle.descriptiveLevel === 'detailed') {
      suggestions.push({
        type: '细节描写',
        priority: 'medium',
        reason: '基于您的偏好，建议增加细节描写'
      })
    }
    
    // 基于修改模式生成建议
    prefs.modificationPatterns.frequentTypes.forEach(type => {
      suggestions.push({
        type: type,
        priority: 'low',
        reason: `您经常使用此类修改：${type}`
      })
    })
    
    return suggestions.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 }
      return priorityOrder[b.priority] - priorityOrder[a.priority]
    })
  }

  /**
   * 获取用户偏好统计
   */
  getPreferenceStatistics() {
    const prefs = this.preferences || {}
    const history = modificationHistoryService.getHistory({ limit: 100 }) || { records: [], total: 0 }

    return {
      writingStyle: prefs.writingStyle || {},
      contentPreferences: prefs.contentPreferences || {},
      learningMetrics: prefs.learningMetrics || {
        totalModifications: 0,
        acceptedSuggestions: 0,
        rejectedSuggestions: 0,
        manualModifications: 0,
        lastLearningUpdate: new Date()
      },
      modificationPatterns: {
        frequentTypes: (prefs.modificationPatterns?.frequentTypes || []).slice(0, 5),
        preferredSuggestions: (prefs.modificationPatterns?.preferredSuggestions || []).slice(0, 5),
        totalModifications: history.total || 0
      },
      qualityStandards: prefs.qualityStandards || {},
      learningProgress: this.calculateLearningProgress()
    }
  }

  /**
   * 计算学习进度
   */
  calculateLearningProgress() {
    const metrics = this.preferences?.learningMetrics || {
      totalModifications: 0,
      acceptedSuggestions: 0,
      rejectedSuggestions: 0,
      manualModifications: 0
    }
    const total = metrics.totalModifications || 0

    if (total === 0) return 0

    const acceptanceRate = (metrics.acceptedSuggestions || 0) / total
    const engagementRate = ((metrics.acceptedSuggestions || 0) + (metrics.rejectedSuggestions || 0)) / total

    return Math.min((acceptanceRate * 0.6 + engagementRate * 0.4) * 100, 100)
  }

  /**
   * 重置用户偏好
   */
  resetPreferences() {
    this.preferences = {}
    this.learningData = {}
    this.initializeDefaultPreferences()
    this.savePreferences()
    this.saveLearningData()
  }

  /**
   * 导出用户偏好
   */
  exportPreferences() {
    return {
      preferences: this.preferences,
      learningData: this.learningData,
      statistics: this.getPreferenceStatistics(),
      exportTime: new Date()
    }
  }

  /**
   * 导入用户偏好
   */
  importPreferences(data) {
    try {
      if (data.preferences) {
        this.preferences = { ...this.preferences, ...data.preferences }
      }
      
      if (data.learningData) {
        this.learningData = { ...this.learningData, ...data.learningData }
      }
      
      this.savePreferences()
      this.saveLearningData()
      
      return true
    } catch (error) {
      console.error('导入用户偏好失败:', error)
      return false
    }
  }

  /**
   * 保存偏好设置
   */
  savePreferences() {
    try {
      localStorage.setItem('userPreferences', JSON.stringify(this.preferences))
    } catch (error) {
      console.error('保存用户偏好失败:', error)
    }
  }

  /**
   * 加载偏好设置
   */
  loadPreferences() {
    try {
      const saved = localStorage.getItem('userPreferences')
      return saved ? JSON.parse(saved) : {}
    } catch (error) {
      console.error('加载用户偏好失败:', error)
      return {}
    }
  }

  /**
   * 保存学习数据
   */
  saveLearningData() {
    try {
      localStorage.setItem('userLearningData', JSON.stringify(this.learningData))
    } catch (error) {
      console.error('保存学习数据失败:', error)
    }
  }

  /**
   * 加载学习数据
   */
  loadLearningData() {
    try {
      const saved = localStorage.getItem('userLearningData')
      return saved ? JSON.parse(saved) : {}
    } catch (error) {
      console.error('加载学习数据失败:', error)
      return {}
    }
  }
}

// 创建单例实例
export const userPreferenceService = new UserPreferenceService()
export default userPreferenceService
