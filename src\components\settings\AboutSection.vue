<template>
  <div class="about-section">
    <div class="section-header">
      <h2 class="section-title">关于应用</h2>
      <p class="section-subtitle">应用信息与帮助</p>
    </div>

    <div class="about-content">
      <!-- 应用信息 -->
      <div class="app-info">
        <div class="app-logo">
          <div class="logo-icon">✍️</div>
          <div class="app-details">
            <h3 class="app-name">创作工坊</h3>
            <p class="app-version">版本 {{ appVersion }}</p>
            <p class="app-description">专业的AI辅助创作平台</p>
          </div>
        </div>

        <div class="app-stats">
          <div class="stat-item">
            <div class="stat-value">{{ formatNumber(totalWords) }}</div>
            <div class="stat-label">总字数</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ totalProjects }}</div>
            <div class="stat-label">项目数</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ usageDays }}</div>
            <div class="stat-label">使用天数</div>
          </div>
        </div>
      </div>

      <!-- 功能特性 -->
      <div class="features-section">
        <h3 class="section-subtitle">核心功能</h3>
        <div class="features-grid">
          <div class="feature-item">
            <div class="feature-icon">🤖</div>
            <div class="feature-info">
              <h4 class="feature-name">AI智能创作</h4>
              <p class="feature-desc">多种AI模型支持，激发创作灵感</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">📝</div>
            <div class="feature-info">
              <h4 class="feature-name">专业编辑器</h4>
              <p class="feature-desc">舒适的写作环境，专注创作体验</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">🎨</div>
            <div class="feature-info">
              <h4 class="feature-name">个性化主题</h4>
              <p class="feature-desc">多种主题风格，打造专属工作空间</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">📚</div>
            <div class="feature-info">
              <h4 class="feature-name">作品管理</h4>
              <p class="feature-desc">智能分类整理，轻松管理创作</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 系统信息 -->
      <div class="system-info">
        <h3 class="section-subtitle">系统信息</h3>
        <div class="info-grid">
          <div class="info-item">
            <span class="info-label">操作系统</span>
            <span class="info-value">{{ systemInfo.platform }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">浏览器</span>
            <span class="info-value">{{ systemInfo.browser }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">屏幕分辨率</span>
            <span class="info-value">{{ systemInfo.resolution }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">安装时间</span>
            <span class="info-value">{{ installDate }}</span>
          </div>
        </div>
      </div>

      <!-- 帮助链接 -->
      <div class="help-section">
        <h3 class="section-subtitle">帮助与支持</h3>
        <div class="help-links">
          <button class="help-link" @click="openUserGuide">
            <div class="link-icon">📖</div>
            <div class="link-info">
              <div class="link-title">使用指南</div>
              <div class="link-desc">详细的功能介绍和使用教程</div>
            </div>
          </button>
          <button class="help-link" @click="openFeedback">
            <div class="link-icon">💬</div>
            <div class="link-info">
              <div class="link-title">意见反馈</div>
              <div class="link-desc">提交建议或报告问题</div>
            </div>
          </button>
          <button class="help-link" @click="checkUpdates">
            <div class="link-icon">🔄</div>
            <div class="link-info">
              <div class="link-title">检查更新</div>
              <div class="link-desc">获取最新版本和功能</div>
            </div>
          </button>
          <button class="help-link" @click="openLicense">
            <div class="link-icon">📄</div>
            <div class="link-info">
              <div class="link-title">许可协议</div>
              <div class="link-desc">查看软件使用条款</div>
            </div>
          </button>
        </div>
      </div>

      <!-- 开发团队 -->
      <div class="team-section">
        <h3 class="section-subtitle">开发团队</h3>
        <div class="team-info">
          <p class="team-desc">
            创作工坊由一群热爱文学创作的开发者精心打造，
            致力于为创作者提供最优秀的AI辅助创作工具。
          </p>
          <div class="team-links">
            <button class="team-link" @click="openWebsite">官方网站</button>
            <button class="team-link" @click="openGithub">GitHub</button>
            <button class="team-link" @click="openContact">联系我们</button>
          </div>
        </div>
      </div>

      <!-- 版权信息 -->
      <div class="copyright">
        <p>&copy; 2024 创作工坊. 保留所有权利.</p>
        <p>Built with ❤️ for creators</p>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'

export default {
  name: 'AboutSection',
  setup() {
    const appVersion = ref('1.0.0')
    const totalWords = ref(125680)
    const totalProjects = ref(8)
    const usageDays = ref(45)
    const installDate = ref('2024-01-15')

    // 系统信息
    const systemInfo = ref({
      platform: 'Unknown',
      browser: 'Unknown',
      resolution: 'Unknown'
    })

    // 格式化数字
    const formatNumber = (num) => {
      return num.toLocaleString()
    }

    // 获取系统信息
    const getSystemInfo = () => {
      // 获取操作系统
      const platform = navigator.platform || 'Unknown'
      systemInfo.value.platform = platform

      // 获取浏览器信息
      const userAgent = navigator.userAgent
      let browser = 'Unknown'
      if (userAgent.includes('Chrome')) browser = 'Chrome'
      else if (userAgent.includes('Firefox')) browser = 'Firefox'
      else if (userAgent.includes('Safari')) browser = 'Safari'
      else if (userAgent.includes('Edge')) browser = 'Edge'
      systemInfo.value.browser = browser

      // 获取屏幕分辨率
      const resolution = `${screen.width} × ${screen.height}`
      systemInfo.value.resolution = resolution
    }

    // 打开用户指南
    const openUserGuide = () => {
      console.log('打开用户指南')
      // TODO: 实现用户指南功能
    }

    // 打开反馈页面
    const openFeedback = () => {
      console.log('打开反馈页面')
      // TODO: 实现反馈功能
    }

    // 检查更新
    const checkUpdates = () => {
      console.log('检查更新')
      // TODO: 实现更新检查功能
    }

    // 打开许可协议
    const openLicense = () => {
      console.log('打开许可协议')
      // TODO: 实现许可协议显示
    }

    // 打开官网
    const openWebsite = () => {
      window.open('https://example.com', '_blank')
    }

    // 打开GitHub
    const openGithub = () => {
      window.open('https://github.com/example/writing-studio', '_blank')
    }

    // 联系我们
    const openContact = () => {
      window.open('mailto:<EMAIL>', '_blank')
    }

    onMounted(() => {
      getSystemInfo()
    })

    return {
      appVersion,
      totalWords,
      totalProjects,
      usageDays,
      installDate,
      systemInfo,
      formatNumber,
      openUserGuide,
      openFeedback,
      checkUpdates,
      openLicense,
      openWebsite,
      openGithub,
      openContact
    }
  }
}
</script>

<style scoped>
.about-section {
  max-width: 600px;
}

.section-header {
  margin-bottom: 32px;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.section-subtitle {
  font-size: 14px;
  opacity: 0.7;
  margin: 0;
}

.about-content {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.app-info {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.app-logo {
  display: flex;
  align-items: center;
  gap: 16px;
}

.logo-icon {
  font-size: 48px;
  width: 64px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(74, 144, 226, 0.1);
  border-radius: 16px;
}

.app-name {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 4px 0;
}

.app-version {
  font-size: 14px;
  opacity: 0.7;
  margin: 0 0 8px 0;
}

.app-description {
  font-size: 16px;
  margin: 0;
  color: #666;
}

.app-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.stat-item {
  text-align: center;
  padding: 16px;
  background: rgba(74, 144, 226, 0.05);
  border-radius: 12px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #4a90e2;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  opacity: 0.7;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 12px;
}

.feature-icon {
  font-size: 24px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(74, 144, 226, 0.1);
  border-radius: 8px;
}

.feature-name {
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 4px 0;
}

.feature-desc {
  font-size: 12px;
  opacity: 0.7;
  margin: 0;
}

.info-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 8px;
}

.info-label {
  font-weight: 500;
  font-size: 14px;
}

.info-value {
  font-size: 14px;
  opacity: 0.8;
}

.help-links {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.help-link {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: rgba(0, 0, 0, 0.02);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.help-link:hover {
  background: rgba(74, 144, 226, 0.05);
  border-color: rgba(74, 144, 226, 0.2);
  transform: translateY(-1px);
}

.link-icon {
  font-size: 20px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(74, 144, 226, 0.1);
  border-radius: 6px;
}

.link-title {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 2px;
}

.link-desc {
  font-size: 12px;
  opacity: 0.7;
}

.team-info {
  text-align: center;
}

.team-desc {
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 20px;
  opacity: 0.8;
}

.team-links {
  display: flex;
  justify-content: center;
  gap: 12px;
}

.team-link {
  padding: 8px 16px;
  background: transparent;
  border: 1px solid #4a90e2;
  border-radius: 6px;
  color: #4a90e2;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.team-link:hover {
  background: #4a90e2;
  color: white;
}

.copyright {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  opacity: 0.6;
}

.copyright p {
  margin: 4px 0;
  font-size: 12px;
}
</style>
