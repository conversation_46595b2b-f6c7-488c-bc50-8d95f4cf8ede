<template>
  <div class="settings">
    <el-card>
      <template #header>
        <h3>应用设置</h3>
      </template>
      
      <el-form :model="settings" label-width="120px">
        <el-form-item label="Gemini API Key">
          <el-input 
            v-model="settings.geminiApiKey" 
            type="password" 
            placeholder="请输入Google Gemini API Key (以AIza开头)"
            show-password
          />
          <div class="form-tip">
            <div style="margin-bottom: 5px;">
              <strong>如何获取API Key：</strong>
            </div>
            <div>
              1. 访问 <el-link href="https://aistudio.google.com/app/apikey" target="_blank">Google AI Studio</el-link>
            </div>
            <div>
              2. 登录Google账号并创建API Key
            </div>
            <div>
              3. 复制API Key到此处 (格式: AIza...)
            </div>
            <div style="margin-top: 5px;">
              <el-button size="small" @click="testAIConnection" style="margin-right: 10px;">
                测试连接
              </el-button>
              <el-button size="small" type="success" @click="openAPIKeyHelp">
                获取帮助
              </el-button>
            </div>
          </div>
          <div class="ai-status">
            <el-tag :type="aiStatusType" size="small">
              {{ aiStatusText }}
            </el-tag>
          </div>
        </el-form-item>
        
        <el-form-item label="自动保存">
          <el-switch v-model="settings.autoSave" />
          <span class="setting-desc">开启后会自动保存您的创作内容</span>
        </el-form-item>
        
        <el-form-item label="保存间隔">
          <el-input-number 
            v-model="settings.autoSaveInterval" 
            :min="10" 
            :max="300" 
            :step="10"
            :disabled="!settings.autoSave"
          />
          <span class="setting-desc">秒</span>
        </el-form-item>
        
        <el-form-item label="主题风格">
          <el-radio-group v-model="settings.theme">
            <el-radio label="light">浅色</el-radio>
            <el-radio label="dark">深色</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="语言">
          <el-select v-model="settings.language">
            <el-option label="简体中文" value="zh-CN" />
            <el-option label="繁体中文" value="zh-TW" />
            <el-option label="English" value="en-US" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="默认字体">
          <el-select v-model="settings.fontFamily">
            <el-option label="微软雅黑" value="Microsoft YaHei" />
            <el-option label="宋体" value="SimSun" />
            <el-option label="楷体" value="KaiTi" />
            <el-option label="黑体" value="SimHei" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="字体大小">
          <el-input-number 
            v-model="settings.fontSize" 
            :min="12" 
            :max="24" 
            :step="1"
          />
          <span class="setting-desc">像素</span>
        </el-form-item>
        
        <el-form-item label="行间距">
          <el-input-number 
            v-model="settings.lineHeight" 
            :min="1.2" 
            :max="2.5" 
            :step="0.1"
          />
        </el-form-item>
        
        <el-form-item label="创作提醒">
          <el-switch v-model="settings.writingReminder" />
          <span class="setting-desc">定时提醒您进行创作</span>
        </el-form-item>
        
        <el-form-item label="提醒时间">
          <el-time-picker 
            v-model="settings.reminderTime" 
            format="HH:mm"
            :disabled="!settings.writingReminder"
          />
        </el-form-item>
        
        <el-form-item label="数据备份">
          <el-switch v-model="settings.dataBackup" />
          <span class="setting-desc">定期备份您的创作数据</span>
        </el-form-item>
        
        <el-form-item label="备份路径">
          <el-input 
            v-model="settings.backupPath" 
            placeholder="选择备份路径"
            :disabled="!settings.dataBackup"
          >
            <template #append>
              <el-button @click="selectBackupPath">浏览</el-button>
            </template>
          </el-input>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="saveSettings">保存设置</el-button>
          <el-button @click="resetSettings">重置设置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <el-card style="margin-top: 20px;">
      <template #header>
        <h3>关于应用</h3>
      </template>
      
      <div class="about-content">
        <div class="app-info">
          <h4>AI网文小说创作助手</h4>
          <p>版本：1.0.0</p>
          <p>基于Google Gemini AI的智能创作工具</p>
        </div>
        
        <div class="features">
          <h4>主要功能</h4>
          <ul>
            <li>全自动小说创作</li>
            <li>智能写作辅助</li>
            <li>文笔优化建议</li>
            <li>情节发展建议</li>
            <li>人物对话优化</li>
            <li>场景描写增强</li>
          </ul>
        </div>
        
        <div class="contact">
          <h4>技术支持</h4>
          <p>如遇问题请联系技术支持</p>
          <el-button type="text">反馈问题</el-button>
          <el-button type="text">使用帮助</el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, onMounted, computed } from 'vue'
import { useAppStore } from '@/store/app'
import { useAIStore } from '@/store/ai'
import { ElMessage } from 'element-plus'

export default {
  name: 'Settings',
  setup() {
    const appStore = useAppStore()
    const aiStore = useAIStore()
    
    const settings = ref({
      geminiApiKey: '',
      autoSave: true,
      autoSaveInterval: 30,
      theme: 'light',
      language: 'zh-CN',
      fontFamily: 'Microsoft YaHei',
      fontSize: 16,
      lineHeight: 1.8,
      writingReminder: false,
      reminderTime: null,
      dataBackup: false,
      backupPath: ''
    })
    
    // AI连接状态
    const aiConnectionStatus = computed(() => {
      if (!aiStore.isInitialized) return 'not-configured'
      if (aiStore.isConnected) return 'connected'
      return 'error'
    })
    
    const aiStatusText = computed(() => {
      switch (aiConnectionStatus.value) {
        case 'connected': return 'AI服务已连接'
        case 'error': return 'AI服务连接失败'
        default: return '未配置API Key'
      }
    })
    
    const aiStatusType = computed(() => {
      switch (aiConnectionStatus.value) {
        case 'connected': return 'success'
        case 'error': return 'danger'
        default: return 'warning'
      }
    })
    
    // 加载设置
    const loadSettings = () => {
      settings.value = { ...settings.value, ...appStore.config }
    }
    
    // 保存设置
    const saveSettings = async () => {
      try {
        await appStore.setConfig(settings.value)
        
        // 如果API Key发生变化，重新初始化AI服务
        if (settings.value.geminiApiKey && settings.value.geminiApiKey !== appStore.config.geminiApiKey) {
          ElMessage({
            message: '正在初始化AI服务...',
            type: 'info'
          })
          
          const result = await aiStore.initializeAI(settings.value.geminiApiKey)
          
          if (result.success) {
            ElMessage({
              message: '设置保存成功，AI服务已连接',
              type: 'success'
            })
          } else {
            ElMessage({
              message: `设置已保存，但AI服务连接失败：${result.message}`,
              type: 'warning'
            })
          }
        } else {
          ElMessage({
            message: '设置保存成功',
            type: 'success'
          })
        }
      } catch (error) {
        ElMessage({
          message: '设置保存失败',
          type: 'error'
        })
      }
    }
    
    // 测试AI连接
    const testAIConnection = async () => {
      if (!settings.value.geminiApiKey) {
        ElMessage({
          message: '请先输入API Key',
          type: 'warning'
        })
        return
      }
      
      try {
        ElMessage({
          message: '正在测试连接...',
          type: 'info'
        })
        
        const result = await aiStore.initializeAI(settings.value.geminiApiKey)
        
        if (result.success) {
          ElMessage({
            message: 'AI服务连接成功',
            type: 'success'
          })
        } else {
          ElMessage({
            message: result.message || 'AI服务连接失败',
            type: 'error'
          })
        }
      } catch (error) {
        ElMessage({
          message: `连接测试失败: ${error.message}`,
          type: 'error'
        })
      }
    }
    
    // 重置设置
    const resetSettings = () => {
      settings.value = {
        geminiApiKey: '',
        autoSave: true,
        autoSaveInterval: 30,
        theme: 'light',
        language: 'zh-CN',
        fontFamily: 'Microsoft YaHei',
        fontSize: 16,
        lineHeight: 1.8,
        writingReminder: false,
        reminderTime: null,
        dataBackup: false,
        backupPath: ''
      }
      ElMessage({
        message: '设置已重置',
        type: 'info'
      })
    }
    
    // 选择备份路径
    const selectBackupPath = () => {
      // 这里应该调用Electron的文件选择对话框
      ElMessage({
        message: '文件选择功能开发中...',
        type: 'info'
      })
    }
    
    // 打开API Key帮助
    const openAPIKeyHelp = () => {
      ElMessage({
        message: '正在打开API Key获取页面...',
        type: 'info'
      })
      // 打开Google AI Studio
      window.open('https://aistudio.google.com/app/apikey', '_blank')
    }
    
    onMounted(() => {
      loadSettings()
    })
    
    return {
      settings,
      aiConnectionStatus,
      aiStatusText,
      aiStatusType,
      saveSettings,
      testAIConnection,
      resetSettings,
      selectBackupPath,
      openAPIKeyHelp
    }
  }
}
</script>

<style scoped>
.settings {
  max-width: 800px;
  margin: 0 auto;
}

.form-tip {
  margin-top: 5px;
  font-size: 12px;
  color: #666;
  display: flex;
  align-items: center;
}

.ai-status {
  margin-top: 5px;
}

.setting-desc {
  margin-left: 10px;
  font-size: 14px;
  color: #666;
}

.about-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.app-info h4,
.features h4,
.contact h4 {
  margin: 0 0 10px 0;
  color: #2c3e50;
}

.app-info p {
  margin: 5px 0;
  color: #666;
}

.features ul {
  margin: 0;
  padding-left: 20px;
  color: #666;
}

.features li {
  margin: 5px 0;
}

.contact p {
  margin: 5px 0;
  color: #666;
}
</style>