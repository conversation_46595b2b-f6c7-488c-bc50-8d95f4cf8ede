<template>
  <div class="editor-config-section">
    <div class="section-header">
      <h2 class="section-title">编辑器设置</h2>
      <p class="section-subtitle">个性化您的写作环境</p>
    </div>

    <div class="config-form">
      <!-- 字体设置 -->
      <div class="settings-group">
        <h3 class="group-title">字体设置</h3>
        
        <div class="form-group">
          <label class="form-label">字体大小 ({{ localConfig.fontSize }}px)</label>
          <div class="slider-container">
            <input 
              type="range" 
              v-model.number="localConfig.fontSize"
              min="12" 
              max="24" 
              step="1"
              class="form-range"
            />
            <div class="range-labels">
              <span>12px</span>
              <span>24px</span>
            </div>
          </div>
        </div>

        <div class="form-group">
          <label class="form-label">行间距 ({{ localConfig.lineHeight }})</label>
          <div class="slider-container">
            <input 
              type="range" 
              v-model.number="localConfig.lineHeight"
              min="1.2" 
              max="2.5" 
              step="0.1"
              class="form-range"
            />
            <div class="range-labels">
              <span>紧凑</span>
              <span>宽松</span>
            </div>
          </div>
        </div>

        <div class="form-group">
          <label class="form-label">字体族</label>
          <select v-model="localConfig.fontFamily" class="form-select">
            <option value="Microsoft YaHei">微软雅黑</option>
            <option value="SimSun">宋体</option>
            <option value="KaiTi">楷体</option>
            <option value="SimHei">黑体</option>
            <option value="PingFang SC">苹方</option>
            <option value="Helvetica Neue">Helvetica Neue</option>
          </select>
        </div>
      </div>

      <!-- 编辑器行为 -->
      <div class="settings-group">
        <h3 class="group-title">编辑器行为</h3>
        
        <div class="form-group">
          <label class="form-checkbox">
            <input type="checkbox" v-model="localConfig.autoSave" />
            <span class="checkbox-mark"></span>
            自动保存
          </label>
          <div class="form-hint">开启后将自动保存您的创作内容</div>
        </div>

        <div class="form-group" v-if="localConfig.autoSave">
          <label class="form-label">自动保存间隔 ({{ localConfig.autoSaveInterval }}秒)</label>
          <div class="slider-container">
            <input 
              type="range" 
              v-model.number="localConfig.autoSaveInterval"
              min="10" 
              max="300" 
              step="10"
              class="form-range"
            />
            <div class="range-labels">
              <span>10秒</span>
              <span>5分钟</span>
            </div>
          </div>
        </div>

        <div class="form-group">
          <label class="form-checkbox">
            <input type="checkbox" v-model="localConfig.wordCount" />
            <span class="checkbox-mark"></span>
            显示字数统计
          </label>
          <div class="form-hint">实时显示字数统计信息</div>
        </div>

        <div class="form-group">
          <label class="form-checkbox">
            <input type="checkbox" v-model="localConfig.spellCheck" />
            <span class="checkbox-mark"></span>
            拼写检查
          </label>
          <div class="form-hint">启用浏览器拼写检查功能</div>
        </div>
      </div>

      <!-- 写作辅助 -->
      <div class="settings-group">
        <h3 class="group-title">写作辅助</h3>
        
        <div class="form-group">
          <label class="form-checkbox">
            <input type="checkbox" v-model="localConfig.writingReminder" />
            <span class="checkbox-mark"></span>
            写作提醒
          </label>
          <div class="form-hint">定时提醒您进行创作</div>
        </div>

        <div class="form-group" v-if="localConfig.writingReminder">
          <label class="form-label">提醒间隔</label>
          <select v-model="localConfig.reminderInterval" class="form-select">
            <option value="30">30分钟</option>
            <option value="60">1小时</option>
            <option value="120">2小时</option>
            <option value="240">4小时</option>
          </select>
        </div>

        <div class="form-group">
          <label class="form-checkbox">
            <input type="checkbox" v-model="localConfig.focusMode" />
            <span class="checkbox-mark"></span>
            专注模式
          </label>
          <div class="form-hint">隐藏干扰元素，专注写作</div>
        </div>
      </div>

      <!-- 预览区域 -->
      <div class="preview-section">
        <h3 class="group-title">预览效果</h3>
        <div class="preview-text" :style="previewStyle">
          这是一段示例文本，用于预览您的字体设置效果。您可以看到当前的字体大小、行间距和字体族的实际显示效果。
          <br><br>
          The quick brown fox jumps over the lazy dog. 这句话包含了英文的所有字母，可以很好地展示字体效果。
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="form-actions">
        <button type="button" class="save-btn" @click="saveConfig">
          保存设置
        </button>
        <button type="button" class="reset-btn" @click="resetConfig">
          重置默认
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'

export default {
  name: 'EditorConfigSection',
  props: {
    modelValue: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:modelValue', 'save'],
  setup(props, { emit }) {
    const localConfig = ref({
      fontSize: 16,
      lineHeight: 1.6,
      fontFamily: 'Microsoft YaHei',
      autoSave: true,
      autoSaveInterval: 30,
      wordCount: true,
      spellCheck: false,
      writingReminder: false,
      reminderInterval: 60,
      focusMode: false,
      ...props.modelValue
    })

    // 预览样式
    const previewStyle = computed(() => ({
      fontSize: `${localConfig.value.fontSize}px`,
      lineHeight: localConfig.value.lineHeight,
      fontFamily: localConfig.value.fontFamily
    }))

    // 保存配置
    const saveConfig = () => {
      emit('update:modelValue', localConfig.value)
      emit('save')
    }

    // 重置配置
    const resetConfig = () => {
      localConfig.value = {
        fontSize: 16,
        lineHeight: 1.6,
        fontFamily: 'Microsoft YaHei',
        autoSave: true,
        autoSaveInterval: 30,
        wordCount: true,
        spellCheck: false,
        writingReminder: false,
        reminderInterval: 60,
        focusMode: false
      }
    }

    // 监听props变化
    watch(() => props.modelValue, (newValue) => {
      localConfig.value = { ...localConfig.value, ...newValue }
    }, { deep: true })

    return {
      localConfig,
      previewStyle,
      saveConfig,
      resetConfig
    }
  }
}
</script>

<style scoped>
.editor-config-section {
  max-width: 600px;
}

.section-header {
  margin-bottom: 32px;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: inherit;
}

.section-subtitle {
  font-size: 14px;
  opacity: 0.7;
  margin: 0;
}

.config-form {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.settings-group {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.group-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  color: inherit;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  font-size: 14px;
  font-weight: 500;
  color: inherit;
}

.form-select {
  padding: 12px 16px;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  font-size: 14px;
  transition: all 0.2s ease;
}

.form-select:focus {
  outline: none;
  border-color: #4a90e2;
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.slider-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-range {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: rgba(0, 0, 0, 0.1);
  outline: none;
  -webkit-appearance: none;
}

.form-range::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #4a90e2;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(74, 144, 226, 0.3);
}

.range-labels {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  opacity: 0.6;
}

.form-checkbox {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  font-size: 14px;
}

.form-checkbox input[type="checkbox"] {
  display: none;
}

.checkbox-mark {
  width: 18px;
  height: 18px;
  border: 2px solid rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  position: relative;
  transition: all 0.2s ease;
}

.form-checkbox input[type="checkbox"]:checked + .checkbox-mark {
  background: #4a90e2;
  border-color: #4a90e2;
}

.form-checkbox input[type="checkbox"]:checked + .checkbox-mark::after {
  content: '';
  position: absolute;
  left: 5px;
  top: 2px;
  width: 4px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.form-hint {
  font-size: 12px;
  opacity: 0.6;
}

.preview-section {
  padding: 20px;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.preview-text {
  padding: 16px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  color: #333;
  transition: all 0.2s ease;
}

.form-actions {
  display: flex;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.save-btn,
.reset-btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.save-btn {
  background: #4a90e2;
  color: white;
  border: none;
}

.save-btn:hover {
  background: #357abd;
  transform: translateY(-1px);
}

.reset-btn {
  background: transparent;
  color: #666;
  border: 1px solid rgba(0, 0, 0, 0.2);
}

.reset-btn:hover {
  background: rgba(0, 0, 0, 0.05);
}
</style>
