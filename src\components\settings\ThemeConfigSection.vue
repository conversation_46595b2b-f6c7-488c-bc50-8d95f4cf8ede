<template>
  <div class="theme-config-section">
    <div class="section-header">
      <h2 class="section-title">主题设置</h2>
      <p class="section-subtitle">个性化您的界面外观</p>
    </div>

    <div class="config-form">
      <!-- 主题模式 -->
      <div class="settings-group">
        <h3 class="group-title">主题模式</h3>
        
        <div class="theme-options">
          <div 
            class="theme-option"
            :class="{ 'theme-option--active': localConfig.mode === 'light' }"
            @click="setThemeMode('light')"
          >
            <div class="theme-preview theme-preview--light">
              <div class="preview-header"></div>
              <div class="preview-content">
                <div class="preview-sidebar"></div>
                <div class="preview-main"></div>
              </div>
            </div>
            <div class="theme-info">
              <h4 class="theme-name">日间模式</h4>
              <p class="theme-desc">明亮清新的界面</p>
            </div>
          </div>

          <div 
            class="theme-option"
            :class="{ 'theme-option--active': localConfig.mode === 'dark' }"
            @click="setThemeMode('dark')"
          >
            <div class="theme-preview theme-preview--dark">
              <div class="preview-header"></div>
              <div class="preview-content">
                <div class="preview-sidebar"></div>
                <div class="preview-main"></div>
              </div>
            </div>
            <div class="theme-info">
              <h4 class="theme-name">夜间模式</h4>
              <p class="theme-desc">护眼的深色主题</p>
            </div>
          </div>

          <div 
            class="theme-option"
            :class="{ 'theme-option--active': localConfig.mode === 'auto' }"
            @click="setThemeMode('auto')"
          >
            <div class="theme-preview theme-preview--auto">
              <div class="preview-header"></div>
              <div class="preview-content">
                <div class="preview-sidebar"></div>
                <div class="preview-main"></div>
              </div>
            </div>
            <div class="theme-info">
              <h4 class="theme-name">自动切换</h4>
              <p class="theme-desc">跟随系统设置</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 色彩配置 -->
      <div class="settings-group">
        <h3 class="group-title">色彩配置</h3>
        
        <div class="form-group">
          <label class="form-label">主色调</label>
          <div class="color-picker-group">
            <div class="color-presets">
              <div 
                v-for="color in colorPresets" 
                :key="color.value"
                class="color-preset"
                :class="{ 'color-preset--active': localConfig.primaryColor === color.value }"
                :style="{ backgroundColor: color.value }"
                @click="setPrimaryColor(color.value)"
                :title="color.name"
              ></div>
            </div>
            <input 
              type="color" 
              v-model="localConfig.primaryColor"
              class="color-input"
              title="自定义颜色"
            />
          </div>
        </div>

        <div class="form-group">
          <label class="form-label">强调色</label>
          <div class="color-picker-group">
            <div class="color-presets">
              <div 
                v-for="color in accentColors" 
                :key="color.value"
                class="color-preset"
                :class="{ 'color-preset--active': localConfig.accentColor === color.value }"
                :style="{ backgroundColor: color.value }"
                @click="setAccentColor(color.value)"
                :title="color.name"
              ></div>
            </div>
            <input 
              type="color" 
              v-model="localConfig.accentColor"
              class="color-input"
              title="自定义强调色"
            />
          </div>
        </div>
      </div>

      <!-- 界面效果 -->
      <div class="settings-group">
        <h3 class="group-title">界面效果</h3>
        
        <div class="form-group">
          <label class="form-label">毛玻璃强度 ({{ localConfig.blurIntensity }}px)</label>
          <div class="slider-container">
            <input 
              type="range" 
              v-model.number="localConfig.blurIntensity"
              min="0" 
              max="30" 
              step="2"
              class="form-range"
            />
            <div class="range-labels">
              <span>关闭</span>
              <span>最强</span>
            </div>
          </div>
        </div>

        <div class="form-group">
          <label class="form-label">透明度 ({{ Math.round(localConfig.opacity * 100) }}%)</label>
          <div class="slider-container">
            <input 
              type="range" 
              v-model.number="localConfig.opacity"
              min="0.7" 
              max="1" 
              step="0.05"
              class="form-range"
            />
            <div class="range-labels">
              <span>透明</span>
              <span>不透明</span>
            </div>
          </div>
        </div>

        <div class="form-group">
          <label class="form-checkbox">
            <input type="checkbox" v-model="localConfig.animations" />
            <span class="checkbox-mark"></span>
            启用动画效果
          </label>
          <div class="form-hint">界面切换和交互动画</div>
        </div>

        <div class="form-group">
          <label class="form-checkbox">
            <input type="checkbox" v-model="localConfig.shadows" />
            <span class="checkbox-mark"></span>
            显示阴影
          </label>
          <div class="form-hint">为界面元素添加阴影效果</div>
        </div>
      </div>

      <!-- 预览区域 -->
      <div class="preview-section">
        <h3 class="group-title">效果预览</h3>
        <div class="effect-preview" :style="previewStyle">
          <div class="preview-card">
            <div class="card-header" :style="{ backgroundColor: localConfig.primaryColor }">
              <h4>预览卡片</h4>
            </div>
            <div class="card-content">
              <p>这是一个预览卡片，展示当前的主题设置效果。</p>
              <button class="preview-button" :style="{ backgroundColor: localConfig.accentColor }">
                按钮示例
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="form-actions">
        <button type="button" class="save-btn" @click="saveConfig">
          应用主题
        </button>
        <button type="button" class="reset-btn" @click="resetConfig">
          重置默认
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'

export default {
  name: 'ThemeConfigSection',
  props: {
    modelValue: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:modelValue', 'save'],
  setup(props, { emit }) {
    const localConfig = ref({
      mode: 'dark',
      primaryColor: '#4a90e2',
      accentColor: '#667eea',
      blurIntensity: 20,
      opacity: 0.9,
      animations: true,
      shadows: true,
      ...props.modelValue
    })

    // 颜色预设
    const colorPresets = [
      { name: '经典蓝', value: '#4a90e2' },
      { name: '优雅紫', value: '#667eea' },
      { name: '温暖橙', value: '#ff9500' },
      { name: '清新绿', value: '#34c759' },
      { name: '活力红', value: '#ff3b30' },
      { name: '深邃紫', value: '#5856d6' },
      { name: '科技青', value: '#00c7be' },
      { name: '典雅灰', value: '#8e8e93' }
    ]

    const accentColors = [
      { name: '渐变紫', value: '#667eea' },
      { name: '渐变蓝', value: '#764ba2' },
      { name: '渐变粉', value: '#f093fb' },
      { name: '渐变橙', value: '#fad0c4' },
      { name: '渐变绿', value: '#a8edea' },
      { name: '渐变黄', value: '#ffecd2' }
    ]

    // 预览样式
    const previewStyle = computed(() => ({
      backdropFilter: `blur(${localConfig.value.blurIntensity}px)`,
      opacity: localConfig.value.opacity,
      transition: localConfig.value.animations ? 'all 0.3s ease' : 'none',
      boxShadow: localConfig.value.shadows ? '0 8px 32px rgba(0, 0, 0, 0.1)' : 'none'
    }))

    // 设置主题模式
    const setThemeMode = (mode) => {
      localConfig.value.mode = mode
    }

    // 设置主色调
    const setPrimaryColor = (color) => {
      localConfig.value.primaryColor = color
    }

    // 设置强调色
    const setAccentColor = (color) => {
      localConfig.value.accentColor = color
    }

    // 保存配置
    const saveConfig = () => {
      emit('update:modelValue', localConfig.value)
      emit('save')
    }

    // 重置配置
    const resetConfig = () => {
      localConfig.value = {
        mode: 'dark',
        primaryColor: '#4a90e2',
        accentColor: '#667eea',
        blurIntensity: 20,
        opacity: 0.9,
        animations: true,
        shadows: true
      }
    }

    // 监听props变化
    watch(() => props.modelValue, (newValue) => {
      localConfig.value = { ...localConfig.value, ...newValue }
    }, { deep: true })

    return {
      localConfig,
      colorPresets,
      accentColors,
      previewStyle,
      setThemeMode,
      setPrimaryColor,
      setAccentColor,
      saveConfig,
      resetConfig
    }
  }
}
</script>

<style scoped>
.theme-config-section {
  max-width: 600px;
}

.section-header {
  margin-bottom: 32px;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: inherit;
}

.section-subtitle {
  font-size: 14px;
  opacity: 0.7;
  margin: 0;
}

.config-form {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.settings-group {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.group-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  color: inherit;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.theme-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: 16px;
}

.theme-option {
  padding: 16px;
  border: 2px solid rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: rgba(255, 255, 255, 0.5);
}

.theme-option:hover {
  border-color: rgba(74, 144, 226, 0.5);
  transform: translateY(-2px);
}

.theme-option--active {
  border-color: #4a90e2;
  background: rgba(74, 144, 226, 0.1);
}

.theme-preview {
  width: 100%;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 12px;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.theme-preview--light {
  background: #ffffff;
}

.theme-preview--dark {
  background: #2d2d2d;
}

.theme-preview--auto {
  background: linear-gradient(45deg, #ffffff 50%, #2d2d2d 50%);
}

.preview-header {
  height: 20px;
  background: rgba(0, 0, 0, 0.1);
}

.theme-preview--dark .preview-header {
  background: rgba(255, 255, 255, 0.1);
}

.preview-content {
  display: flex;
  height: 60px;
}

.preview-sidebar {
  width: 30%;
  background: rgba(0, 0, 0, 0.05);
}

.theme-preview--dark .preview-sidebar {
  background: rgba(255, 255, 255, 0.05);
}

.preview-main {
  flex: 1;
  background: rgba(0, 0, 0, 0.02);
}

.theme-preview--dark .preview-main {
  background: rgba(255, 255, 255, 0.02);
}

.theme-info {
  text-align: center;
}

.theme-name {
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: inherit;
}

.theme-desc {
  font-size: 12px;
  opacity: 0.7;
  margin: 0;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  font-size: 14px;
  font-weight: 500;
  color: inherit;
}

.color-picker-group {
  display: flex;
  align-items: center;
  gap: 16px;
}

.color-presets {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.color-preset {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.2s ease;
}

.color-preset:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.color-preset--active {
  border-color: #ffffff;
  box-shadow: 0 0 0 2px #4a90e2;
}

.color-input {
  width: 40px;
  height: 32px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  background: none;
}

.slider-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-range {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: rgba(0, 0, 0, 0.1);
  outline: none;
  -webkit-appearance: none;
}

.form-range::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #4a90e2;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(74, 144, 226, 0.3);
}

.range-labels {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  opacity: 0.6;
}

.form-checkbox {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  font-size: 14px;
}

.form-checkbox input[type="checkbox"] {
  display: none;
}

.checkbox-mark {
  width: 18px;
  height: 18px;
  border: 2px solid rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  position: relative;
  transition: all 0.2s ease;
}

.form-checkbox input[type="checkbox"]:checked + .checkbox-mark {
  background: #4a90e2;
  border-color: #4a90e2;
}

.form-checkbox input[type="checkbox"]:checked + .checkbox-mark::after {
  content: '';
  position: absolute;
  left: 5px;
  top: 2px;
  width: 4px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.form-hint {
  font-size: 12px;
  opacity: 0.6;
}

.preview-section {
  padding: 20px;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.effect-preview {
  padding: 20px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.preview-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.card-header {
  padding: 16px;
  color: white;
}

.card-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.card-content {
  padding: 16px;
}

.card-content p {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: #666;
}

.preview-button {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  color: white;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.preview-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.form-actions {
  display: flex;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.save-btn,
.reset-btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.save-btn {
  background: #4a90e2;
  color: white;
  border: none;
}

.save-btn:hover {
  background: #357abd;
  transform: translateY(-1px);
}

.reset-btn {
  background: transparent;
  color: #666;
  border: 1px solid rgba(0, 0, 0, 0.2);
}

.reset-btn:hover {
  background: rgba(0, 0, 0, 0.05);
}
</style>
