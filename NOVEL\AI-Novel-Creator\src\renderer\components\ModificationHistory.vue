<template>
  <div class="modification-history">
    <!-- 历史记录头部 -->
    <div class="history-header">
      <div class="header-left">
        <h3>
          <el-icon><Clock /></el-icon>
          修改历史
        </h3>
        <el-tag type="info" size="small">
          共 {{ statistics.totalModifications }} 次修改
        </el-tag>
      </div>
      
      <div class="header-actions">
        <el-button 
          @click="undo" 
          :disabled="!canUndo"
          size="small"
          type="primary"
        >
          <el-icon><RefreshLeft /></el-icon>
          撤销
        </el-button>
        
        <el-button 
          @click="redo" 
          :disabled="!canRedo"
          size="small"
          type="primary"
        >
          <el-icon><RefreshRight /></el-icon>
          重做
        </el-button>
        
        <el-dropdown @command="handleMenuCommand">
          <el-button size="small">
            <el-icon><More /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="export">导出历史</el-dropdown-item>
              <el-dropdown-item command="statistics">查看统计</el-dropdown-item>
              <el-dropdown-item command="clear" divided>清空历史</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="history-filters">
      <el-row :gutter="16">
        <el-col :span="8">
          <el-select 
            v-model="filters.category" 
            placeholder="筛选类别"
            clearable
            size="small"
          >
            <el-option label="全部" value="" />
            <el-option 
              v-for="(count, category) in statistics.categories"
              :key="category"
              :label="`${category} (${count})`"
              :value="category"
            />
          </el-select>
        </el-col>
        
        <el-col :span="8">
          <el-date-picker
            v-model="filters.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            size="small"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-col>
        
        <el-col :span="8">
          <el-input
            v-model="filters.searchText"
            placeholder="搜索修改内容"
            size="small"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
      </el-row>
    </div>

    <!-- 历史记录列表 -->
    <div class="history-list">
      <el-timeline>
        <el-timeline-item
          v-for="record in historyRecords"
          :key="record.id"
          :timestamp="formatTime(record.timestamp)"
          :type="getTimelineType(record)"
          :icon="getTimelineIcon(record)"
        >
          <div class="history-item">
            <div class="item-header">
              <el-tag 
                :type="getCategoryColor(record.metadata.category)"
                size="small"
              >
                {{ record.metadata.category }}
              </el-tag>
              
              <span class="change-stats">
                <el-icon><Edit /></el-icon>
                {{ record.metadata.changeLength > 0 ? '+' : '' }}{{ record.metadata.changeLength }} 字
              </span>
            </div>
            
            <div class="item-content">
              <div v-if="record.question" class="question">
                <strong>问题：</strong>{{ record.question }}
              </div>
              
              <div class="text-changes">
                <div v-if="record.originalText" class="original-text">
                  <strong>原文：</strong>
                  <span class="text-content">{{ record.originalText }}</span>
                </div>
                
                <div v-if="record.modifiedText" class="modified-text">
                  <strong>修改：</strong>
                  <span class="text-content">{{ record.modifiedText }}</span>
                </div>
              </div>
              
              <div v-if="record.reason" class="reason">
                <strong>原因：</strong>{{ record.reason }}
              </div>
            </div>
            
            <div class="item-actions">
              <el-button 
                @click="previewChange(record)"
                size="small"
                type="text"
              >
                预览
              </el-button>
              
              <el-button 
                @click="revertToThis(record)"
                size="small"
                type="text"
                :disabled="!canRevertTo(record)"
              >
                回滚到此
              </el-button>
              
              <el-button 
                @click="copyChange(record)"
                size="small"
                type="text"
              >
                复制
              </el-button>
            </div>
          </div>
        </el-timeline-item>
      </el-timeline>
      
      <!-- 加载更多 -->
      <div v-if="hasMore" class="load-more">
        <el-button @click="loadMore" :loading="loading">
          加载更多
        </el-button>
      </div>
      
      <!-- 空状态 -->
      <el-empty 
        v-if="!historyRecords.length && !loading"
        description="暂无修改历史"
      />
    </div>

    <!-- 统计对话框 -->
    <el-dialog
      v-model="showStatistics"
      title="修改统计"
      width="600px"
    >
      <div class="statistics-content">
        <el-row :gutter="16">
          <el-col :span="12">
            <el-statistic title="总修改次数" :value="statistics.totalModifications" />
          </el-col>
          <el-col :span="12">
            <el-statistic title="今日修改" :value="statistics.todayModifications" />
          </el-col>
        </el-row>
        
        <el-row :gutter="16" style="margin-top: 20px;">
          <el-col :span="12">
            <el-statistic title="平均修改长度" :value="statistics.averageChangeLength" suffix="字" />
          </el-col>
          <el-col :span="12">
            <el-statistic title="最活跃时间" :value="`${statistics.mostActiveHour}:00`" />
          </el-col>
        </el-row>
        
        <div class="category-chart" style="margin-top: 20px;">
          <h4>修改类别分布</h4>
          <div class="category-bars">
            <div 
              v-for="(count, category) in statistics.categories"
              :key="category"
              class="category-bar"
            >
              <span class="category-name">{{ category }}</span>
              <div class="bar-container">
                <div 
                  class="bar-fill"
                  :style="{ width: `${(count / statistics.totalModifications) * 100}%` }"
                ></div>
                <span class="bar-count">{{ count }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 预览对话框 -->
    <el-dialog
      v-model="showPreview"
      title="修改预览"
      width="800px"
    >
      <div v-if="previewRecord" class="preview-content">
        <div class="preview-section">
          <h4>原文</h4>
          <div class="preview-text original">{{ previewRecord.originalText }}</div>
        </div>
        
        <div class="preview-section">
          <h4>修改后</h4>
          <div class="preview-text modified">{{ previewRecord.modifiedText }}</div>
        </div>
        
        <div v-if="previewRecord.reason" class="preview-section">
          <h4>修改原因</h4>
          <div class="preview-reason">{{ previewRecord.reason }}</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Clock, RefreshLeft, RefreshRight, More, Search, Edit
} from '@element-plus/icons-vue'
import { modificationHistoryService } from '../services/modificationHistoryService'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['undo', 'redo', 'revert-to', 'close'])

// 响应式数据
const historyRecords = ref([])
const statistics = ref({})
const loading = ref(false)
const hasMore = ref(false)
const currentOffset = ref(0)
const pageSize = 20

const showStatistics = ref(false)
const showPreview = ref(false)
const previewRecord = ref(null)

// 筛选条件
const filters = reactive({
  category: '',
  dateRange: null,
  searchText: ''
})

// 计算属性
const canUndo = computed(() => {
  const state = modificationHistoryService.getCurrentState()
  return state.canUndo
})

const canRedo = computed(() => {
  const state = modificationHistoryService.getCurrentState()
  return state.canRedo
})

// 方法
const loadHistory = async () => {
  loading.value = true
  
  try {
    const options = {
      limit: pageSize,
      offset: currentOffset.value,
      category: filters.category || null,
      searchText: filters.searchText || null
    }
    
    if (filters.dateRange && filters.dateRange.length === 2) {
      options.dateRange = {
        start: new Date(filters.dateRange[0]),
        end: new Date(filters.dateRange[1])
      }
    }
    
    const result = modificationHistoryService.getHistory(options)
    console.log('加载历史记录结果:', result)
    console.log('历史记录总数:', result.total)

    if (currentOffset.value === 0) {
      historyRecords.value = result.records
    } else {
      historyRecords.value.push(...result.records)
    }

    hasMore.value = result.hasMore
    statistics.value = modificationHistoryService.getStatistics()
    console.log('历史记录统计:', statistics.value)
  } catch (error) {
    console.error('加载历史记录失败:', error)
    ElMessage.error('加载历史记录失败')
  } finally {
    loading.value = false
  }
}

const loadMore = () => {
  currentOffset.value += pageSize
  loadHistory()
}

const refreshHistory = () => {
  currentOffset.value = 0
  loadHistory()
}

const undo = () => {
  const result = modificationHistoryService.undo()
  if (result) {
    emit('undo', result)
    refreshHistory()
    ElMessage.success('已撤销')
  }
}

const redo = () => {
  const result = modificationHistoryService.redo()
  if (result) {
    emit('redo', result)
    refreshHistory()
    ElMessage.success('已重做')
  }
}

const handleMenuCommand = (command) => {
  switch (command) {
    case 'export':
      exportHistory()
      break
    case 'statistics':
      showStatistics.value = true
      break
    case 'clear':
      clearHistory()
      break
  }
}

const exportHistory = () => {
  try {
    const data = modificationHistoryService.exportHistory('json')
    const blob = new Blob([data], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `modification_history_${new Date().toISOString().split('T')[0]}.json`
    a.click()
    URL.revokeObjectURL(url)
    ElMessage.success('历史记录已导出')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  }
}

const clearHistory = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有修改历史吗？此操作不可恢复。',
      '确认清空',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    modificationHistoryService.clearHistory()
    refreshHistory()
    ElMessage.success('历史记录已清空')
  } catch {
    // 用户取消
  }
}

const previewChange = (record) => {
  previewRecord.value = record
  showPreview.value = true
}

const revertToThis = async (record) => {
  try {
    await ElMessageBox.confirm(
      `确定要回滚到这个修改吗？这将撤销此修改之后的所有操作。`,
      '确认回滚',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    emit('revert-to', record)
    ElMessage.success('已回滚')
  } catch {
    // 用户取消
  }
}

const copyChange = (record) => {
  const text = `原文：${record.originalText}\n修改：${record.modifiedText}`
  navigator.clipboard.writeText(text).then(() => {
    ElMessage.success('已复制到剪贴板')
  }).catch(() => {
    ElMessage.error('复制失败')
  })
}

const canRevertTo = (record) => {
  // 简单实现：检查是否是当前记录之前的记录
  const currentState = modificationHistoryService.getCurrentState()
  return currentState.currentIndex >= 0
}

// 辅助方法
const formatTime = (timestamp) => {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now - date
  
  if (diff < 60000) { // 1分钟内
    return '刚刚'
  } else if (diff < 3600000) { // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) { // 24小时内
    return `${Math.floor(diff / 3600000)}小时前`
  } else {
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString()
  }
}

const getTimelineType = (record) => {
  const category = record.metadata.category
  const typeMap = {
    '新增内容': 'success',
    '删除内容': 'danger',
    '扩展内容': 'primary',
    '精简内容': 'warning',
    '微调修改': 'info'
  }
  return typeMap[category] || 'info'
}

const getTimelineIcon = (record) => {
  return Edit
}

const getCategoryColor = (category) => {
  const colorMap = {
    '新增内容': 'success',
    '删除内容': 'danger',
    '扩展内容': 'primary',
    '精简内容': 'warning',
    '微调修改': 'info',
    '文笔优化': 'success',
    '语言优化': 'primary',
    '情节调整': 'warning',
    '角色优化': 'info',
    '逻辑修正': 'danger'
  }
  return colorMap[category] || 'info'
}

// 监听筛选条件变化
watch([() => filters.category, () => filters.dateRange, () => filters.searchText], () => {
  currentOffset.value = 0
  loadHistory()
}, { deep: true })

// 生命周期
onMounted(() => {
  loadHistory()
})
</script>

<style scoped>
.modification-history {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--border-light);
  background: var(--bg-primary);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-left h3 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-primary);
}

.header-actions {
  display: flex;
  gap: 8px;
}

.history-filters {
  padding: 16px;
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-light);
}

.history-list {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.history-item {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: 16px;
  margin-bottom: 8px;
  transition: all 0.3s ease;
}

.history-item:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-paper-lg);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.change-stats {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--text-muted);
  font-size: 12px;
}

.item-content {
  margin-bottom: 12px;
}

.question {
  margin-bottom: 8px;
  padding: 8px;
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  font-size: 14px;
}

.text-changes {
  margin: 8px 0;
}

.original-text,
.modified-text {
  margin: 4px 0;
  font-size: 14px;
}

.text-content {
  display: inline-block;
  max-width: 100%;
  padding: 4px 8px;
  border-radius: var(--radius-sm);
  font-family: var(--font-mono);
  word-break: break-all;
}

.original-text .text-content {
  background: #fef2f2;
  color: #991b1b;
  border: 1px solid #fecaca;
}

.modified-text .text-content {
  background: #f0fdf4;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.reason {
  font-size: 13px;
  color: var(--text-muted);
  font-style: italic;
}

.item-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.load-more {
  text-align: center;
  padding: 20px;
}

.statistics-content {
  padding: 16px 0;
}

.category-chart {
  margin-top: 20px;
}

.category-bars {
  margin-top: 12px;
}

.category-bar {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  gap: 12px;
}

.category-name {
  min-width: 80px;
  font-size: 12px;
  color: var(--text-secondary);
}

.bar-container {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
}

.bar-fill {
  height: 8px;
  background: var(--primary-color);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.bar-count {
  font-size: 12px;
  color: var(--text-muted);
  min-width: 20px;
}

.preview-content {
  padding: 16px 0;
}

.preview-section {
  margin-bottom: 20px;
}

.preview-section h4 {
  margin: 0 0 8px 0;
  color: var(--text-primary);
}

.preview-text {
  padding: 12px;
  border-radius: var(--radius-md);
  font-family: var(--font-mono);
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-all;
}

.preview-text.original {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #991b1b;
}

.preview-text.modified {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  color: #166534;
}

.preview-reason {
  padding: 12px;
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  font-style: italic;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .history-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: center;
  }

  .item-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .item-actions {
    justify-content: flex-start;
  }
}
</style>
