<template>
  <div class="ink-auto-create">
    <!-- 页面标题 -->
    <div class="page-header elegant-card fade-in-up">
      <div class="header-content">
        <h1 class="page-title gradient-text purple">妙笔生花</h1>
        <p class="page-subtitle">AI全自动创作，让灵感如泉涌</p>
      </div>
      <div class="header-decoration floating">
        <div class="decoration-icon pulsing">
          <svg viewBox="0 0 24 24" fill="none">
            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
      </div>
    </div>

    <!-- AI状态检查 -->
    <div class="ai-status-card elegant-card fade-in-up">
      <div class="status-indicator" :class="aiStatusClass">
        <div class="status-icon floating">
          <svg v-if="isConfigured" viewBox="0 0 24 24" fill="none">
            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" stroke="currentColor" stroke-width="2"/>
            <polyline points="22,4 12,14.01 9,11.01" stroke="currentColor" stroke-width="2"/>
          </svg>
          <svg v-else viewBox="0 0 24 24" fill="none">
            <path d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
        <div class="status-info">
          <span class="status-title gradient-text" :class="{ 'jade': isConfigured, 'warm': !isConfigured }">{{ aiStatusText }}</span>
          <span class="status-desc">{{ aiStatusDesc }}</span>
        </div>
      </div>

      <div v-if="!isConfigured" class="config-notice">
        <p>请先前往文房设置配置AI服务</p>
        <div class="config-actions">
          <button class="config-btn elegant-btn" @click="$emit('navigate', 'settings')">
            前往配置
          </button>
          <button class="refresh-btn elegant-btn" @click="refreshAIConfig">
            刷新状态
          </button>
          <button class="debug-btn elegant-btn" @click="debugConfig">
            调试配置
          </button>
        </div>
      </div>

      <div v-if="isConfigured" class="test-section">
        <div class="test-actions">
          <span class="test-desc">AI服务已就绪，可以开始创作</span>
        </div>
      </div>
    </div>

    <!-- 创作表单 -->
    <div v-if="isConfigured" class="create-form-section">
      <div class="form-card glass-card fade-in-up">
        <h2 class="form-title gradient-text">创作设定</h2>
        
        <div class="create-form">
          <div class="form-item">
            <label class="form-label">项目名称</label>
            <input 
              v-model="createForm.title" 
              type="text"
              placeholder="请输入小说名称"
              class="form-input"
            />
          </div>
          
          <div class="form-item">
            <label class="form-label">题材类型</label>
            <select v-model="createForm.genre" class="form-select">
              <option value="">请选择题材</option>
              <option value="玄幻">玄幻</option>
              <option value="都市">都市</option>
              <option value="科幻">科幻</option>
              <option value="历史">历史</option>
              <option value="军事">军事</option>
              <option value="游戏">游戏</option>
            </select>
          </div>
          
          <div class="form-item">
            <label class="form-label">读者群体</label>
            <select v-model="createForm.category" class="form-select">
              <option value="">请选择类型</option>
              <option value="男频">男频</option>
              <option value="女频">女频</option>
            </select>
          </div>
          
          <div class="form-item">
            <label class="form-label">创作要求</label>
            <textarea
              v-model="createForm.requirements"
              rows="4"
              placeholder="请描述您的创作要求，如：主角设定、故事背景、情节要求等..."
              class="form-textarea"
            ></textarea>
          </div>

          <div class="form-item">
            <label class="form-label">创意灵感</label>
            <textarea
              v-model="createForm.creativity"
              rows="3"
              placeholder="请输入您的奇思妙想和创意，如：我觉醒反派系统当打脸主角就会变强、我能看到别人的好感度、我有读心术等..."
              class="form-textarea creativity-input"
            ></textarea>
            <div class="creativity-hint">
              <svg viewBox="0 0 24 24" fill="none">
                <circle cx="12" cy="12" r="5" stroke="currentColor" stroke-width="2"/>
                <line x1="12" y1="1" x2="12" y2="3" stroke="currentColor" stroke-width="2"/>
                <line x1="12" y1="21" x2="12" y2="23" stroke="currentColor" stroke-width="2"/>
                <line x1="4.22" y1="4.22" x2="5.64" y2="5.64" stroke="currentColor" stroke-width="2"/>
                <line x1="18.36" y1="18.36" x2="19.78" y2="19.78" stroke="currentColor" stroke-width="2"/>
                <line x1="1" y1="12" x2="3" y2="12" stroke="currentColor" stroke-width="2"/>
                <line x1="21" y1="12" x2="23" y2="12" stroke="currentColor" stroke-width="2"/>
                <line x1="4.22" y1="19.78" x2="5.64" y2="18.36" stroke="currentColor" stroke-width="2"/>
                <line x1="18.36" y1="5.64" x2="19.78" y2="4.22" stroke="currentColor" stroke-width="2"/>
              </svg>
              <span>这里是您发挥创意的地方！任何脑洞都可以，AI会基于您的创意设计整个故事</span>
            </div>
          </div>

          <div class="form-row">
            <div class="form-item">
              <label class="form-label">总章节数</label>
              <input
                type="number"
                v-model="createForm.chapterCount"
                min="10"
                max="2000"
                step="10"
                class="form-input number-input"
              />
              <span class="form-hint">{{ getChapterCountTip(createForm.chapterCount) }}</span>
            </div>

            <div class="form-item">
              <label class="form-label">单章字数</label>
              <input
                type="number"
                v-model="createForm.wordsPerChapter"
                min="1000"
                max="8000"
                step="500"
                class="form-input number-input"
              />
              <span class="form-hint">{{ getWordCountTip(createForm.wordsPerChapter) }}</span>
            </div>
          </div>
        </div>
        
        <div class="form-actions">
          <button
            class="create-btn primary neumorphism-btn"
            @click="startAutoCreate"
            :disabled="!canCreate"
          >
            <svg viewBox="0 0 24 24" fill="none">
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" stroke="currentColor" stroke-width="2"/>
            </svg>
            开始创作
          </button>

          <button class="create-btn secondary neumorphism-btn" @click="resetForm">
            <svg viewBox="0 0 24 24" fill="none">
              <path d="M1 4v6h6M23 20v-6h-6" stroke="currentColor" stroke-width="2"/>
              <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15" stroke="currentColor" stroke-width="2"/>
            </svg>
            重置表单
          </button>
        </div>
      </div>
    </div>

    <!-- 创作进度 -->
    <div v-if="isCreating" class="progress-section fade-in-up">
      <div class="progress-card elegant-card">
        <h3 class="progress-title gradient-text purple">{{ currentStep }}</h3>
        <div class="progress-content">
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: createProgress + '%' }"></div>
          </div>
          <div class="progress-info">
            <span class="progress-text">{{ progressText }}</span>
            <span class="progress-percent">{{ createProgress }}%</span>
          </div>
        </div>

        <!-- 创作步骤 -->
        <div class="creation-steps">
          <div
            v-for="(step, index) in creationSteps"
            :key="index"
            class="step-item"
            :class="{
              'active': index === currentStepIndex,
              'completed': index < currentStepIndex,
              'pending': index > currentStepIndex
            }"
          >
            <div class="step-icon">
              <svg v-if="index < currentStepIndex" viewBox="0 0 24 24" fill="none">
                <polyline points="20,6 9,17 4,12" stroke="currentColor" stroke-width="2"/>
              </svg>
              <svg v-else-if="index === currentStepIndex" viewBox="0 0 24 24" fill="none" class="spinning">
                <path d="M21 12a9 9 0 11-6.219-8.56" stroke="currentColor" stroke-width="2"/>
              </svg>
              <span v-else>{{ index + 1 }}</span>
            </div>
            <span class="step-text">{{ step.name }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 创作结果 -->
    <div v-if="creationResult" class="result-section fade-in-up">
      <div class="result-card elegant-card">
        <div class="result-header">
          <h3 class="result-title gradient-text jade">创作完成！</h3>
          <div class="result-actions">
            <button class="result-btn primary" @click="saveProject">
              <svg viewBox="0 0 24 24" fill="none">
                <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z" stroke="currentColor" stroke-width="2"/>
                <polyline points="17,21 17,13 7,13 7,21" stroke="currentColor" stroke-width="2"/>
                <polyline points="7,3 7,8 15,8" stroke="currentColor" stroke-width="2"/>
              </svg>
              保存项目
            </button>
            <button class="result-btn secondary" @click="exportResult">
              <svg viewBox="0 0 24 24" fill="none">
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" stroke="currentColor" stroke-width="2"/>
                <polyline points="7,10 12,15 17,10" stroke="currentColor" stroke-width="2"/>
                <line x1="12" y1="15" x2="12" y2="3" stroke="currentColor" stroke-width="2"/>
              </svg>
              导出文档
            </button>
            <button class="result-btn secondary" @click="continueEdit">
              <svg viewBox="0 0 24 24" fill="none">
                <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" stroke="currentColor" stroke-width="2"/>
                <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" stroke="currentColor" stroke-width="2"/>
              </svg>
              继续编辑
            </button>
          </div>
        </div>

        <div class="result-content">
          <div class="result-tabs">
            <div class="tab-nav">
              <button 
                v-for="tab in resultTabs" 
                :key="tab.key"
                class="tab-btn"
                :class="{ active: activeResultTab === tab.key }"
                @click="activeResultTab = tab.key"
              >
                {{ tab.label }}
              </button>
            </div>

            <div class="tab-content">
              <!-- 故事大纲 -->
              <div v-if="activeResultTab === 'outline'" class="content-panel">
                <pre class="content-text">{{ creationResult.outline }}</pre>
              </div>

              <!-- 角色设定 -->
              <div v-if="activeResultTab === 'characters'" class="content-panel">
                <div v-for="(character, index) in creationResult.characters" :key="index" class="character-card">
                  <h4>{{ character.name }}</h4>
                  <pre class="content-text">{{ character.description }}</pre>
                </div>
              </div>

              <!-- 章节内容 -->
              <div v-if="activeResultTab === 'chapters'" class="content-panel">
                <div v-for="(chapter, index) in creationResult.chapters" :key="index" class="chapter-card">
                  <h4>第{{ index + 1 }}章 {{ chapter.title }}</h4>
                  <pre class="content-text">{{ chapter.content }}</pre>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { aiCreationService } from '../services/aiCreationService.js'

export default {
  name: 'MiaoBiShengHua',
  emits: ['navigate'],
  setup(props, { emit }) {
    // 响应式数据
    const isConfigured = ref(false)
    const isCreating = ref(false)
    const createProgress = ref(0)
    const currentStepIndex = ref(0)
    const activeResultTab = ref('outline')
    const creationResult = ref(null)

    // 表单数据
    const createForm = reactive({
      title: '',
      genre: '',
      category: '',
      requirements: '',
      creativity: '',
      chapterCount: 50,
      wordsPerChapter: 3000
    })

    // 创作步骤 - 完整迁移自NOVEL项目
    const creationSteps = ref([
      { name: '分析需求', key: 'analyze' },
      { name: '总体规划', key: 'outline' },
      { name: '创建角色', key: 'characters' },
      { name: '分卷规划', key: 'volumes' },
      { name: '章节概要', key: 'chapters' },
      { name: '完善内容', key: 'polish' }
    ])

    // 结果标签页
    const resultTabs = ref([
      { key: 'outline', label: '故事大纲' },
      { key: 'characters', label: '角色设定' },
      { key: 'chapters', label: '章节内容' }
    ])

    // 计算属性
    const aiStatusClass = computed(() => ({
      'status-ready': isConfigured.value,
      'status-warning': !isConfigured.value
    }))

    const aiStatusText = computed(() =>
      isConfigured.value ? 'AI笔墨就绪' : '待配置笔墨'
    )

    const aiStatusDesc = computed(() =>
      isConfigured.value ? '可以开始自动创作' : '需要配置API Key'
    )

    const canCreate = computed(() => 
      isConfigured.value && 
      createForm.title && 
      createForm.genre && 
      createForm.category && 
      createForm.requirements
    )

    const currentStep = computed(() => 
      creationSteps.value[currentStepIndex.value]?.name || '准备中'
    )

    const progressText = computed(() => 
      creationSteps.value[currentStepIndex.value]?.desc || '正在准备'
    )

    // 方法
    // 刷新AI配置状态 - 完整迁移自NOVEL项目
    const refreshAIConfig = () => {
      isConfigured.value = aiCreationService.isConfigured()
    }



    const getChapterCountTip = (count) => {
      if (count < 30) return '短篇小说'
      if (count < 100) return '中篇小说'
      if (count < 500) return '长篇小说'
      return '超长篇小说'
    }

    const getWordCountTip = (words) => {
      if (words < 2000) return '简洁风格'
      if (words < 4000) return '标准长度'
      if (words < 6000) return '详细描述'
      return '超详细风格'
    }

    const resetForm = () => {
      Object.assign(createForm, {
        title: '',
        genre: '',
        category: '',
        requirements: '',
        creativity: '',
        chapterCount: 50,
        wordsPerChapter: 3000
      })
    }

    // 开始自动创作 - 完整迁移自NOVEL项目
    const startAutoCreate = async () => {
      if (!canCreate.value) {
        alert('请填写完整的创作信息')
        return
      }

      if (!isConfigured.value) {
        alert('请先配置AI服务')
        return
      }

      try {
        isCreating.value = true
        createProgress.value = 0
        currentStepIndex.value = 0
        creationResult.value = null

        // 步骤1: 分析需求
        currentStepIndex.value = 0
        createProgress.value = 10
        await new Promise(resolve => setTimeout(resolve, 1000))

        // 统一使用分层规划流程，不再区分章节数量
        const creationData = await createLayeredOutline()

        await new Promise(resolve => setTimeout(resolve, 1000))
        createProgress.value = 100

        // 保存结果（使用createLayeredOutline返回的数据）
        creationResult.value = creationData

        console.log('Creation result:', creationResult.value)
        alert('创作完成！')

      } catch (error) {
        console.error('创作失败:', error)
        alert(`创作失败: ${error.message}`)
      } finally {
        isCreating.value = false
      }
    }

    // 统一的分层规划流程（适用于所有章节数量）- 完整迁移自NOVEL项目
    const createLayeredOutline = async () => {
      // 步骤2: 总体规划
      currentStepIndex.value = 1
      createProgress.value = 20

      const outline = await aiCreationService.generateOutline(createForm, (partialContent) => {
        console.log('Layered outline generation progress:', partialContent.length, 'characters')
      })
      createProgress.value = 40

      // 步骤3: 创建角色设定
      currentStepIndex.value = 2
      createProgress.value = 50

      const characters = await generateCharacters()
      createProgress.value = 70

      // 步骤4: 分卷规划提示
      currentStepIndex.value = 3
      createProgress.value = 80

      // 步骤5: 章节概要提示
      currentStepIndex.value = 4
      createProgress.value = 90

      // 步骤6: 完善内容
      currentStepIndex.value = 5
      createProgress.value = 95

      await new Promise(resolve => setTimeout(resolve, 1000))
      createProgress.value = 100

      // 构建结果数据
      const resultData = {
        outline,
        characters,
        chapters: [], // 大型网文不预生成章节内容
        isLayered: true, // 标记为分层规划
        volumeInfo: {
          totalChapters: createForm.chapterCount,
          estimatedVolumes: Math.ceil(createForm.chapterCount / 100),
          message: '已完成总体规划，可按需生成具体卷的详细章节概要'
        },
        metadata: {
          title: createForm.title,
          genre: createForm.genre,
          category: createForm.category,
          chapterCount: createForm.chapterCount,
          wordsPerChapter: createForm.wordsPerChapter,
          createdAt: new Date().toISOString()
        }
      }

      console.log('Creation result:', resultData)
      alert('分层规划完成！')

      // 返回结果数据
      return resultData
    }

    // 生成角色设定 - 完整迁移自NOVEL项目
    const generateCharacters = async () => {
      const characters = []
      const characterRoles = ['主角', '女主角', '反派', '配角']

      for (const role of characterRoles) {
        try {
          const character = await aiCreationService.generateCharacter({
            name: `${role}（待命名）`,
            role,
            genre: createForm.genre,
            background: createForm.requirements
          }, (partialContent) => {
            // 实时更新角色设定
            console.log(`${role} character generation progress:`, partialContent.length, 'characters')
          })

          characters.push({
            name: role,
            description: character
          })
        } catch (error) {
          console.error(`生成${role}失败:`, error)
        }
      }

      return characters
    }



    const saveProject = () => {
      alert('项目保存功能')
    }

    const exportResult = () => {
      alert('导出文档功能')
    }

    const continueEdit = () => {
      alert('继续编辑功能')
    }

    // 生命周期
    onMounted(() => {
      refreshAIConfig()
    })

    return {
      isConfigured,
      isCreating,
      createProgress,
      currentStepIndex,
      activeResultTab,
      creationResult,
      createForm,
      creationSteps,
      resultTabs,
      aiStatusClass,
      aiStatusText,
      aiStatusDesc,
      canCreate,
      currentStep,
      progressText,
      refreshAIConfig,
      getChapterCountTip,
      getWordCountTip,
      resetForm,
      startAutoCreate,
      saveProject,
      exportResult,
      continueEdit
    }
  }
}
</script>

<style scoped>
.ink-auto-create {
  padding: 24px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  overflow-y: auto;
  height: 100vh;
}

/* 页面标题 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding: 32px 40px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.3);
  max-width: 1000px;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 32px;
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: 36px;
  font-weight: 800;
  margin: 0 0 12px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.5px;
}

.page-subtitle {
  font-size: 18px;
  color: #64748b;
  margin: 0;
  font-weight: 400;
  opacity: 0.8;
}

.header-decoration {
  width: 70px;
  height: 70px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  animation: float 3s ease-in-out infinite;
}

.decoration-icon svg {
  width: 32px;
  height: 32px;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.pulsing {
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* AI状态卡片 */
.ai-status-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 32px;
  margin-bottom: 32px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.3);
  max-width: 1000px;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 32px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 24px;
}

.status-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.status-ready .status-icon {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.status-warning .status-icon {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.status-icon svg {
  width: 24px;
  height: 24px;
}

.status-info {
  flex: 1;
}

.status-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.status-title.jade {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.status-title.warm {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.status-desc {
  font-size: 14px;
  color: #64748b;
}

.config-notice {
  text-align: center;
  padding: 24px;
  background: rgba(249, 115, 22, 0.05);
  border-radius: 16px;
  border: 1px solid rgba(249, 115, 22, 0.1);
}

.config-notice p {
  margin: 0 0 20px 0;
  color: #92400e;
  font-size: 16px;
}

.config-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
}

.test-section {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
}

.test-desc {
  font-size: 14px;
  color: #64748b;
}

/* 按钮样式 */
.elegant-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.config-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.25);
}

.config-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.35);
}

.refresh-btn {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  color: #64748b;
  border: 1px solid rgba(148, 163, 184, 0.2);
}

.refresh-btn:hover {
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
  color: #475569;
  transform: translateY(-2px);
}

.test-btn {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  box-shadow: 0 6px 20px rgba(79, 172, 254, 0.25);
}



.test-btn:disabled {
  background: linear-gradient(135deg, #cbd5e1 0%, #94a3b8 100%);
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 2px 8px rgba(148, 163, 184, 0.2);
}

/* 创作表单 */
.create-form-section {
  max-width: 1000px;
  margin: 0 auto 32px auto;
}

.form-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 36px 40px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.form-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #667eea, #764ba2, #4facfe, #00f2fe);
  background-size: 200% 100%;
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { background-position: 200% 0; }
  50% { background-position: -200% 0; }
}

.form-title {
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 32px 0;
  text-align: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.create-form {
  max-width: 800px;
  margin: 0 auto;
}

.form-item {
  margin-bottom: 28px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.form-label {
  display: block;
  margin-bottom: 10px;
  font-size: 15px;
  font-weight: 600;
  color: #1e293b;
  letter-spacing: -0.1px;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 14px 18px;
  border: 2px solid rgba(148, 163, 184, 0.2);
  border-radius: 14px;
  background: rgba(255, 255, 255, 0.9);
  font-size: 15px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 500;
  color: #1e293b;
  font-family: inherit;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.12);
  background: rgba(255, 255, 255, 1);
  transform: translateY(-1px);
}

.form-input:hover,
.form-select:hover,
.form-textarea:hover {
  border-color: rgba(102, 126, 234, 0.3);
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: #94a3b8;
  font-weight: 400;
}

.number-input {
  max-width: 200px;
}

.form-hint {
  display: block;
  margin-top: 8px;
  font-size: 13px;
  color: #64748b;
  font-weight: 500;
}

.creativity-input {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.02) 0%, rgba(116, 75, 162, 0.02) 100%);
  border-color: rgba(102, 126, 234, 0.2);
}

.creativity-hint {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 12px;
  padding: 12px 16px;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 12px;
  font-size: 14px;
  color: #667eea;
}

.creativity-hint svg {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

/* 表单按钮 */
.form-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-top: 40px;
  padding-top: 32px;
  border-top: 1px solid rgba(148, 163, 184, 0.1);
}

.create-btn {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 16px 32px;
  border: none;
  border-radius: 14px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.create-btn svg {
  width: 18px;
  height: 18px;
}

.create-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.create-btn.primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
}

.create-btn.primary:disabled {
  background: linear-gradient(135deg, #cbd5e1 0%, #94a3b8 100%);
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 2px 8px rgba(148, 163, 184, 0.2);
}

.create-btn.secondary {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  color: #64748b;
  border: 1px solid rgba(148, 163, 184, 0.2);
}

.create-btn.secondary:hover {
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
  color: #475569;
  transform: translateY(-2px);
}



/* 进度区域 */
.progress-section {
  max-width: 1000px;
  margin: 0 auto 32px auto;
}

.progress-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 36px 40px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.progress-title {
  font-size: 24px;
  font-weight: 700;
  margin: 0 0 24px 0;
  text-align: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.progress-content {
  margin-bottom: 32px;
}

.progress-bar {
  width: 100%;
  height: 12px;
  background: rgba(148, 163, 184, 0.2);
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 16px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 6px;
  transition: width 0.3s ease;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-text {
  font-size: 16px;
  color: #64748b;
  font-weight: 500;
}

.progress-percent {
  font-size: 18px;
  font-weight: 700;
  color: #667eea;
}

/* 创作步骤 */
.creation-steps {
  display: flex;
  justify-content: space-between;
  gap: 16px;
}

.step-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 20px 16px;
  border-radius: 16px;
  transition: all 0.3s ease;
}

.step-item.completed {
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.step-item.active {
  background: rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.2);
}

.step-item.pending {
  background: rgba(148, 163, 184, 0.05);
  border: 1px solid rgba(148, 163, 184, 0.1);
}

.step-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 16px;
}

.step-item.completed .step-icon {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.step-item.active .step-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.step-item.pending .step-icon {
  background: rgba(148, 163, 184, 0.2);
  color: #64748b;
}

.step-icon svg {
  width: 20px;
  height: 20px;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.step-text {
  font-size: 14px;
  font-weight: 600;
  text-align: center;
}

.step-item.completed .step-text {
  color: #059669;
}

.step-item.active .step-text {
  color: #667eea;
}

.step-item.pending .step-text {
  color: #64748b;
}

/* 结果区域 */
.result-section {
  max-width: 1000px;
  margin: 0 auto;
}

.result-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 36px 40px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
}

.result-title {
  font-size: 28px;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.result-actions {
  display: flex;
  gap: 12px;
}

.result-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.result-btn svg {
  width: 16px;
  height: 16px;
}

.result-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.25);
}

.result-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.35);
}

.result-btn.secondary {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  color: #64748b;
  border: 1px solid rgba(148, 163, 184, 0.2);
}

.result-btn.secondary:hover {
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
  color: #475569;
  transform: translateY(-2px);
}

/* 结果标签页 */
.result-tabs {
  width: 100%;
}

.tab-nav {
  display: flex;
  gap: 4px;
  margin-bottom: 24px;
  background: rgba(148, 163, 184, 0.1);
  border-radius: 12px;
  padding: 4px;
}

.tab-btn {
  flex: 1;
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  background: transparent;
  color: #64748b;
}

.tab-btn.active {
  background: white;
  color: #667eea;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tab-btn:hover:not(.active) {
  background: rgba(255, 255, 255, 0.5);
}

.tab-content {
  min-height: 400px;
}

.content-panel {
  padding: 20px;
  background: rgba(248, 250, 252, 0.5);
  border-radius: 16px;
  border: 1px solid rgba(148, 163, 184, 0.1);
}

.content-text {
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.6;
  color: #1e293b;
  white-space: pre-wrap;
  margin: 0;
}

.character-card,
.chapter-card {
  margin-bottom: 24px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  border: 1px solid rgba(148, 163, 184, 0.1);
}

.character-card h4,
.chapter-card h4 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 700;
  color: #667eea;
}

/* 动画 */
.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.elegant-card {
  position: relative;
  overflow: hidden;
}

.elegant-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #667eea, #764ba2, #4facfe, #00f2fe);
  background-size: 200% 100%;
  animation: shimmer 3s ease-in-out infinite;
}

.floating {
  animation: float 3s ease-in-out infinite;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ink-auto-create {
    padding: 16px;
  }

  .page-header,
  .ai-status-card,
  .form-card,
  .progress-card,
  .result-card {
    padding: 24px 20px;
    margin-bottom: 20px;
  }

  .page-title {
    font-size: 28px;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .form-actions,
  .result-actions {
    flex-direction: column;
    gap: 12px;
  }

  .create-btn,
  .result-btn {
    width: 100%;
    justify-content: center;
  }

  .creation-steps {
    flex-direction: column;
    gap: 12px;
  }

  .step-item {
    flex-direction: row;
    text-align: left;
  }

  .config-actions {
    flex-direction: column;
    gap: 12px;
  }

  .elegant-btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .page-header {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }

  .header-decoration {
    order: -1;
  }

  .result-header {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .tab-nav {
    flex-direction: column;
  }
}
</style>
