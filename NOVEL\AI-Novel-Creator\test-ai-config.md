# AI配置功能测试指南

## 🎯 测试目标

验证以下两个修复：
1. **刷新模型后下拉列表正确更新**
2. **Google Gemini默认选择gemini-2.0-flash-exp**

## 📋 测试步骤

### 1. 测试默认模型选择

1. 启动应用，进入"文房设置"页面
2. 在AI服务配置中，选择"Google Gemini"
3. **预期结果**：
   - API地址自动填充为：`https://generativelanguage.googleapis.com/v1beta`
   - 模型自动选择为：`gemini-2.0-flash-exp`（Gemini 2.0 Flash 实验版）

### 2. 测试模型列表刷新

1. 在AI服务配置中，确保已选择"Google Gemini"
2. 点击"刷新模型列表"按钮
3. **预期结果**：
   - 按钮显示加载状态
   - 如果没有API Key：显示"使用预定义模型列表（未提供API Key）"
   - 如果有API Key：尝试从API获取，失败时回退到预定义列表
   - 模型下拉列表应该更新显示最新的模型

### 3. 测试模型信息显示

1. 选择任意AI服务商
2. 点击"模型信息"按钮
3. **预期结果**：
   - 弹出对话框显示详细的模型信息
   - 包含模型来源（API动态获取 或 预定义列表）
   - 显示每个模型的详细信息：名称、ID、描述、最大上下文

### 4. 测试不同服务商的模型

1. 依次选择不同的AI服务商：
   - Google Gemini
   - OpenAI GPT
   - Anthropic Claude
   - 百度文心
   - 阿里通义

2. **预期结果**：
   - 每个服务商都有对应的模型列表
   - 推荐模型标有"推荐"标签
   - 模型信息完整显示

## 🔍 验证要点

### Gemini模型列表应包含：
- ✅ **gemini-2.0-flash-exp** (推荐，默认选择)
- ✅ **gemini-1.5-pro** (推荐)
- ✅ **gemini-1.5-flash**
- ✅ **gemini-1.5-flash-8b**
- ✅ **gemini-pro**
- ✅ **gemini-pro-vision**

### 刷新功能验证：
- ✅ 无API Key时使用预定义列表
- ✅ 有API Key时尝试API获取
- ✅ API失败时回退到预定义列表
- ✅ 模型列表实时更新
- ✅ 当前选择的模型如果不存在，自动选择推荐模型

### 用户体验验证：
- ✅ 操作流畅，无卡顿
- ✅ 错误提示友好
- ✅ 加载状态清晰
- ✅ 模型信息详细准确

## 🐛 可能的问题

1. **模型列表不更新**：检查dynamicModels是否正确更新
2. **默认模型不正确**：检查onProviderChange逻辑
3. **API调用失败**：检查网络和API Key
4. **界面显示异常**：检查Vue响应式数据更新

## 📝 测试记录

请在测试时记录：
- [ ] 默认模型选择是否正确
- [ ] 刷新功能是否工作
- [ ] 模型信息是否准确
- [ ] 用户体验是否良好
- [ ] 发现的任何问题

---

**测试完成后，AI配置功能应该完全满足用户需求！** 🎉
