<template>
  <div class="elegant-sidebar" :class="{ 'collapsed': isCollapsed }">
    <!-- 侧边栏头部 -->
    <div class="sidebar-header">
      <div class="brand-section">
        <div class="brand-icon">
          <div class="icon-container">
            <el-icon><EditPen /></el-icon>
          </div>
          <div class="icon-ripple"></div>
        </div>
        <div class="brand-info" v-show="!isCollapsed">
          <h1 class="brand-title">墨韵</h1>
          <p class="brand-subtitle">AI创作工坊</p>
        </div>
      </div>

      <el-button
        class="collapse-toggle"
        @click="toggleCollapse"
        :icon="isCollapsed ? ArrowRight : ArrowLeft"
        circle
        size="small"
      />
    </div>

    <!-- 用户信息卡片 -->
    <div class="user-card" v-show="!isCollapsed">
      <div class="user-avatar">
        <div class="avatar-ring">
          <el-icon><User /></el-icon>
        </div>
      </div>
      <div class="user-info">
        <div class="user-name">创作者</div>
        <div class="user-status">
          <div class="status-dot" :class="{ 'online': isAIConnected }"></div>
          <span class="status-text">{{ isAIConnected ? 'AI已就绪' : 'AI离线' }}</span>
        </div>
      </div>
    </div>

    <!-- 导航菜单 -->
    <nav class="navigation">
      <!-- 主要功能 -->
      <div class="nav-section">
        <div class="section-label" v-show="!isCollapsed">主要功能</div>
        <div class="nav-items">
          <router-link
            to="/dashboard"
            class="nav-item"
            :class="{ 'active': $route.path === '/dashboard' }"
          >
            <div class="nav-icon">
              <el-icon><House /></el-icon>
            </div>
            <span class="nav-text" v-show="!isCollapsed">工作台</span>
            <div class="nav-badge" v-show="!isCollapsed">
              <span class="badge-dot"></span>
            </div>
          </router-link>

          <router-link
            to="/projects"
            class="nav-item"
            :class="{ 'active': $route.path === '/projects' }"
          >
            <div class="nav-icon">
              <el-icon><Folder /></el-icon>
            </div>
            <span class="nav-text" v-show="!isCollapsed">项目管理</span>
            <div class="nav-count" v-show="!isCollapsed && projectCount > 0">
              {{ projectCount }}
            </div>
          </router-link>
        </div>
      </div>

      <!-- 创作工具 -->
      <div class="nav-section">
        <div class="section-label" v-show="!isCollapsed">创作工具</div>
        <div class="nav-items">
          <router-link
            to="/auto-create"
            class="nav-item"
            :class="{ 'active': $route.path === '/auto-create' }"
          >
            <div class="nav-icon">
              <el-icon><MagicStick /></el-icon>
            </div>
            <span class="nav-text" v-show="!isCollapsed">智能创作</span>
            <div class="nav-tag" v-show="!isCollapsed">
              <span class="tag-text">AI</span>
            </div>
          </router-link>

          <router-link
            to="/editor"
            class="nav-item"
            :class="{ 'active': $route.path === '/editor' }"
          >
            <div class="nav-icon">
              <el-icon><Edit /></el-icon>
            </div>
            <span class="nav-text" v-show="!isCollapsed">文本编辑器</span>
          </router-link>
        </div>
      </div>

      <!-- 系统设置 -->
      <div class="nav-section">
        <div class="section-label" v-show="!isCollapsed">系统</div>
        <div class="nav-items">
          <router-link
            to="/settings"
            class="nav-item"
            :class="{ 'active': $route.path === '/settings' }"
          >
            <div class="nav-icon">
              <el-icon><Setting /></el-icon>
            </div>
            <span class="nav-text" v-show="!isCollapsed">设置</span>
          </router-link>
        </div>
      </div>
    </nav>

    <!-- 底部快捷操作 -->
    <div class="sidebar-footer">
      <div class="quick-actions" v-show="!isCollapsed">
        <el-button
          class="action-btn"
          @click="createNewProject"
          type="primary"
          size="small"
        >
          <el-icon><Plus /></el-icon>
          新建项目
        </el-button>
      </div>

      <!-- 统计信息 -->
      <div class="stats-mini" v-show="!isCollapsed">
        <div class="stat-item">
          <span class="stat-label">今日字数</span>
          <span class="stat-value">{{ todayWords }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">总项目</span>
          <span class="stat-value">{{ projectCount }}</span>
        </div>
      </div>
    </div>
  </div>
</template>


</template>

<script>
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAppStore } from '@/store/app'

export default {
  name: 'ElegantSidebar',
  setup() {
    const route = useRoute()
    const router = useRouter()
    const appStore = useAppStore()

    const isCollapsed = ref(false)

    // 计算属性
    const isAIConnected = computed(() => appStore.isConfigured)
    const projectCount = computed(() => appStore.projects?.length || 0)
    const todayWords = computed(() => appStore.todayWords || 0)

    // 方法
    const toggleCollapse = () => {
      isCollapsed.value = !isCollapsed.value
    }

    const createNewProject = () => {
      router.push('/projects?action=create')
    }

    return {
      isCollapsed,
      isAIConnected,
      projectCount,
      todayWords,
      toggleCollapse,
      createNewProject
    }
  }
}
</script>

<style scoped>
/* 优雅侧边栏 */
.elegant-sidebar {
  position: fixed;
  left: 0;
  top: 0;
  width: 280px;
  height: 100vh;
  background: var(--ink-deepest);
  color: var(--paper-pure);
  display: flex;
  flex-direction: column;
  transition: all var(--duration-slow) var(--ease-out-quart);
  z-index: 1000;
  box-shadow: var(--shadow-paper-xl);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.elegant-sidebar.collapsed {
  width: 80px;
}

/* 侧边栏头部 */
.sidebar-header {
  padding: var(--spacing-xl) var(--spacing-lg);
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 80px;
}

/* 品牌区域 */
.brand-section {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.brand-icon {
  position: relative;
  width: 48px;
  height: 48px;
}

.icon-container {
  width: 100%;
  height: 100%;
  background: var(--accent-warm);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: var(--paper-pure);
  box-shadow: var(--shadow-paper-md);
  transition: all var(--duration-normal) var(--ease-out-quart);
  position: relative;
  z-index: 2;
}

.icon-ripple {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--accent-warm);
  border-radius: var(--radius-xl);
  opacity: 0.3;
  animation: ripple 2s ease-in-out infinite;
}

@keyframes ripple {
  0% {
    transform: scale(1);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.1;
  }
  100% {
    transform: scale(1);
    opacity: 0.3;
  }
}

.brand-info {
  display: flex;
  flex-direction: column;
}

.brand-title {
  font-size: 24px;
  font-weight: var(--font-weight-bold);
  font-family: var(--font-display);
  margin: 0;
  color: var(--paper-pure);
  letter-spacing: -0.5px;
}

.brand-subtitle {
  font-size: 12px;
  color: rgba(252, 252, 252, 0.6);
  margin: 2px 0 0 0;
  font-weight: var(--font-weight-normal);
}

.collapse-toggle {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: var(--paper-pure);
  transition: all var(--duration-normal) var(--ease-out-quart);
}

.collapse-toggle:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

/* 用户信息卡片 */
.user-card {
  margin: var(--spacing-lg);
  padding: var(--spacing-lg);
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--radius-xl);
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  transition: all var(--duration-normal) var(--ease-out-quart);
}

.user-card:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
}

.user-avatar {
  position: relative;
}

.avatar-ring {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, var(--accent-warm) 0%, var(--accent-elegant) 100%);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: var(--paper-pure);
  box-shadow: var(--shadow-paper-sm);
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 14px;
  font-weight: var(--font-weight-semibold);
  color: var(--paper-pure);
  margin-bottom: 4px;
}

.user-status {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: var(--radius-full);
  background: var(--text-muted);
  transition: background var(--duration-fast) var(--ease-out-quart);
}

.status-dot.online {
  background: var(--accent-cool);
  box-shadow: 0 0 8px rgba(74, 103, 65, 0.5);
}

.status-text {
  font-size: 12px;
  color: rgba(252, 252, 252, 0.7);
}

/* 导航菜单 */
.navigation {
  flex: 1;
  padding: var(--spacing-lg) 0;
  overflow-y: auto;
}

.nav-section {
  margin-bottom: var(--spacing-2xl);
}

.section-label {
  font-size: 11px;
  font-weight: var(--font-weight-semibold);
  color: rgba(252, 252, 252, 0.5);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  padding: 0 var(--spacing-lg) var(--spacing-sm) var(--spacing-lg);
  margin-bottom: var(--spacing-sm);
}

.nav-items {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 0 var(--spacing-md);
}

.nav-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--radius-lg);
  color: rgba(252, 252, 252, 0.8);
  text-decoration: none;
  transition: all var(--duration-normal) var(--ease-out-quart);
  position: relative;
  overflow: hidden;
}

.nav-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: var(--accent-warm);
  transform: scaleY(0);
  transition: transform var(--duration-normal) var(--ease-out-quart);
}

.nav-item:hover {
  background: rgba(255, 255, 255, 0.08);
  color: var(--paper-pure);
  transform: translateX(4px);
}

.nav-item.active {
  background: rgba(212, 175, 55, 0.15);
  color: var(--paper-pure);
  border: 1px solid rgba(212, 175, 55, 0.3);
}

.nav-item.active::before {
  transform: scaleY(1);
}

.nav-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  flex-shrink: 0;
}

.nav-text {
  font-size: 14px;
  font-weight: var(--font-weight-medium);
  flex: 1;
}

.nav-badge {
  display: flex;
  align-items: center;
}

.badge-dot {
  width: 6px;
  height: 6px;
  background: var(--accent-cool);
  border-radius: var(--radius-full);
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(0.8);
  }
}

.nav-count {
  background: var(--accent-warm);
  color: var(--ink-deepest);
  font-size: 11px;
  font-weight: var(--font-weight-semibold);
  padding: 2px 6px;
  border-radius: var(--radius-full);
  min-width: 18px;
  text-align: center;
}

.nav-tag {
  background: rgba(74, 103, 65, 0.2);
  border: 1px solid rgba(74, 103, 65, 0.4);
  border-radius: var(--radius-sm);
  padding: 2px 6px;
}

.tag-text {
  font-size: 10px;
  font-weight: var(--font-weight-semibold);
  color: var(--accent-cool);
}

/* 侧边栏底部 */
.sidebar-footer {
  padding: var(--spacing-lg);
  border-top: 1px solid rgba(255, 255, 255, 0.08);
  margin-top: auto;
}

.quick-actions {
  margin-bottom: var(--spacing-lg);
}

.action-btn {
  width: 100%;
  height: 40px;
  background: var(--accent-warm);
  border: none;
  border-radius: var(--radius-lg);
  color: var(--ink-deepest);
  font-weight: var(--font-weight-semibold);
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  transition: all var(--duration-normal) var(--ease-out-quart);
  box-shadow: var(--shadow-paper-sm);
}

.action-btn:hover {
  background: #c49b2a;
  transform: translateY(-1px);
  box-shadow: var(--shadow-paper-md);
}

.stats-mini {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) 0;
}

.stat-label {
  font-size: 12px;
  color: rgba(252, 252, 252, 0.6);
  font-weight: var(--font-weight-normal);
}

.stat-value {
  font-size: 14px;
  color: var(--paper-pure);
  font-weight: var(--font-weight-semibold);
}

/* 收缩状态样式 */
.elegant-sidebar.collapsed .brand-info,
.elegant-sidebar.collapsed .user-card,
.elegant-sidebar.collapsed .section-label,
.elegant-sidebar.collapsed .nav-text,
.elegant-sidebar.collapsed .nav-badge,
.elegant-sidebar.collapsed .nav-count,
.elegant-sidebar.collapsed .nav-tag,
.elegant-sidebar.collapsed .quick-actions,
.elegant-sidebar.collapsed .stats-mini {
  opacity: 0;
  pointer-events: none;
}

.elegant-sidebar.collapsed .nav-item {
  justify-content: center;
  padding: var(--spacing-md);
}

.elegant-sidebar.collapsed .collapse-toggle {
  margin-left: auto;
  margin-right: auto;
}

/* 滚动条样式 */
.navigation::-webkit-scrollbar {
  width: 4px;
}

.navigation::-webkit-scrollbar-track {
  background: transparent;
}

.navigation::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
}

.navigation::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .elegant-sidebar {
    transform: translateX(-100%);
    transition: transform var(--duration-normal) var(--ease-out-quart);
  }

  .elegant-sidebar.mobile-open {
    transform: translateX(0);
  }
}
</style>
  margin-bottom: 24px;
  flex-shrink: 0;
  position: relative;
}

.collapsed .logo-section {
  padding: 0 16px 24px;
}

.logo-link {
  display: flex;
  align-items: center;
  gap: 16px;
  text-decoration: none;
  color: white;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  padding: 16px;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.collapsed .logo-link {
  justify-content: center;
  gap: 0;
  padding: 16px 8px;
}

.logo-link:hover {
  transform: translateY(-2px);
  background: rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);
}

.logo-icon {
  position: relative;
  width: 48px;
  height: 48px;
  flex-shrink: 0;
}

.icon-inner {
  width: 100%;
  height: 100%;
  background: var(--accent-warm);
  border: 2px solid var(--accent-warm);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--paper-pure);
  font-size: 24px;
  position: relative;
  z-index: 2;
  box-shadow: var(--shadow-paper-md);
  transition: all var(--duration-normal) var(--ease-out-quart);
}

.icon-glow {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: var(--accent-warm);
  border-radius: var(--radius-lg);
  opacity: 0;
  transition: opacity var(--duration-normal) var(--ease-out-quart);
  z-index: 1;
  filter: blur(8px);
}

.logo-link:hover .icon-glow {
  opacity: 0.6;
}

.logo-text-wrapper {
  display: flex;
  flex-direction: column;
  gap: 2px;
  overflow: hidden;
}

.collapsed .logo-text-wrapper {
  display: none;
}

.logo-text {
  font-size: 20px;
  font-weight: var(--font-weight-semibold);
  font-family: var(--font-display);
  white-space: nowrap;
  color: var(--paper-pure);
  letter-spacing: -0.3px;
}

.logo-subtitle {
  font-size: 12px;
  font-weight: 400;
  font-family: var(--font-body);
  color: rgba(252, 252, 252, 0.7);
  white-space: nowrap;
  font-weight: var(--font-weight-normal);
}

/* 状态区域 */
.status-section {
  padding: 0 24px 20px;
  margin-bottom: 20px;
}

.status-card {
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.2);
  border-radius: 12px;
  padding: 12px;
  backdrop-filter: blur(10px);
}

.status-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-icon {
  width: 24px;
  height: 24px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.status-icon.success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.status-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.status-label {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.6);
  font-weight: 500;
}

.status-value {
  font-size: 13px;
  color: #10b981;
  font-weight: 600;
}

/* 导航区域 */
.nav-section {
  flex: 1;
  overflow-y: auto;
  padding: 0 24px;
  min-height: 0;
}

.collapsed .nav-section {
  padding: 0 16px;
}

.nav-group {
  margin-bottom: 32px;
}

.nav-group:last-child {
  margin-bottom: 0;
}

.nav-group-title {
  font-size: 11px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.4);
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 16px;
  padding: 0 16px;
}

.nav-items {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 14px 16px;
  border-radius: 14px;
  text-decoration: none;
  color: rgba(255, 255, 255, 0.7);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
  min-height: 48px;
  animation: slideInLeft 0.6s ease forwards;
  animation-delay: var(--delay);
  opacity: 0;
  transform: translateX(-20px);
}

.collapsed .nav-item {
  justify-content: center;
  padding: 14px 12px;
  gap: 0;
}

.nav-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(59, 130, 246, 0.1) 0%, 
    rgba(139, 92, 246, 0.1) 50%,
    rgba(6, 182, 212, 0.1) 100%);
  opacity: 0;
  transition: all 0.4s ease;
  border-radius: 14px;
}

.nav-item:hover {
  color: white;
  transform: translateX(6px) translateY(-2px);
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.2);
}

.nav-item:hover::before {
  opacity: 1;
}

.nav-item:hover .nav-icon-bg {
  opacity: 1;
  transform: scale(1.2);
}

.nav-item.active {
  color: white;
  background: linear-gradient(135deg, 
    rgba(59, 130, 246, 0.2) 0%, 
    rgba(139, 92, 246, 0.15) 50%,
    rgba(6, 182, 212, 0.1) 100%);
  border: 1px solid rgba(59, 130, 246, 0.3);
  box-shadow: 
    0 4px 16px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.nav-item.active .nav-indicator {
  opacity: 1;
  transform: scaleY(1);
}

.nav-item.active .nav-icon-bg {
  opacity: 0.8;
}

.nav-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-size: 20px;
  position: relative;
  z-index: 2;
}

.nav-icon .el-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease;
}

.nav-item:hover .nav-icon .el-icon {
  transform: scale(1.1);
}

.nav-icon-bg {
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6, #06b6d4);
  border-radius: 8px;
  opacity: 0;
  transition: all 0.3s ease;
  z-index: 1;
  filter: blur(8px);
}

.nav-text {
  font-size: 15px;
  font-weight: 500;
  white-space: nowrap;
  position: relative;
  z-index: 2;
  overflow: hidden;
  transition: all 0.3s ease;
}

.collapsed .nav-text {
  display: none;
}

.nav-indicator {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%) scaleY(0);
  width: 3px;
  height: 20px;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  border-radius: 2px;
  opacity: 0;
  transition: all 0.3s ease;
}

@keyframes slideInLeft {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 用户区域 */
.user-section {
  padding: 24px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  flex-shrink: 0;
  margin-top: auto;
}

.collapsed .user-section {
  padding: 24px 16px;
}

.user-info {
  display: flex;
  align-items: center;
  padding: 16px;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  min-height: 64px;
  box-sizing: border-box;
  cursor: pointer;
}

.user-info:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(16, 185, 129, 0.2);
}

.collapsed .user-info {
  justify-content: center;
  padding: 16px 12px;
}

.user-avatar {
  position: relative;
  width: 40px;
  height: 40px;
  flex-shrink: 0;
}

.avatar-inner {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  position: relative;
  z-index: 2;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.avatar-status {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  background: #10b981;
  border: 2px solid #0f172a;
  border-radius: 50%;
  z-index: 3;
  animation: statusPulse 2s ease-in-out infinite;
}

@keyframes statusPulse {
  0%, 100% { 
    transform: scale(1);
    opacity: 1;
  }
  50% { 
    transform: scale(1.2);
    opacity: 0.8;
  }
}

.user-details {
  margin-left: 16px;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
  overflow: hidden;
}

.collapsed .user-details {
  display: none;
}

.user-name {
  font-size: 15px;
  font-weight: 600;
  color: white;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-status {
  font-size: 13px;
  color: #10b981;
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 500;
}

.status-dot {
  width: 6px;
  height: 6px;
  background: #10b981;
  border-radius: 50%;
  animation: statusDotPulse 1.5s ease-in-out infinite;
}

@keyframes statusDotPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.user-actions {
  display: flex;
  align-items: center;
  gap: 4px;
}

.user-action-btn {
  width: 32px !important;
  height: 32px !important;
  border-radius: 8px !important;
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  color: rgba(255, 255, 255, 0.7) !important;
  transition: all 0.3s ease !important;
  padding: 0 !important;
  min-width: 32px !important;
}

.user-action-btn:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  color: white !important;
  transform: scale(1.1) !important;
}

/* 移动端样式 */
.mobile-header {
  display: none;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1001;
}

.mobile-logo {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
  font-size: 16px;
  font-weight: 600;
}

.mobile-menu-btn {
  color: white !important;
  padding: 8px !important;
}

.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1002;
}

.mobile-sidebar {
  position: fixed;
  top: 0;
  left: 0;
  width: 280px;
  height: 100vh;
  background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);
  z-index: 1003;
  display: flex;
  flex-direction: column;
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.2);
}

.mobile-header-inner {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.mobile-close-btn {
  color: white !important;
  padding: 8px !important;
}

.mobile-nav {
  flex: 1;
  padding: 24px 16px;
  overflow-y: auto;
}

.mobile-nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  margin-bottom: 8px;
  border-radius: 12px;
  text-decoration: none;
  color: rgba(255, 255, 255, 0.7);
  transition: all 0.3s ease;
  font-size: 16px;
}

.mobile-nav-item:hover,
.mobile-nav-item.active {
  color: white;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(29, 78, 216, 0.2) 100%);
}

.mobile-user {
  padding: 24px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.mobile-user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.05);
}

.mobile-user-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.mobile-user-name {
  font-size: 16px;
  font-weight: 600;
  color: white;
}

.mobile-user-status {
  font-size: 14px;
  color: #10b981;
}

/* 移动端动画 */
.mobile-overlay-enter-active,
.mobile-overlay-leave-active {
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-overlay-enter-from,
.mobile-overlay-leave-to {
  opacity: 0;
}

.mobile-sidebar-enter-active,
.mobile-sidebar-leave-active {
  transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.mobile-sidebar-enter-from,
.mobile-sidebar-leave-to {
  transform: translateX(-100%);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .desktop-sidebar {
    display: none;
  }

  .mobile-header {
    display: flex;
  }
}

@media (min-width: 769px) {
  .mobile-header,
  .mobile-overlay,
  .mobile-sidebar {
    display: none !important;
  }
}
</style>