<template>
  <div class="text-selection-analyzer">
    <!-- 章节内容显示区域 -->
    <div
      class="chapter-content"
      @mouseup="handleTextSelection"
      @touchend="handleTextSelection"
      @selectstart="onSelectStart"
      @mousedown="clearSelection"
      ref="contentRef"
    >
      <div class="text-content" v-html="formattedContent"></div>

      <!-- 调试信息 -->
      <div v-if="selectedText" class="debug-info">
        <small>已选中: {{ selectedText.substring(0, 50) }}{{ selectedText.length > 50 ? '...' : '' }}</small>
      </div>

      <!-- 配置调试信息 -->
      <div class="debug-config" v-if="showDebugInfo">
        <h5>配置调试信息：</h5>
        <pre>{{ debugConfigInfo }}</pre>
        <el-button size="small" @click="showDebugInfo = false">隐藏调试</el-button>
      </div>

      <div v-else class="debug-toggle">
        <el-button size="small" type="info" @click="showDebugConfig">显示配置调试</el-button>
      </div>
    </div>

    <!-- 划词后的操作菜单 -->
    <div 
      v-if="selectedText && showMenu" 
      class="selection-menu" 
      :style="menuPosition"
      @click.stop
    >
      <button @click="askQuestion" class="menu-btn ask-btn">
        <el-icon><QuestionFilled /></el-icon>
        对此提问
      </button>
      <button @click="suggestImprovement" class="menu-btn improve-btn">
        <el-icon><EditPen /></el-icon>
        建议改进
      </button>
      <button @click="closeMenu" class="menu-btn close-btn">
        <el-icon><Close /></el-icon>
      </button>
    </div>

    <!-- 问题分析面板 -->
    <el-dialog
      v-model="showQuestionPanel"
      title="文本分析与改进"
      width="80%"
      :close-on-click-modal="false"
      class="question-dialog"
      :style="{ maxHeight: '90vh' }"
      :body-style="{ maxHeight: '70vh', overflow: 'auto' }"
    >
      <!-- 选中内容显示 -->
      <div class="selected-highlight">
        <h4>您选中的内容：</h4>
        <blockquote class="selected-quote">
          "{{ selectedText }}"
        </blockquote>
        <div class="context-info">
          <small>位置：第{{ selectedParagraph }}段</small>
        </div>
      </div>

      <!-- 智能问题建议 -->
      <div v-if="suggestedQuestions.length > 0" class="suggested-questions">
        <h4>AI建议的问题：</h4>
        <div class="question-chips">
          <el-tag 
            v-for="(question, index) in suggestedQuestions"
            :key="index"
            @click="selectSuggestedQuestion(question)"
            class="question-chip"
            type="info"
          >
            {{ question }}
          </el-tag>
        </div>
      </div>

      <!-- 环境和配置状态提示 -->
      <div v-if="!isElectronEnv" class="environment-warning">
        <el-alert
          title="浏览器环境限制"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            <p>当前在浏览器环境中运行，AI功能受到CORS限制。系统将自动使用离线分析功能。</p>
            <p><strong>离线功能包括：</strong>文本特征分析、常见问题检查、基础修改建议</p>
            <div class="env-actions">
              <el-button size="small" type="primary" @click="downloadElectron">
                获取完整版
              </el-button>
              <el-button size="small" @click="showOfflineFeatures">
                了解离线功能
              </el-button>
            </div>
          </template>
        </el-alert>
      </div>

      <div v-else-if="!isAIConfigured" class="config-warning">
        <el-alert
          title="AI服务未配置"
          type="warning"
          :closable="false"
          show-icon
        >
          <template #default>
            <p>当前使用基础分析功能。配置AI服务后可获得更专业的文本分析。</p>
            <div class="config-actions">
              <el-button size="small" type="primary" @click="goToSettings">
                前往配置
              </el-button>
              <el-button size="small" @click="refreshConfig">
                刷新配置
              </el-button>
              <el-button size="small" @click="diagnoseNetwork" :loading="isDiagnosing">
                网络诊断
              </el-button>
            </div>
          </template>
        </el-alert>
      </div>

      <!-- AI配置正常提示 -->
      <div v-else class="config-success">
        <el-alert
          title="AI服务已配置"
          type="success"
          :closable="true"
          show-icon
        >
          <template #default>
            <p>正在使用AI智能分析功能，可获得专业的文本分析和修改建议。</p>
          </template>
        </el-alert>
      </div>

      <!-- 用户问题输入 -->
      <div class="question-input">
        <h4>您的疑问：</h4>
        <el-input
          v-model="userQuestion"
          type="textarea"
          placeholder="请输入您对这段内容的疑问，例如：&#10;- 这里的情节发展是否合理？&#10;- 角色的反应是否符合性格？&#10;- 这段描写是否生动？"
          :rows="4"
          maxlength="500"
          show-word-limit
        />
        <div class="input-actions">
          <el-button
            @click="analyzeQuestion"
            type="primary"
            :loading="isAnalyzing"
            :disabled="!userQuestion.trim()"
          >
            <el-icon><Search /></el-icon>
            {{ isAIConfigured ? 'AI分析' : '基础分析' }}
          </el-button>
        </div>
      </div>

      <!-- AI分析结果 -->
      <div v-if="aiAnalysis" class="ai-response">
        <h4>AI分析结果：</h4>
        <div class="analysis-content">
          <div v-if="isStreamingAnalysis" class="streaming-indicator">
            <el-icon class="rotating"><Loading /></el-icon>
            <span>AI正在分析中...</span>
          </div>
          <div class="analysis-text" v-html="formattedAnalysis"></div>
        </div>
        
        <div class="response-actions">
          <el-button
            @click="generateSuggestion"
            type="success"
            :loading="isGeneratingSuggestion"
          >
            <el-icon><Star /></el-icon>
            生成修改建议
          </el-button>
          <el-button @click="reAnalyze" type="warning">
            <el-icon><Refresh /></el-icon>
            重新分析
          </el-button>
          <el-button @click="showHistoryPanel" type="info">
            <el-icon><Clock /></el-icon>
            修改历史
          </el-button>
          <el-button @click="showPreferencePanel" type="warning">
            <el-icon><User /></el-icon>
            个人偏好
          </el-button>
          <el-button @click="showCollaborationPanel" type="success">
            <el-icon><UserFilled /></el-icon>
            协作功能
          </el-button>
        </div>
      </div>

      <!-- 修改建议 -->
      <div v-if="modificationSuggestions.length > 0" class="suggestions-panel">
        <h4>修改建议：</h4>
        
        <div 
          v-for="(suggestion, index) in modificationSuggestions" 
          :key="index" 
          class="suggestion-item"
        >
          <div class="suggestion-header">
            <span class="suggestion-title">方案{{ index + 1 }}：{{ suggestion.title }}</span>
            <el-tag :type="getSuggestionTypeColor(suggestion.type)" size="small">
              {{ suggestion.type }}
            </el-tag>
          </div>
          
          <div class="suggestion-content">
            <div class="text-comparison">
              <div class="original-text">
                <strong>原文：</strong>
                <div class="text-block original">{{ suggestion.originalText }}</div>
              </div>
              <div class="modified-text">
                <strong>修改为：</strong>
                <div class="text-block modified">{{ suggestion.modifiedText }}</div>
              </div>
            </div>
            <div class="reason">
              <strong>修改理由：</strong>{{ suggestion.reason }}
            </div>
          </div>
          
          <div class="suggestion-actions">
            <el-button 
              @click="applySuggestion(suggestion)" 
              type="primary"
              size="small"
            >
              <el-icon><Check /></el-icon>
              应用此修改
            </el-button>
            <el-button 
              @click="previewSuggestion(suggestion)" 
              type="info"
              size="small"
            >
              <el-icon><View /></el-icon>
              预览效果
            </el-button>
          </div>
        </div>
        
        <!-- 自定义修改 -->
        <div class="custom-suggestion">
          <h5>或者输入您自己的修改：</h5>
          <el-input 
            v-model="customModification" 
            type="textarea" 
            :rows="3"
            placeholder="请输入您的修改内容..."
          />
          <div class="custom-actions">
            <el-button 
              @click="applyCustomModification" 
              type="success"
              :disabled="!customModification.trim()"
            >
              <el-icon><EditPen /></el-icon>
              应用自定义修改
            </el-button>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeQuestionPanel">取消</el-button>
          <el-button v-if="hasModifications" @click="saveModifications" type="primary">
            保存所有修改
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 预览对话框 -->
    <el-dialog
      v-model="showPreviewDialog"
      title="修改预览"
      width="70%"
    >
      <div class="preview-content">
        <el-tabs v-model="previewTab">
          <el-tab-pane label="修改前" name="before">
            <div class="content-preview original-preview">{{ originalContent }}</div>
          </el-tab-pane>
          <el-tab-pane label="修改后" name="after">
            <div class="content-preview modified-preview">{{ previewContent }}</div>
          </el-tab-pane>
          <el-tab-pane label="对比" name="diff">
            <div class="diff-view">
              <div class="diff-item removed">
                <span class="diff-label">- 删除：</span>
                <span class="diff-text">{{ currentPreviewSuggestion?.originalText }}</span>
              </div>
              <div class="diff-item added">
                <span class="diff-label">+ 添加：</span>
                <span class="diff-text">{{ currentPreviewSuggestion?.modifiedText }}</span>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      
      <template #footer>
        <div class="preview-footer">
          <el-button @click="showPreviewDialog = false">关闭预览</el-button>
          <el-button 
            @click="confirmPreviewAndApply" 
            type="primary"
          >
            确认并应用
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 修改历史对话框 -->
    <el-dialog
      v-model="showHistoryDialog"
      title="修改历史"
      width="80%"
      :close-on-click-modal="false"
      class="history-dialog"
    >
      <ModificationHistory
        :visible="showHistoryDialog"
        @undo="handleHistoryUndo"
        @redo="handleHistoryRedo"
        @revert-to="handleHistoryRevertTo"
        @close="showHistoryDialog = false"
      />
    </el-dialog>

    <!-- 用户偏好对话框 -->
    <el-dialog
      v-model="showPreferenceDialog"
      title="个人偏好设置"
      width="90%"
      :close-on-click-modal="false"
      class="preference-dialog"
    >
      <UserPreferencePanel />
    </el-dialog>

    <!-- 协作功能对话框 -->
    <el-dialog
      v-model="showCollaborationDialog"
      title="协作功能"
      width="90%"
      :close-on-click-modal="false"
      class="collaboration-dialog"
    >
      <CollaborationPanel />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, nextTick, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  QuestionFilled,
  EditPen,
  Close,
  Search,
  Star,
  Refresh,
  Check,
  View,
  Loading,
  Clock,
  User,
  UserFilled
} from '@element-plus/icons-vue'
import { textAnalysisService } from '../services/textAnalysisService.js'
import { modificationHistoryService } from '../services/modificationHistoryService'
import { smartQuestionService } from '../services/smartQuestionService'
import { userPreferenceService } from '../services/userPreferenceService'
import { collaborationService } from '../services/collaborationService'
import ModificationHistory from './ModificationHistory.vue'
import UserPreferencePanel from './UserPreferencePanel.vue'
import CollaborationPanel from './CollaborationPanel.vue'

// 组件注册
const components = {
  ModificationHistory,
  UserPreferencePanel,
  CollaborationPanel
}

// Props
const props = defineProps({
  chapterContent: {
    type: String,
    required: true
  },
  chapterNumber: {
    type: Number,
    required: true
  }
})

// Emits
const emit = defineEmits(['content-updated', 'modification-applied'])

// 响应式数据
const contentRef = ref(null)
const selectedText = ref('')
const selectedRange = ref(null)
const selectedParagraph = ref(0)
const showMenu = ref(false)
const menuPosition = ref({ top: '0px', left: '0px' })

// 问题分析相关
const showQuestionPanel = ref(false)
const userQuestion = ref('')
const suggestedQuestions = ref([])
const aiAnalysis = ref('')
const isAnalyzing = ref(false)
const isStreamingAnalysis = ref(false)

// 修改建议相关
const modificationSuggestions = ref([])
const isGeneratingSuggestion = ref(false)
const isStreamingSuggestion = ref(false)
const customModification = ref('')
const hasModifications = ref(false)

// 预览相关
const showPreviewDialog = ref(false)
const previewTab = ref('before')
const originalContent = ref('')
const previewContent = ref('')
const currentPreviewSuggestion = ref(null)

// 历史记录相关
const showHistoryDialog = ref(false)

// 用户偏好相关
const showPreferenceDialog = ref(false)

// 协作功能相关
const showCollaborationDialog = ref(false)

// AI配置状态
const isAIConfigured = ref(false)
const isElectronEnv = ref(false)
const showDebugInfo = ref(false)
const debugConfigInfo = ref('')
const isDiagnosing = ref(false)

// 计算属性
const formattedContent = computed(() => {
  // 将内容按段落分割并添加段落标记
  const paragraphs = props.chapterContent.split('\n').filter(p => p.trim())
  return paragraphs.map((p, index) =>
    `<p data-paragraph="${index + 1}">${p}</p>`
  ).join('')
})

// 格式化AI分析结果
const formattedAnalysis = computed(() => {
  if (!aiAnalysis.value) return ''

  // 简单的Markdown到HTML转换
  let formatted = aiAnalysis.value
    .replace(/### (.*)/g, '<h3>$1</h3>')
    .replace(/## (.*)/g, '<h2>$1</h2>')
    .replace(/# (.*)/g, '<h1>$1</h1>')
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/`(.*?)`/g, '<code>$1</code>')
    .replace(/\n\n/g, '</p><p>')
    .replace(/\n/g, '<br>')

  // 处理列表
  formatted = formatted.replace(/^\* (.*)/gm, '<li>$1</li>')
  formatted = formatted.replace(/(<li>.*<\/li>)/gs, '<ul>$1</ul>')

  // 包装在段落中
  if (!formatted.startsWith('<')) {
    formatted = '<p>' + formatted + '</p>'
  }

  return formatted
})

// 方法
const handleTextSelection = (event) => {
  // 延迟执行，确保选择完成
  setTimeout(() => {
    const selection = window.getSelection()
    const selectedTextContent = selection.toString().trim()

    console.log('选择事件触发，选中文本长度:', selectedTextContent.length)
    console.log('选中文本内容:', selectedTextContent)

    if (selectedTextContent.length > 0) {
      selectedText.value = selectedTextContent
      selectedRange.value = selection.getRangeAt(0)

      // 获取选中文本所在的段落
      const container = selection.getRangeAt(0).commonAncestorContainer
      const paragraph = container.nodeType === Node.TEXT_NODE
        ? container.parentElement
        : container

      const paragraphElement = paragraph.closest('[data-paragraph]')
      selectedParagraph.value = paragraphElement
        ? parseInt(paragraphElement.dataset.paragraph)
        : 1

      // 计算菜单位置
      const rect = selection.getRangeAt(0).getBoundingClientRect()
      const containerRect = contentRef.value.getBoundingClientRect()

      menuPosition.value = {
        top: (rect.bottom - containerRect.top + 10) + 'px',
        left: (rect.left - containerRect.left) + 'px'
      }

      showMenu.value = true

      // 生成智能问题建议
      generateSuggestedQuestions()

      console.log('菜单已显示，位置:', menuPosition.value)
    } else {
      console.log('没有选中文本')
    }
  }, 100)
}

const onSelectStart = (event) => {
  console.log('开始选择文本')
  // 确保可以选择文本
  event.stopPropagation()
}

const clearSelection = () => {
  if (!showQuestionPanel.value) {
    showMenu.value = false
    selectedText.value = ''
    selectedRange.value = null
  }
}

const closeMenu = () => {
  showMenu.value = false
  window.getSelection().removeAllRanges()
}

const askQuestion = () => {
  showQuestionPanel.value = true
  showMenu.value = false
}

const suggestImprovement = () => {
  userQuestion.value = '请分析这段文本并提供改进建议'
  askQuestion()
}

const generateSuggestedQuestions = () => {
  const text = selectedText.value

  // 构建上下文信息
  const context = {
    chapterContent: props.chapterContent,
    selectedParagraph: selectedParagraph.value,
    position: calculateTextPosition(),
    previousQuestions: getPreviousQuestions(),
    userHistory: getUserQuestionHistory()
  }

  // 使用增强的智能问题建议服务
  const smartQuestions = smartQuestionService.generateSmartQuestions(text, context, {
    maxQuestions: 6,
    includeGeneral: true,
    prioritizeUserPreferences: true
  })

  suggestedQuestions.value = smartQuestions
}

const selectSuggestedQuestion = (question) => {
  userQuestion.value = question

  // 学习用户选择偏好
  smartQuestionService.learnFromUserChoice(
    question,
    suggestedQuestions.value,
    {
      selectedText: selectedText.value,
      context: getContext()
    }
  )
}

// 辅助方法
const calculateTextPosition = () => {
  if (!props.chapterContent || !selectedText.value) return 0

  const textIndex = props.chapterContent.indexOf(selectedText.value)
  return textIndex / props.chapterContent.length
}

const getPreviousQuestions = () => {
  // 从历史记录中获取之前的问题
  const history = modificationHistoryService.getHistory({ limit: 10 })
  return history.records
    .filter(record => record.question)
    .map(record => record.question)
    .slice(0, 5)
}

const getUserQuestionHistory = () => {
  // 获取用户问题历史统计
  const history = modificationHistoryService.getHistory({ limit: 50 })
  const questionTypes = {}

  history.records.forEach(record => {
    if (record.question) {
      const category = smartQuestionService.categorizeQuestion(record.question)
      questionTypes[category] = (questionTypes[category] || 0) + 1
    }
  })

  return questionTypes
}

const analyzeQuestion = async () => {
  if (!userQuestion.value.trim()) {
    ElMessage.warning('请输入您的疑问')
    return
  }

  isAnalyzing.value = true
  isStreamingAnalysis.value = true
  aiAnalysis.value = '' // 清空之前的分析结果

  try {
    // 调用AI分析服务，使用非流式输出
    const analysis = await analyzeSelectedText(
      selectedText.value,
      userQuestion.value,
      getContext(),
      {
        stream: false
      }
    )

    // 直接设置分析结果
    aiAnalysis.value = analysis

    ElMessage.success('分析完成')
  } catch (error) {
    console.error('分析失败:', error)
    ElMessage.error('分析失败，请重试')
  } finally {
    isAnalyzing.value = false
    isStreamingAnalysis.value = false
  }
}

const getContext = () => {
  // 获取选中文本的上下文
  const paragraphs = props.chapterContent.split('\n').filter(p => p.trim())
  const currentParagraph = paragraphs[selectedParagraph.value - 1] || ''
  const prevParagraph = paragraphs[selectedParagraph.value - 2] || ''
  const nextParagraph = paragraphs[selectedParagraph.value] || ''
  
  return {
    previous: prevParagraph,
    current: currentParagraph,
    next: nextParagraph,
    chapterNumber: props.chapterNumber
  }
}

// 使用AI分析服务（支持流式输出）
const analyzeSelectedText = async (selectedText, question, context, options = {}) => {
  try {
    return await textAnalysisService.analyzeSelectedText(selectedText, question, context, options)
  } catch (error) {
    console.error('AI分析失败:', error)
    // 返回备用分析
    return `针对您选中的文本"${selectedText}"和问题"${question}"，我的分析如下：

这段文本在当前上下文中的作用是推进情节发展。从文学角度来看，这段描述具有以下特点：

1. 语言表达：文字流畅，但可能存在一些可以优化的地方
2. 情节逻辑：与前文的连接基本合理
3. 角色塑造：符合角色的基本设定
4. 节奏把控：在整体节奏中起到了应有的作用

建议关注的方面：可以考虑增强细节描写，使内容更加生动具体。

注意：当前使用备用分析，请检查AI服务配置。`
  }
}

const reAnalyze = () => {
  aiAnalysis.value = ''
  modificationSuggestions.value = []
}

const generateSuggestion = async () => {
  isGeneratingSuggestion.value = true
  
  try {
    const suggestions = await generateModificationSuggestions(
      aiAnalysis.value,
      selectedText.value,
      getContext()
    )
    
    modificationSuggestions.value = suggestions
    ElMessage.success('修改建议生成完成')
  } catch (error) {
    console.error('生成建议失败:', error)
    ElMessage.error('生成建议失败，请重试')
  } finally {
    isGeneratingSuggestion.value = false
  }
}

// 使用AI服务生成修改建议
const generateModificationSuggestions = async (analysis, selectedText, context) => {
  try {
    return await textAnalysisService.generateModificationSuggestions(analysis, selectedText, context)
  } catch (error) {
    console.error('生成建议失败:', error)
    // 返回备用建议
    return [
      {
        title: '增强细节描写',
        type: '文笔优化',
        originalText: selectedText,
        modifiedText: selectedText + '，细节更加丰富生动',
        reason: '通过增加细节描写，使内容更加具体形象，增强读者的代入感'
      },
      {
        title: '调整表达方式',
        type: '语言优化',
        originalText: selectedText,
        modifiedText: '用更加精炼的语言表达：' + selectedText.substring(0, Math.max(0, selectedText.length - 10)) + '...',
        reason: '简化表达，使语言更加精炼有力'
      }
    ]
  }
}

const getSuggestionTypeColor = (type) => {
  const colorMap = {
    '文笔优化': 'success',
    '语言优化': 'primary',
    '情节调整': 'warning',
    '角色优化': 'info',
    '逻辑修正': 'danger'
  }
  return colorMap[type] || 'info'
}

const applySuggestion = async (suggestion) => {
  try {
    await ElMessageBox.confirm(
      `确定要应用这个修改建议吗？\n\n原文：${suggestion.originalText}\n\n修改为：${suggestion.modifiedText}`,
      '确认修改',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 应用修改
    applyModification(suggestion.originalText, suggestion.modifiedText)
    hasModifications.value = true
    
    ElMessage.success('修改已应用')
  } catch {
    // 用户取消
  }
}

const previewSuggestion = (suggestion) => {
  currentPreviewSuggestion.value = suggestion
  originalContent.value = props.chapterContent
  previewContent.value = props.chapterContent.replace(
    suggestion.originalText, 
    suggestion.modifiedText
  )
  showPreviewDialog.value = true
  previewTab.value = 'before'
}

const confirmPreviewAndApply = () => {
  if (currentPreviewSuggestion.value) {
    applyModification(
      currentPreviewSuggestion.value.originalText,
      currentPreviewSuggestion.value.modifiedText
    )
    hasModifications.value = true
    showPreviewDialog.value = false
    ElMessage.success('修改已应用')
  }
}

const applyCustomModification = async () => {
  if (!customModification.value.trim()) {
    ElMessage.warning('请输入修改内容')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      `确定要应用自定义修改吗？\n\n原文：${selectedText.value}\n\n修改为：${customModification.value}`,
      '确认修改',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    applyModification(selectedText.value, customModification.value)
    hasModifications.value = true
    customModification.value = ''
    
    ElMessage.success('自定义修改已应用')
  } catch {
    // 用户取消
  }
}

const applyModification = (originalText, modifiedText) => {
  // 应用修改到内容中
  const newContent = props.chapterContent.replace(originalText, modifiedText)

  // 记录修改历史
  const modification = {
    type: 'text_edit',
    originalText,
    modifiedText,
    question: userQuestion.value,
    aiAnalysis: aiAnalysis.value,
    suggestionType: getCurrentSuggestionType(),
    reason: getCurrentModificationReason(),
    context: getContext()
  }

  const historyRecord = modificationHistoryService.addModification(modification)
  console.log('修改记录已添加:', historyRecord)
  console.log('当前历史记录总数:', modificationHistoryService.history.length)

  // 学习用户偏好
  userPreferenceService.learnFromModification(historyRecord, 'accept')

  // 同步到协作会话
  if (collaborationService.activeSession) {
    collaborationService.syncTextChange({
      operation: 'replace',
      position: {
        start: props.chapterContent.indexOf(originalText),
        end: props.chapterContent.indexOf(originalText) + originalText.length
      },
      content: modifiedText,
      metadata: {
        question: userQuestion.value,
        suggestionType: getCurrentSuggestionType()
      }
    })
  }

  emit('content-updated', newContent)
  emit('modification-applied', {
    original: originalText,
    modified: modifiedText,
    timestamp: new Date(),
    question: userQuestion.value
  })
}

const getCurrentSuggestionType = () => {
  // 从当前的修改建议中获取类型
  if (modificationSuggestions.value.length > 0) {
    return modificationSuggestions.value[0].type
  }
  return '手动修改'
}

const getCurrentModificationReason = () => {
  // 从当前的修改建议中获取原因
  if (modificationSuggestions.value.length > 0) {
    return modificationSuggestions.value[0].reason
  }
  return userQuestion.value || '用户手动修改'
}

const showHistoryPanel = () => {
  showHistoryDialog.value = true
}

const showPreferencePanel = () => {
  showPreferenceDialog.value = true
}

const showCollaborationPanel = () => {
  showCollaborationDialog.value = true
}

const handleHistoryUndo = (result) => {
  // 处理撤销操作
  if (result && result.record) {
    const record = result.record
    // 恢复到撤销前的状态
    const newContent = props.chapterContent.replace(record.modifiedText, record.originalText)
    emit('content-updated', newContent)
    ElMessage.success('已撤销修改')
  }
}

const handleHistoryRedo = (result) => {
  // 处理重做操作
  if (result && result.record) {
    const record = result.record
    // 重新应用修改
    const newContent = props.chapterContent.replace(record.originalText, record.modifiedText)
    emit('content-updated', newContent)
    ElMessage.success('已重做修改')
  }
}

const handleHistoryRevertTo = (record) => {
  // 回滚到指定记录
  if (record) {
    const newContent = props.chapterContent.replace(record.modifiedText, record.originalText)
    emit('content-updated', newContent)
    ElMessage.success('已回滚到指定版本')
  }
}

const closeQuestionPanel = () => {
  showQuestionPanel.value = false
  userQuestion.value = ''
  aiAnalysis.value = ''
  modificationSuggestions.value = []
  customModification.value = ''
  clearSelection()
}

const saveModifications = () => {
  ElMessage.success('所有修改已保存')
  closeQuestionPanel()
}

// 检查运行环境
const checkEnvironment = () => {
  isElectronEnv.value = !!(window.electronAPI && typeof window.electronAPI.invoke === 'function')
  console.log('运行环境检测:', isElectronEnv.value ? 'Electron' : 'Browser')
}

// 检查AI配置状态
const checkAIConfiguration = () => {
  const configStatus = textAnalysisService.checkConfiguration()
  isAIConfigured.value = configStatus.isValid
  return configStatus
}

// 跳转到设置页面
const goToSettings = () => {
  // 这里可以添加路由跳转到设置页面
  ElMessage.info('请在设置页面配置AI服务')
  // 如果有路由，可以使用：
  // router.push('/settings')
}

// 下载Electron版本
const downloadElectron = () => {
  ElMessage.info('请联系开发者获取Electron桌面版本')
  // 这里可以添加下载链接或说明
}

// 显示离线功能说明
const showOfflineFeatures = () => {
  ElMessageBox.alert(`
离线分析功能包括：

📝 文本特征分析
• 文本长度检查
• 对话识别
• 转折词检测
• 程度副词识别

🔍 常见问题检查
• 重复词汇检测
• 句子长度分析
• 标点符号检查

💡 基础修改建议
• 语言精炼建议
• 细节增强建议
• 逻辑检查建议

虽然功能有限，但仍能为您的写作提供有价值的参考！
  `, '离线功能说明', {
    confirmButtonText: '知道了',
    type: 'info'
  })
}

// 刷新配置
const refreshConfig = () => {
  const configStatus = checkAIConfiguration()
  if (configStatus.isValid) {
    ElMessage.success('AI配置已更新！')
  } else {
    ElMessage.warning('仍未检测到有效的AI配置')
  }
}

// 网络诊断
const diagnoseNetwork = async () => {
  if (!isElectronEnv.value) {
    ElMessage.warning('网络诊断功能仅在Electron环境中可用')
    return
  }

  isDiagnosing.value = true
  try {
    const result = await textAnalysisService.diagnoseNetwork()

    if (result.success) {
      ElMessage.success(`网络诊断成功: ${result.message}`)
    } else {
      ElMessage.error(`网络诊断失败: ${result.message}`)

      // 提供解决建议
      ElMessageBox.alert(`
网络连接问题可能的解决方案：

🔧 检查网络连接
• 确保网络连接正常
• 尝试访问其他网站验证网络

🛡️ 检查防火墙设置
• 确保防火墙允许应用访问网络
• 临时关闭防火墙测试

🌐 代理设置
• 如果使用代理，请配置正确的代理设置
• 尝试关闭代理测试直连

⏱️ 网络延迟
• 当前网络可能较慢，请稍后重试
• 考虑使用更稳定的网络环境

如果问题持续存在，建议联系网络管理员或使用其他网络环境。
      `, '网络诊断失败', {
        confirmButtonText: '知道了',
        type: 'warning'
      })
    }
  } catch (error) {
    ElMessage.error(`诊断过程出错: ${error.message}`)
  } finally {
    isDiagnosing.value = false
  }
}

// 显示配置调试信息
const showDebugConfig = () => {
  try {
    const aiSettings = localStorage.getItem('aiSettings')
    const advancedSettings = localStorage.getItem('advancedSettings')

    debugConfigInfo.value = JSON.stringify({
      aiSettings: aiSettings ? JSON.parse(aiSettings) : null,
      advancedSettings: advancedSettings ? JSON.parse(advancedSettings) : null,
      textAnalysisConfig: textAnalysisService.config,
      configStatus: textAnalysisService.checkConfiguration()
    }, null, 2)

    showDebugInfo.value = true
  } catch (error) {
    debugConfigInfo.value = `配置读取错误: ${error.message}`
    showDebugInfo.value = true
  }
}

// 组件挂载时检查环境和配置
onMounted(() => {
  checkEnvironment()
  checkAIConfiguration()
})

// 监听点击外部关闭菜单
document.addEventListener('click', (event) => {
  if (!event.target.closest('.selection-menu') && !event.target.closest('.question-dialog')) {
    showMenu.value = false
  }
})
</script>

<style scoped>
.text-selection-analyzer {
  position: relative;
  width: 100%;
}

.chapter-content {
  position: relative;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
  cursor: text;
}

.text-content {
  line-height: 1.8;
  font-size: 16px;
  color: #333;
  font-family: 'Microsoft YaHei', sans-serif;
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
  cursor: text;
}

.text-content p {
  margin-bottom: 16px;
  text-indent: 2em;
}

.text-content ::selection {
  background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
  color: #333;
}

.debug-info {
  margin-top: 10px;
  padding: 8px;
  background: #f0f9ff;
  border-radius: 4px;
  border: 1px solid #bae6fd;
  font-size: 12px;
  color: #0369a1;
}

.environment-warning, .config-warning, .config-success {
  margin-bottom: 20px;
}

.environment-warning .el-alert, .config-warning .el-alert, .config-success .el-alert {
  border-radius: 8px;
}

.config-warning p {
  margin: 8px 0;
  color: #92400e;
}

.config-success p {
  margin: 8px 0;
  color: #166534;
}

.config-actions, .env-actions {
  margin-top: 12px;
  display: flex;
  gap: 8px;
}

.environment-warning p {
  margin: 8px 0;
  color: #1f2937;
}

.debug-config {
  margin: 16px 0;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #dee2e6;
}

.debug-config h5 {
  margin: 0 0 8px 0;
  color: #495057;
}

.debug-config pre {
  background: #ffffff;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #e9ecef;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
  white-space: pre-wrap;
}

.debug-toggle {
  margin: 8px 0;
  text-align: center;
}

.streaming-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  padding: 8px 12px;
  background: #f0f9ff;
  border-radius: 6px;
  color: #1e40af;
  font-size: 14px;
  border: 1px solid #dbeafe;
}

.streaming-indicator .rotating {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 选择菜单样式 */
.selection-menu {
  position: absolute;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0,0,0,0.15);
  padding: 8px;
  z-index: 1000;
  display: flex;
  gap: 8px;
  border: 1px solid #e0e0e0;
}

.menu-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  background: #f5f5f5;
  color: #666;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.menu-btn:hover {
  background: #e0e0e0;
  color: #333;
}

.ask-btn:hover {
  background: #e3f2fd;
  color: #1976d2;
}

.improve-btn:hover {
  background: #e8f5e8;
  color: #388e3c;
}

.close-btn:hover {
  background: #ffebee;
  color: #d32f2f;
}

/* 问题对话框样式 */
.question-dialog {
  --el-dialog-padding-primary: 0;
}

.selected-highlight {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.selected-quote {
  background: #fff;
  border-left: 4px solid #409eff;
  margin: 12px 0;
  padding: 12px 16px;
  font-style: italic;
  color: #555;
  border-radius: 0 4px 4px 0;
}

.context-info {
  color: #999;
  font-size: 12px;
}

/* 建议问题样式 */
.suggested-questions {
  margin-bottom: 20px;
}

.question-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.question-chip {
  cursor: pointer;
  transition: all 0.2s ease;
}

.question-chip:hover {
  background: #409eff;
  color: white;
}

/* 问题输入样式 */
.question-input {
  margin-bottom: 20px;
}

.input-actions {
  margin-top: 12px;
  display: flex;
  justify-content: flex-end;
}

/* AI分析结果样式 */
.ai-response {
  margin-bottom: 20px;
  padding: 16px;
  background: #f0f9ff;
  border-radius: 8px;
  border: 1px solid #bae6fd;
}

.analysis-content {
  margin: 12px 0;
  max-height: 500px;
  overflow-y: auto;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: #fafafa;
}

.analysis-text {
  white-space: pre-wrap;
  line-height: 1.6;
  color: #374151;
  padding: 16px;
  min-height: 100px;
}

.response-actions {
  margin-top: 16px;
  display: flex;
  gap: 12px;
}

/* 修改建议样式 */
.suggestions-panel {
  margin-top: 20px;
}

.suggestion-item {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  transition: all 0.3s ease;
  background: #fff;
}

.suggestion-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.suggestion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.suggestion-title {
  font-weight: 600;
  color: #374151;
}

.suggestion-content {
  margin-bottom: 16px;
}

.text-comparison {
  margin-bottom: 12px;
}

.text-block {
  padding: 12px;
  border-radius: 6px;
  margin: 8px 0;
  font-family: 'Microsoft YaHei', sans-serif;
  line-height: 1.6;
}

.text-block.original {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #991b1b;
}

.text-block.modified {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  color: #166534;
}

.reason {
  color: #6b7280;
  font-size: 14px;
  font-style: italic;
}

.suggestion-actions {
  display: flex;
  gap: 8px;
}

/* 自定义修改样式 */
.custom-suggestion {
  margin-top: 20px;
  padding: 16px;
  background: #fffbeb;
  border-radius: 8px;
  border: 1px solid #fed7aa;
}

.custom-actions {
  margin-top: 12px;
  display: flex;
  justify-content: flex-end;
}

/* 预览对话框样式 */
.preview-content {
  max-height: 60vh;
  overflow-y: auto;
}

.content-preview {
  padding: 16px;
  background: #f9fafb;
  border-radius: 6px;
  white-space: pre-wrap;
  line-height: 1.6;
  font-family: 'Microsoft YaHei', sans-serif;
}

.original-preview {
  border-left: 4px solid #ef4444;
}

.modified-preview {
  border-left: 4px solid #10b981;
}

.diff-view {
  padding: 16px;
}

.diff-item {
  padding: 8px 12px;
  margin: 8px 0;
  border-radius: 6px;
  font-family: monospace;
}

.diff-item.removed {
  background: #fef2f2;
  border-left: 4px solid #ef4444;
}

.diff-item.added {
  background: #f0fdf4;
  border-left: 4px solid #10b981;
}

.diff-label {
  font-weight: bold;
  margin-right: 8px;
}

.diff-text {
  font-family: 'Microsoft YaHei', sans-serif;
}

/* 对话框底部样式 */
.dialog-footer, .preview-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chapter-content {
    padding: 16px;
  }

  .text-content {
    font-size: 14px;
  }

  .selection-menu {
    flex-direction: column;
    min-width: 120px;
  }

  .question-chips {
    flex-direction: column;
  }

  .suggestion-actions {
    flex-direction: column;
  }

  .response-actions {
    flex-direction: column;
  }
}
</style>
