<template>
  <div class="user-preference-panel">
    <!-- 头部 -->
    <div class="panel-header">
      <h3>
        <el-icon><User /></el-icon>
        个人偏好设置
      </h3>
      <div class="header-actions">
        <el-button @click="refreshData" size="small" type="primary">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-dropdown @command="handleMenuCommand">
          <el-button size="small">
            <el-icon><More /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="export">导出偏好</el-dropdown-item>
              <el-dropdown-item command="import">导入偏好</el-dropdown-item>
              <el-dropdown-item command="reset" divided>重置偏好</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 学习进度 -->
    <div class="learning-progress">
      <el-card>
        <template #header>
          <span>学习进度</span>
        </template>
        <div class="progress-content">
          <el-progress
            :percentage="statistics.learningProgress || 0"
            :color="getProgressColor(statistics.learningProgress || 0)"
          />
          <div class="progress-stats">
            <el-row :gutter="16">
              <el-col :span="6">
                <el-statistic title="总修改次数" :value="statistics.learningMetrics?.totalModifications || 0" />
              </el-col>
              <el-col :span="6">
                <el-statistic title="接受建议" :value="statistics.learningMetrics?.acceptedSuggestions || 0" />
              </el-col>
              <el-col :span="6">
                <el-statistic title="拒绝建议" :value="statistics.learningMetrics?.rejectedSuggestions || 0" />
              </el-col>
              <el-col :span="6">
                <el-statistic title="手动修改" :value="statistics.learningMetrics?.manualModifications || 0" />
              </el-col>
            </el-row>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 偏好设置标签页 -->
    <el-tabs v-model="activeTab" class="preference-tabs">
      <!-- 写作风格 -->
      <el-tab-pane label="写作风格" name="style">
        <div class="style-preferences">
          <el-form :model="preferences.writingStyle" label-width="120px">
            <el-form-item label="文本长度偏好">
              <el-radio-group v-model="preferences.writingStyle.preferredLength">
                <el-radio label="short">简短</el-radio>
                <el-radio label="medium">适中</el-radio>
                <el-radio label="long">详细</el-radio>
              </el-radio-group>
            </el-form-item>
            
            <el-form-item label="句式复杂度">
              <el-radio-group v-model="preferences.writingStyle.sentenceComplexity">
                <el-radio label="simple">简单</el-radio>
                <el-radio label="balanced">平衡</el-radio>
                <el-radio label="complex">复杂</el-radio>
              </el-radio-group>
            </el-form-item>
            
            <el-form-item label="描述详细度">
              <el-radio-group v-model="preferences.writingStyle.descriptiveLevel">
                <el-radio label="minimal">简约</el-radio>
                <el-radio label="moderate">适度</el-radio>
                <el-radio label="detailed">详细</el-radio>
              </el-radio-group>
            </el-form-item>
            
            <el-form-item label="对话风格">
              <el-radio-group v-model="preferences.writingStyle.dialogueStyle">
                <el-radio label="formal">正式</el-radio>
                <el-radio label="natural">自然</el-radio>
                <el-radio label="casual">随意</el-radio>
              </el-radio-group>
            </el-form-item>
            
            <el-form-item label="节奏偏好">
              <el-radio-group v-model="preferences.writingStyle.pacing">
                <el-radio label="slow">缓慢</el-radio>
                <el-radio label="steady">稳定</el-radio>
                <el-radio label="fast">快速</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
        </div>
      </el-tab-pane>

      <!-- 内容偏好 -->
      <el-tab-pane label="内容偏好" name="content">
        <div class="content-preferences">
          <el-form :model="preferences.contentPreferences" label-width="120px">
            <el-form-item label="角色描写">
              <el-slider 
                v-model="preferences.contentPreferences.characterFocus"
                :min="0" 
                :max="1" 
                :step="0.1"
                show-stops
              />
              <span class="slider-value">{{ (preferences.contentPreferences.characterFocus * 100).toFixed(0) }}%</span>
            </el-form-item>
            
            <el-form-item label="情节推进">
              <el-slider 
                v-model="preferences.contentPreferences.plotFocus"
                :min="0" 
                :max="1" 
                :step="0.1"
                show-stops
              />
              <span class="slider-value">{{ (preferences.contentPreferences.plotFocus * 100).toFixed(0) }}%</span>
            </el-form-item>
            
            <el-form-item label="氛围营造">
              <el-slider 
                v-model="preferences.contentPreferences.atmosphereFocus"
                :min="0" 
                :max="1" 
                :step="0.1"
                show-stops
              />
              <span class="slider-value">{{ (preferences.contentPreferences.atmosphereFocus * 100).toFixed(0) }}%</span>
            </el-form-item>
            
            <el-form-item label="动作描写">
              <el-slider 
                v-model="preferences.contentPreferences.actionFocus"
                :min="0" 
                :max="1" 
                :step="0.1"
                show-stops
              />
              <span class="slider-value">{{ (preferences.contentPreferences.actionFocus * 100).toFixed(0) }}%</span>
            </el-form-item>
            
            <el-form-item label="情感表达">
              <el-slider 
                v-model="preferences.contentPreferences.emotionFocus"
                :min="0" 
                :max="1" 
                :step="0.1"
                show-stops
              />
              <span class="slider-value">{{ (preferences.contentPreferences.emotionFocus * 100).toFixed(0) }}%</span>
            </el-form-item>
          </el-form>
        </div>
      </el-tab-pane>

      <!-- 修改模式 -->
      <el-tab-pane label="修改模式" name="patterns">
        <div class="pattern-preferences">
          <el-row :gutter="16">
            <el-col :span="12">
              <el-card>
                <template #header>
                  <span>常用修改类型</span>
                </template>
                <div class="pattern-list">
                  <el-tag
                    v-for="type in (statistics.modificationPatterns?.frequentTypes || [])"
                    :key="type"
                    class="pattern-tag"
                    type="success"
                  >
                    {{ type }}
                  </el-tag>
                  <el-empty v-if="!(statistics.modificationPatterns?.frequentTypes?.length)" description="暂无数据" />
                </div>
              </el-card>
            </el-col>
            
            <el-col :span="12">
              <el-card>
                <template #header>
                  <span>偏好建议类型</span>
                </template>
                <div class="pattern-list">
                  <el-tag
                    v-for="type in (statistics.modificationPatterns?.preferredSuggestions || [])"
                    :key="type"
                    class="pattern-tag"
                    type="primary"
                  >
                    {{ type }}
                  </el-tag>
                  <el-empty v-if="!(statistics.modificationPatterns?.preferredSuggestions?.length)" description="暂无数据" />
                </div>
              </el-card>
            </el-col>
          </el-row>
          
          <!-- 修改模式图表 -->
          <el-card style="margin-top: 16px;">
            <template #header>
              <span>修改类型分布</span>
            </template>
            <div class="pattern-chart">
              <div 
                v-for="(count, type) in patternDistribution"
                :key="type"
                class="pattern-bar"
              >
                <span class="pattern-name">{{ type }}</span>
                <div class="bar-container">
                  <div 
                    class="bar-fill"
                    :style="{ width: `${(count / maxPatternCount) * 100}%` }"
                  ></div>
                  <span class="bar-count">{{ count }}</span>
                </div>
              </div>
            </div>
          </el-card>
        </div>
      </el-tab-pane>

      <!-- 质量标准 -->
      <el-tab-pane label="质量标准" name="quality">
        <div class="quality-preferences">
          <el-form :model="preferences.qualityStandards" label-width="120px">
            <el-form-item label="最小文本长度">
              <el-input-number 
                v-model="preferences.qualityStandards.minTextLength"
                :min="5"
                :max="100"
              />
              <span class="input-suffix">字符</span>
            </el-form-item>
            
            <el-form-item label="最大文本长度">
              <el-input-number 
                v-model="preferences.qualityStandards.maxTextLength"
                :min="100"
                :max="2000"
              />
              <span class="input-suffix">字符</span>
            </el-form-item>
            
            <el-form-item label="语法严格度">
              <el-radio-group v-model="preferences.qualityStandards.grammarStrictness">
                <el-radio label="loose">宽松</el-radio>
                <el-radio label="moderate">适中</el-radio>
                <el-radio label="strict">严格</el-radio>
              </el-radio-group>
            </el-form-item>
            
            <el-form-item label="风格一致性">
              <el-radio-group v-model="preferences.qualityStandards.styleConsistency">
                <el-radio label="low">低</el-radio>
                <el-radio label="medium">中</el-radio>
                <el-radio label="high">高</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
        </div>
      </el-tab-pane>

      <!-- 个性化建议 -->
      <el-tab-pane label="个性化建议" name="suggestions">
        <div class="personalized-suggestions">
          <el-card>
            <template #header>
              <span>基于您的偏好的建议</span>
              <el-button @click="generateSuggestions" size="small" type="primary" style="float: right;">
                生成建议
              </el-button>
            </template>
            
            <div class="suggestions-list">
              <div 
                v-for="suggestion in personalizedSuggestions"
                :key="suggestion.type"
                class="suggestion-item"
              >
                <div class="suggestion-header">
                  <el-tag :type="getSuggestionTagType(suggestion.priority)">
                    {{ suggestion.priority.toUpperCase() }}
                  </el-tag>
                  <strong>{{ suggestion.type }}</strong>
                </div>
                <div class="suggestion-reason">
                  {{ suggestion.reason }}
                </div>
              </div>
              
              <el-empty v-if="!personalizedSuggestions.length" description="点击生成建议按钮获取个性化建议" />
            </div>
          </el-card>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 保存按钮 -->
    <div class="panel-footer">
      <el-button @click="savePreferences" type="primary" :loading="saving">
        保存设置
      </el-button>
      <el-button @click="resetToDefaults">
        恢复默认
      </el-button>
    </div>

    <!-- 导入文件对话框 -->
    <input 
      ref="fileInput" 
      type="file" 
      accept=".json" 
      style="display: none" 
      @change="handleFileImport"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { User, Refresh, More } from '@element-plus/icons-vue'
import { userPreferenceService } from '../services/userPreferenceService'
import { modificationHistoryService } from '../services/modificationHistoryService'

// 响应式数据
const activeTab = ref('style')
const saving = ref(false)
const fileInput = ref(null)

const preferences = reactive({
  writingStyle: {},
  contentPreferences: {},
  qualityStandards: {}
})

const statistics = ref({})
const personalizedSuggestions = ref([])

// 计算属性
const patternDistribution = computed(() => {
  try {
    const history = modificationHistoryService.getHistory({ limit: 100 }) || { records: [] }
    const distribution = {}

    if (history.records && Array.isArray(history.records)) {
      history.records.forEach(record => {
        const category = record.metadata?.category || '未分类'
        distribution[category] = (distribution[category] || 0) + 1
      })
    }

    return distribution
  } catch (error) {
    console.error('计算修改模式分布失败:', error)
    return {}
  }
})

const maxPatternCount = computed(() => {
  const counts = Object.values(patternDistribution.value)
  return Math.max(...counts, 1)
})

// 方法
const refreshData = () => {
  loadPreferences()
  loadStatistics()
}

const loadPreferences = () => {
  const prefs = userPreferenceService.preferences || {}
  Object.assign(preferences.writingStyle, prefs.writingStyle || {})
  Object.assign(preferences.contentPreferences, prefs.contentPreferences || {})
  Object.assign(preferences.qualityStandards, prefs.qualityStandards || {})
}

const loadStatistics = () => {
  try {
    statistics.value = userPreferenceService.getPreferenceStatistics()
  } catch (error) {
    console.error('加载统计数据失败:', error)
    statistics.value = {
      writingStyle: {},
      contentPreferences: {},
      learningMetrics: {
        totalModifications: 0,
        acceptedSuggestions: 0,
        rejectedSuggestions: 0,
        manualModifications: 0
      },
      modificationPatterns: {
        frequentTypes: [],
        preferredSuggestions: [],
        totalModifications: 0
      },
      qualityStandards: {},
      learningProgress: 0
    }
  }
}

const savePreferences = async () => {
  saving.value = true

  try {
    // 确保偏好对象存在
    if (!userPreferenceService.preferences) {
      userPreferenceService.preferences = {}
    }
    if (!userPreferenceService.preferences.writingStyle) {
      userPreferenceService.preferences.writingStyle = {}
    }
    if (!userPreferenceService.preferences.contentPreferences) {
      userPreferenceService.preferences.contentPreferences = {}
    }
    if (!userPreferenceService.preferences.qualityStandards) {
      userPreferenceService.preferences.qualityStandards = {}
    }

    // 更新服务中的偏好设置
    Object.assign(userPreferenceService.preferences.writingStyle, preferences.writingStyle)
    Object.assign(userPreferenceService.preferences.contentPreferences, preferences.contentPreferences)
    Object.assign(userPreferenceService.preferences.qualityStandards, preferences.qualityStandards)

    userPreferenceService.savePreferences()

    ElMessage.success('偏好设置已保存')
  } catch (error) {
    console.error('保存偏好设置失败:', error)
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const resetToDefaults = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要恢复默认设置吗？这将清除所有学习数据。',
      '确认重置',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    userPreferenceService.resetPreferences()
    refreshData()
    ElMessage.success('已恢复默认设置')
  } catch {
    // 用户取消
  }
}

const generateSuggestions = () => {
  try {
    personalizedSuggestions.value = userPreferenceService.getPersonalizedSuggestions('general')
  } catch (error) {
    console.error('生成个性化建议失败:', error)
    personalizedSuggestions.value = []
    ElMessage.error('生成建议失败')
  }
}

const handleMenuCommand = (command) => {
  switch (command) {
    case 'export':
      exportPreferences()
      break
    case 'import':
      importPreferences()
      break
    case 'reset':
      resetToDefaults()
      break
  }
}

const exportPreferences = () => {
  try {
    const data = userPreferenceService.exportPreferences()
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `user_preferences_${new Date().toISOString().split('T')[0]}.json`
    a.click()
    URL.revokeObjectURL(url)
    ElMessage.success('偏好设置已导出')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  }
}

const importPreferences = () => {
  fileInput.value.click()
}

const handleFileImport = (event) => {
  const file = event.target.files[0]
  if (!file) return
  
  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const data = JSON.parse(e.target.result)
      const success = userPreferenceService.importPreferences(data)
      
      if (success) {
        refreshData()
        ElMessage.success('偏好设置已导入')
      } else {
        ElMessage.error('导入失败：数据格式错误')
      }
    } catch (error) {
      console.error('导入失败:', error)
      ElMessage.error('导入失败：文件格式错误')
    }
  }
  reader.readAsText(file)
  
  // 清空文件输入
  event.target.value = ''
}

// 辅助方法
const getProgressColor = (percentage) => {
  if (percentage < 30) return '#f56c6c'
  if (percentage < 70) return '#e6a23c'
  return '#67c23a'
}

const getSuggestionTagType = (priority) => {
  const typeMap = {
    high: 'danger',
    medium: 'warning',
    low: 'info'
  }
  return typeMap[priority] || 'info'
}

// 生命周期
onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.user-preference-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--border-light);
}

.panel-header h3 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-primary);
}

.header-actions {
  display: flex;
  gap: 8px;
}

.learning-progress {
  margin-bottom: 20px;
}

.progress-content {
  padding: 16px 0;
}

.progress-stats {
  margin-top: 20px;
}

.preference-tabs {
  flex: 1;
  overflow: hidden;
}

.style-preferences,
.content-preferences,
.quality-preferences {
  padding: 20px 0;
}

.slider-value {
  margin-left: 12px;
  color: var(--text-secondary);
  font-weight: 500;
}

.input-suffix {
  margin-left: 8px;
  color: var(--text-muted);
}

.pattern-preferences {
  padding: 20px 0;
}

.pattern-list {
  min-height: 100px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: flex-start;
}

.pattern-tag {
  margin: 2px;
}

.pattern-chart {
  padding: 16px 0;
}

.pattern-bar {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  gap: 12px;
}

.pattern-name {
  min-width: 100px;
  font-size: 14px;
  color: var(--text-secondary);
}

.bar-container {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
}

.bar-fill {
  height: 8px;
  background: var(--primary-color);
  border-radius: 4px;
  transition: width 0.3s ease;
  min-width: 2px;
}

.bar-count {
  font-size: 12px;
  color: var(--text-muted);
  min-width: 20px;
}

.personalized-suggestions {
  padding: 20px 0;
}

.suggestions-list {
  min-height: 200px;
}

.suggestion-item {
  padding: 16px;
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  margin-bottom: 12px;
  background: var(--bg-primary);
  transition: all 0.3s ease;
}

.suggestion-item:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-paper-sm);
}

.suggestion-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.suggestion-reason {
  color: var(--text-secondary);
  font-size: 14px;
  line-height: 1.5;
}

.panel-footer {
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid var(--border-light);
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .user-preference-panel {
    padding: 12px;
  }

  .panel-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: center;
  }

  .progress-stats .el-row {
    flex-direction: column;
  }

  .pattern-bar {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .bar-container {
    width: 100%;
  }

  .panel-footer {
    justify-content: stretch;
  }

  .panel-footer .el-button {
    flex: 1;
  }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .suggestion-item {
    background: var(--bg-secondary);
  }

  .bar-fill {
    background: var(--primary-color-light);
  }
}
</style>
