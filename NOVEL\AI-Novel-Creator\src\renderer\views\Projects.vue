<template>
  <div class="projects">
    <div class="projects-header">
      <h2>项目管理</h2>
      <el-button type="primary" @click="showCreateDialog = true">
        <el-icon><CirclePlus /></el-icon>
        新建项目
      </el-button>
    </div>

    <div class="projects-content">
      <!-- 项目统计卡片 -->
      <div class="stats-cards">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-number">{{ projectStats.total }}</div>
            <div class="stat-label">总项目数</div>
          </div>
        </el-card>
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-number">{{ projectStats.totalChapters }}</div>
            <div class="stat-label">总章节数</div>
          </div>
        </el-card>
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-number">{{ formatNumber(projectStats.totalWords) }}</div>
            <div class="stat-label">总字数</div>
          </div>
        </el-card>
      </div>

      <!-- 项目列表 -->
      <div class="projects-section">
        <div class="section-header" v-if="displayProjects.length > 0">
          <h3>项目列表</h3>
          <div class="header-actions">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索项目..."
              style="width: 300px"
              clearable
              class="search-input"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
        </div>

        <!-- 卡片网格布局 -->
        <div class="projects-grid" v-if="displayProjects.length > 0">
          <div
            v-for="project in displayProjects"
            :key="project.id"
            class="project-card"
            @click="openProject(project)"
          >
            <div class="card-header">
              <div class="project-info">
                <h4 class="project-title">{{ project.title }}</h4>
                <p class="project-meta">{{ project.genre }} · {{ project.category }}</p>
              </div>
              <el-dropdown @command="(command) => handleCommand(command, project)" @click.stop>
                <el-button class="card-menu-btn" circle size="small">
                  <el-icon><MoreFilled /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="edit">编辑项目</el-dropdown-item>
                    <el-dropdown-item command="export">导出项目</el-dropdown-item>
                    <el-dropdown-item command="duplicate">复制项目</el-dropdown-item>
                    <el-dropdown-item command="delete" divided>删除项目</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>

            <div class="card-content">
              <div class="progress-section">
                <div class="progress-header">
                  <span class="progress-label">创作进度</span>
                  <span class="progress-percentage">
                    {{ Math.round((project.chaptersCount || 0) / (project.targetChapters || 1) * 100) }}%
                  </span>
                </div>
                <el-progress
                  :percentage="Math.round((project.chaptersCount || 0) / (project.targetChapters || 1) * 100)"
                  :stroke-width="8"
                  :show-text="false"
                  class="progress-bar"
                />
              </div>

              <div class="stats-row">
                <div class="stat-item">
                  <div class="stat-icon">
                    <el-icon><Document /></el-icon>
                  </div>
                  <div class="stat-info">
                    <span class="stat-value">{{ project.chaptersCount || 0 }}</span>
                    <span class="stat-label">章节</span>
                  </div>
                </div>

                <div class="stat-item">
                  <div class="stat-icon">
                    <el-icon><Edit /></el-icon>
                  </div>
                  <div class="stat-info">
                    <span class="stat-value">{{ formatNumber(project.totalWords || 0) }}</span>
                    <span class="stat-label">字数</span>
                  </div>
                </div>
              </div>
            </div>

            <div class="card-footer">
              <div class="status-badge">
                <el-tag
                  :type="project.status === 'active' ? 'success' : 'info'"
                  size="small"
                  effect="light"
                >
                  {{ project.status === 'active' ? '进行中' : '已完成' }}
                </el-tag>
              </div>
              <div class="update-time">
                {{ formatTime(project.updatedAt) }}
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-else class="empty-state">
          <div class="empty-icon">
            <el-icon size="80"><Document /></el-icon>
          </div>
          <h3>还没有项目</h3>
          <p>创建您的第一个AI小说项目开始创作吧！</p>
          <el-button type="primary" size="large" @click="showCreateDialog = true" class="create-first-btn">
            <el-icon><Plus /></el-icon>
            创建第一个项目
          </el-button>
        </div>
      </div>
    </div>

    <!-- 创建项目对话框 -->
    <el-dialog v-model="showCreateDialog" title="创建新项目" width="600px">
      <el-form :model="newProject" :rules="rules" ref="projectForm" label-width="100px">
        <el-form-item label="项目名称" prop="title">
          <el-input v-model="newProject.title" placeholder="请输入项目名称" />
        </el-form-item>
        <el-form-item label="题材" prop="genre">
          <el-select v-model="newProject.genre" placeholder="请选择题材" style="width: 100%">
            <el-option label="玄幻" value="玄幻" />
            <el-option label="都市" value="都市" />
            <el-option label="科幻" value="科幻" />
            <el-option label="历史" value="历史" />
            <el-option label="军事" value="军事" />
            <el-option label="游戏" value="游戏" />
            <el-option label="体育" value="体育" />
            <el-option label="灵异" value="灵异" />
          </el-select>
        </el-form-item>
        <el-form-item label="类型" prop="category">
          <el-select v-model="newProject.category" placeholder="请选择类型" style="width: 100%">
            <el-option label="男频" value="男频" />
            <el-option label="女频" value="女频" />
          </el-select>
        </el-form-item>
        <el-form-item label="创作模式">
          <el-radio-group v-model="newProject.mode">
            <el-radio label="auto">全自动创作</el-radio>
            <el-radio label="assist">辅助创作</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="项目描述">
          <el-input 
            v-model="newProject.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入项目描述（可选）"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="createProject" :loading="creating">创建</el-button>
      </template>
    </el-dialog>

    <!-- 编辑项目对话框 -->
    <el-dialog v-model="showEditDialog" title="编辑项目" width="600px">
      <el-form :model="editingProject" :rules="rules" ref="editForm" label-width="100px">
        <el-form-item label="项目名称" prop="title">
          <el-input v-model="editingProject.title" placeholder="请输入项目名称" />
        </el-form-item>
        <el-form-item label="题材" prop="genre">
          <el-select v-model="editingProject.genre" placeholder="请选择题材" style="width: 100%">
            <el-option label="玄幻" value="玄幻" />
            <el-option label="都市" value="都市" />
            <el-option label="科幻" value="科幻" />
            <el-option label="历史" value="历史" />
            <el-option label="军事" value="军事" />
            <el-option label="游戏" value="游戏" />
            <el-option label="体育" value="体育" />
            <el-option label="灵异" value="灵异" />
          </el-select>
        </el-form-item>
        <el-form-item label="类型" prop="category">
          <el-select v-model="editingProject.category" placeholder="请选择类型" style="width: 100%">
            <el-option label="男频" value="男频" />
            <el-option label="女频" value="女频" />
          </el-select>
        </el-form-item>
        <el-form-item label="项目描述">
          <el-input 
            v-model="editingProject.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入项目描述（可选）"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showEditDialog = false">取消</el-button>
        <el-button type="primary" @click="updateProject" :loading="updating">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'

export default {
  name: 'Projects',
  setup() {
    const router = useRouter()

    // 模拟store数据
    const mockProjectStore = {
      projects: []
    }
    
    const showCreateDialog = ref(false)
    const showEditDialog = ref(false)
    const projectForm = ref(null)
    const editForm = ref(null)
    const creating = ref(false)
    const updating = ref(false)
    const searchKeyword = ref('')
    
    const projects = computed(() => mockProjectStore.projects)

    // 项目统计
    const projectStats = computed(() => {
      const stats = {
        total: projects.value.length,
        totalChapters: 0,
        totalWords: 0
      }

      projects.value.forEach(project => {
        // 这里需要计算每个项目的章节数和字数
        // 暂时使用模拟数据
        stats.totalChapters += project.chaptersCount || 0
        stats.totalWords += project.totalWords || 0
      })

      return stats
    })
    
    // 搜索过滤后的项目
    const displayProjects = computed(() => {
      if (!searchKeyword.value) {
        return projects.value
      }
      
      return projects.value.filter(project => 
        project.title.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
        project.genre.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
        (project.description && project.description.toLowerCase().includes(searchKeyword.value.toLowerCase()))
      )
    })
    
    const newProject = ref({
      title: '',
      genre: '',
      category: '',
      mode: 'auto',
      description: ''
    })
    
    const editingProject = ref({
      id: '',
      title: '',
      genre: '',
      category: '',
      description: ''
    })
    
    const rules = {
      title: [
        { required: true, message: '请输入项目名称', trigger: 'blur' },
        { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
      ],
      genre: [
        { required: true, message: '请选择题材', trigger: 'change' }
      ],
      category: [
        { required: true, message: '请选择类型', trigger: 'change' }
      ]
    }
    
    // 格式化数字
    const formatNumber = (num) => {
      if (num >= 10000) {
        return (num / 10000).toFixed(1) + '万'
      }
      return num.toString()
    }
    
    // 格式化时间
    const formatTime = (time) => {
      return timeUtils.getRelativeTime(time)
    }
    
    // 创建项目
    const createProject = async () => {
      if (!projectForm.value) return
      
      try {
        creating.value = true
        await projectForm.value.validate()
        
        const result = await projectStore.createProject(newProject.value)
        
        if (result.success) {
          ElMessage({
            message: '项目创建成功',
            type: 'success'
          })
          
          showCreateDialog.value = false
          resetForm()
          
          // 如果是全自动创作，跳转到创作页面
          if (newProject.value.mode === 'auto') {
            await projectStore.setCurrentProject(result.data.id)
            router.push('/auto-create')
          } else {
            // 辅助创作直接跳转到编辑器
            await projectStore.setCurrentProject(result.data.id)
            router.push('/editor')
          }
        } else {
          ElMessage({
            message: result.message || '项目创建失败',
            type: 'error'
          })
        }
        
      } catch (error) {
        console.error('创建项目失败:', error)
        ElMessage({
          message: '表单验证失败，请检查输入',
          type: 'error'
        })
      } finally {
        creating.value = false
      }
    }
    
    // 重置表单
    const resetForm = () => {
      newProject.value = {
        title: '',
        genre: '',
        category: '',
        mode: 'auto',
        description: ''
      }
      if (projectForm.value) {
        projectForm.value.resetFields()
      }
    }
    
    // 打开项目
    const openProject = async (project) => {
      try {
        await projectStore.setCurrentProject(project.id)
        
        ElMessage({
          message: `已打开项目：${project.title}`,
          type: 'success'
        })
        
        // 跳转到编辑器
        router.push('/editor')
      } catch (error) {
        ElMessage({
          message: '打开项目失败',
          type: 'error'
        })
      }
    }
    
    // 编辑项目
    const editProject = (project) => {
      editingProject.value = {
        id: project.id,
        title: project.title,
        genre: project.genre,
        category: project.category,
        description: project.description || ''
      }
      showEditDialog.value = true
    }
    
    // 更新项目
    const updateProject = async () => {
      if (!editForm.value) return
      
      try {
        updating.value = true
        await editForm.value.validate()
        
        const result = await projectStore.updateProject(editingProject.value.id, {
          title: editingProject.value.title,
          genre: editingProject.value.genre,
          category: editingProject.value.category,
          description: editingProject.value.description
        })
        
        if (result.success) {
          ElMessage({
            message: '项目更新成功',
            type: 'success'
          })
          showEditDialog.value = false
        } else {
          ElMessage({
            message: result.message || '项目更新失败',
            type: 'error'
          })
        }
        
      } catch (error) {
        console.error('更新项目失败:', error)
        ElMessage({
          message: '表单验证失败，请检查输入',
          type: 'error'
        })
      } finally {
        updating.value = false
      }
    }
    
    // 处理下拉菜单命令
    const handleCommand = async (command, project) => {
      switch (command) {
        case 'export':
          await exportProject(project)
          break
        case 'duplicate':
          await duplicateProject(project)
          break
        case 'delete':
          await deleteProject(project)
          break
      }
    }
    
    // 导出项目
    const exportProject = async (project) => {
      try {
        await projectStore.setCurrentProject(project.id)
        const result = await projectStore.exportProject('txt')
        
        if (result.success) {
          // 触发下载
          const blob = new Blob([result.data], { type: 'text/plain;charset=utf-8' })
          const url = URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = `${project.title}.txt`
          document.body.appendChild(a)
          a.click()
          document.body.removeChild(a)
          URL.revokeObjectURL(url)
          
          ElMessage({
            message: '导出成功',
            type: 'success'
          })
        } else {
          ElMessage({
            message: result.message || '导出失败',
            type: 'error'
          })
        }
      } catch (error) {
        ElMessage({
          message: '导出失败',
          type: 'error'
        })
      }
    }
    
    // 复制项目
    const duplicateProject = async (project) => {
      try {
        const newProjectData = {
          title: project.title + ' - 副本',
          genre: project.genre,
          category: project.category,
          description: project.description,
          mode: project.mode || 'assist'
        }
        
        const result = await projectStore.createProject(newProjectData)
        
        if (result.success) {
          ElMessage({
            message: '项目复制成功',
            type: 'success'
          })
        } else {
          ElMessage({
            message: result.message || '项目复制失败',
            type: 'error'
          })
        }
      } catch (error) {
        ElMessage({
          message: '项目复制失败',
          type: 'error'
        })
      }
    }
    
    // 删除项目
    const deleteProject = async (project) => {
      try {
        await ElMessageBox.confirm(
          `确定要删除项目 "${project.title}" 吗？此操作将删除所有相关的章节和数据，且不可恢复。`,
          '删除确认',
          {
            confirmButtonText: '确定删除',
            cancelButtonText: '取消',
            type: 'warning',
            dangerouslyUseHTMLString: true
          }
        )
        
        const result = await projectStore.deleteProject(project.id)
        
        if (result.success) {
          ElMessage({
            message: '项目删除成功',
            type: 'success'
          })
        } else {
          ElMessage({
            message: result.message || '项目删除失败',
            type: 'error'
          })
        }
        
      } catch (error) {
        // 用户取消删除
      }
    }
    
    // 初始化
    onMounted(async () => {
      console.log('项目管理页面初始化...')
      try {
        await projectStore.loadProjects()
        console.log('项目列表加载完成:', projectStore.projects.length)
      } catch (error) {
        console.error('加载项目列表失败:', error)
      }
    })
    
    return {
      projects,
      projectStats,
      displayProjects,
      showCreateDialog,
      showEditDialog,
      projectForm,
      editForm,
      newProject,
      editingProject,
      rules,
      creating,
      updating,
      searchKeyword,
      formatNumber,
      formatTime,
      createProject,
      resetForm,
      openProject,
      editProject,
      updateProject,
      handleCommand
    }
  }
}
</script>

<style scoped>
.projects {
  max-width: 1400px;
  margin: 0 auto;
  padding: 32px;
}

.projects-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
}

.projects-header h2 {
  margin: 0;
  color: var(--text-primary);
  font-size: 32px;
  font-weight: var(--font-weight-bold);
  font-family: var(--font-display);
  position: relative;
  letter-spacing: -0.5px;
}

.projects-header h2::after {
  content: '';
  position: absolute;
  bottom: -6px;
  left: 0;
  width: 60px;
  height: 2px;
  background: var(--accent-warm);
  border-radius: var(--radius-xs);
}

.projects-content {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

/* 统计卡片 */
.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.stat-card {
  text-align: center;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.stat-item {
  padding: 24px;
}

.stat-number {
  font-size: 36px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

/* 项目列表区域 */
.projects-section {
  margin-bottom: 32px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.section-header h3 {
  font-size: 24px;
  font-weight: 600;
  color: #1a202c;
  margin: 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.search-input {
  border-radius: 12px;
}

/* 项目网格 */
.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  gap: 24px;
}

/* 项目卡片 */
.project-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-subtle);
  box-shadow: var(--shadow-paper-md);
  transition: all var(--duration-normal) var(--ease-out-quart);
  cursor: pointer;
  overflow: hidden;
  position: relative;
}

.project-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--accent-warm);
}

.project-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-paper-xl);
  border-color: var(--border-soft);
}

.card-header {
  padding: 24px 24px 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.project-info {
  flex: 1;
}

.project-title {
  font-size: 18px;
  font-weight: var(--font-weight-semibold);
  font-family: var(--font-display);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-sm) 0;
  line-height: 1.4;
  letter-spacing: -0.2px;
}

.project-meta {
  font-size: 13px;
  color: var(--text-muted);
  margin: 0;
  font-family: var(--font-body);
  font-weight: var(--font-weight-normal);
}

.card-menu-btn {
  background: rgba(0, 0, 0, 0.05);
  border: none;
  color: #64748b;
  transition: all 0.3s ease;
}

.card-menu-btn:hover {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

/* 卡片内容 */
.card-content {
  padding: 0 24px 16px 24px;
}

.progress-section {
  margin-bottom: 20px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.progress-label {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

.progress-percentage {
  font-size: 14px;
  font-weight: 600;
  color: #667eea;
}

.progress-bar {
  border-radius: 8px;
}

.stats-row {
  display: flex;
  gap: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  padding: 16px;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(102, 126, 234, 0.1);
}

.stat-icon {
  width: 36px;
  height: 36px;
  background: var(--accent-warm);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--paper-pure);
  font-size: 16px;
  border: 1px solid var(--accent-warm);
  transition: all var(--duration-fast) var(--ease-out-quart);
}

.stat-icon:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-paper-sm);
}

.stat-info {
  display: flex;
  flex-direction: column;
}

.stat-value {
  font-size: 16px;
  font-weight: var(--font-weight-semibold);
  font-family: var(--font-display);
  color: var(--text-primary);
  line-height: 1.2;
}

.stat-label {
  font-size: 11px;
  color: var(--text-muted);
  margin-top: var(--spacing-xs);
  font-family: var(--font-body);
  font-weight: var(--font-weight-medium);
}

/* 卡片底部 */
.card-footer {
  padding: 16px 24px 24px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.status-badge {
  display: flex;
  align-items: center;
}

.update-time {
  font-size: 12px;
  color: #64748b;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80px 40px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
}

.empty-icon {
  margin-bottom: 24px;
  color: #cbd5e0;
}

.empty-state h3 {
  font-size: 24px;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 12px 0;
}

.empty-state p {
  font-size: 16px;
  color: #64748b;
  margin: 0 0 32px 0;
  line-height: 1.6;
}

.create-first-btn {
  height: 48px;
  padding: 0 32px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.create-first-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .projects {
    padding: 16px;
  }

  .projects-header {
    flex-direction: column;
    gap: 20px;
    align-items: stretch;
  }

  .stats-cards {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .projects-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .section-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: center;
  }

  .search-input {
    width: 100% !important;
  }

  .project-card {
    margin: 0;
  }

  .stats-row {
    flex-direction: column;
    gap: 12px;
  }

  .empty-state {
    padding: 60px 24px;
    align-items: stretch;
  }
  
  .table-header {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }
}

/* 空状态 */
.el-empty {
  padding: 60px 20px;
}
</style>