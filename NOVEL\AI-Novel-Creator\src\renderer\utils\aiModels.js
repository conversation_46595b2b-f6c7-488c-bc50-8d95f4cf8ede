// AI模型数据库
export const AI_PROVIDERS = {
  gemini: {
    name: 'Google Gemini',
    icon: '🤖',
    description: 'Google最新的多模态AI模型',
    defaultUrl: 'https://generativelanguage.googleapis.com/v1beta',
    models: [
      {
        id: 'gemini-2.0-flash-exp',
        name: 'Gemini 2.0 Flash (实验版)',
        description: '最新的Gemini 2.0 Flash实验版本，极速响应',
        maxTokens: 1000000,
        recommended: true
      },
      {
        id: 'gemini-1.5-pro',
        name: 'Gemini 1.5 Pro',
        description: '强大的Gemini Pro模型，支持长上下文',
        maxTokens: 2000000,
        recommended: true
      },
      {
        id: 'gemini-1.5-flash',
        name: 'Gemini 1.5 Flash',
        description: '快速响应的Gemini模型',
        maxTokens: 1000000,
        recommended: false
      },
      {
        id: 'gemini-1.5-flash-8b',
        name: 'Gemini 1.5 Flash 8B',
        description: '轻量级的Gemini Flash模型',
        maxTokens: 1000000,
        recommended: false
      },
      {
        id: 'gemini-pro',
        name: 'Gemini Pro',
        description: '经典的Gemini Pro模型',
        maxTokens: 32768,
        recommended: false
      },
      {
        id: 'gemini-pro-vision',
        name: 'Gemini Pro Vision',
        description: '支持图像理解的Gemini模型',
        maxTokens: 16384,
        recommended: false
      }
    ],
    commonUrls: [
      'https://generativelanguage.googleapis.com/v1beta',
      'https://api.gemini.com/v1',
      'https://gemini-api.openai-proxy.com/v1',
      'https://api.chatanywhere.tech/v1'
    ]
  },
  
  openai: {
    name: 'OpenAI GPT',
    icon: '🧠',
    description: 'OpenAI的GPT系列模型',
    defaultUrl: 'https://api.openai.com/v1',
    models: [
      {
        id: 'gpt-4o',
        name: 'GPT-4o',
        description: '最新的GPT-4 Omni模型',
        maxTokens: 128000,
        recommended: true
      },
      {
        id: 'gpt-4-turbo',
        name: 'GPT-4 Turbo',
        description: '更快的GPT-4模型',
        maxTokens: 128000,
        recommended: true
      },
      {
        id: 'gpt-4',
        name: 'GPT-4',
        description: '经典的GPT-4模型',
        maxTokens: 8192,
        recommended: false
      },
      {
        id: 'gpt-3.5-turbo',
        name: 'GPT-3.5 Turbo',
        description: '性价比高的GPT模型',
        maxTokens: 16385,
        recommended: false
      }
    ],
    commonUrls: [
      'https://api.openai.com/v1',
      'https://api.chatanywhere.tech/v1',
      'https://api.openai-proxy.com/v1',
      'https://openai.api2d.net/v1'
    ]
  },
  
  claude: {
    name: 'Anthropic Claude',
    icon: '🎭',
    description: 'Anthropic的Claude系列模型',
    defaultUrl: 'https://api.anthropic.com/v1',
    models: [
      {
        id: 'claude-3-5-sonnet-20241022',
        name: 'Claude 3.5 Sonnet',
        description: '最新的Claude 3.5 Sonnet模型',
        maxTokens: 200000,
        recommended: true
      },
      {
        id: 'claude-3-opus-20240229',
        name: 'Claude 3 Opus',
        description: '最强大的Claude 3模型',
        maxTokens: 200000,
        recommended: true
      },
      {
        id: 'claude-3-sonnet-20240229',
        name: 'Claude 3 Sonnet',
        description: '平衡性能的Claude 3模型',
        maxTokens: 200000,
        recommended: false
      },
      {
        id: 'claude-3-haiku-20240307',
        name: 'Claude 3 Haiku',
        description: '快速响应的Claude 3模型',
        maxTokens: 200000,
        recommended: false
      }
    ],
    commonUrls: [
      'https://api.anthropic.com/v1',
      'https://claude-api.openai-proxy.com/v1',
      'https://api.claude-ai.com/v1'
    ]
  },
  
  wenxin: {
    name: '百度文心',
    icon: '🐼',
    description: '百度文心大模型',
    defaultUrl: 'https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop',
    models: [
      {
        id: 'ernie-4.0-8k',
        name: 'ERNIE 4.0 8K',
        description: '文心4.0模型，8K上下文',
        maxTokens: 8192,
        recommended: true
      },
      {
        id: 'ernie-3.5-8k',
        name: 'ERNIE 3.5 8K',
        description: '文心3.5模型，8K上下文',
        maxTokens: 8192,
        recommended: false
      },
      {
        id: 'ernie-turbo-8k',
        name: 'ERNIE Turbo 8K',
        description: '文心Turbo模型，快速响应',
        maxTokens: 8192,
        recommended: false
      }
    ],
    commonUrls: [
      'https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop',
      'https://wenxin-api.openai-proxy.com/v1'
    ]
  },
  
  tongyi: {
    name: '阿里通义',
    icon: '🌟',
    description: '阿里巴巴通义千问大模型',
    defaultUrl: 'https://dashscope.aliyuncs.com/api/v1',
    models: [
      {
        id: 'qwen-max',
        name: 'Qwen Max',
        description: '通义千问最强模型',
        maxTokens: 32768,
        recommended: true
      },
      {
        id: 'qwen-plus',
        name: 'Qwen Plus',
        description: '通义千问增强模型',
        maxTokens: 32768,
        recommended: false
      },
      {
        id: 'qwen-turbo',
        name: 'Qwen Turbo',
        description: '通义千问快速模型',
        maxTokens: 8192,
        recommended: false
      }
    ],
    commonUrls: [
      'https://dashscope.aliyuncs.com/api/v1',
      'https://tongyi-api.openai-proxy.com/v1'
    ]
  }
}

// 搜索模型
export function searchModels(provider, query = '') {
  if (!provider || !AI_PROVIDERS[provider]) {
    return []
  }
  
  const models = AI_PROVIDERS[provider].models
  
  if (!query) {
    return models
  }
  
  const lowerQuery = query.toLowerCase()
  return models.filter(model => 
    model.id.toLowerCase().includes(lowerQuery) ||
    model.name.toLowerCase().includes(lowerQuery) ||
    model.description.toLowerCase().includes(lowerQuery)
  )
}

// 获取推荐模型
export function getRecommendedModels(provider) {
  if (!provider || !AI_PROVIDERS[provider]) {
    return []
  }
  
  return AI_PROVIDERS[provider].models.filter(model => model.recommended)
}

// 获取默认URL
export function getDefaultUrl(provider) {
  if (!provider || !AI_PROVIDERS[provider]) {
    return ''
  }
  
  return AI_PROVIDERS[provider].defaultUrl
}

// 获取常用URL列表
export function getCommonUrls(provider) {
  if (!provider || !AI_PROVIDERS[provider]) {
    return []
  }
  
  return AI_PROVIDERS[provider].commonUrls || []
}

// 验证API配置
export function validateApiConfig(config) {
  const errors = []
  
  if (!config.provider) {
    errors.push('请选择AI服务提供商')
  }
  
  if (!config.apiKey) {
    errors.push('请输入API Key')
  }
  
  if (!config.model) {
    errors.push('请选择模型')
  }
  
  if (config.apiUrl && !isValidUrl(config.apiUrl)) {
    errors.push('API地址格式不正确')
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

// 验证URL格式
function isValidUrl(string) {
  try {
    new URL(string)
    return true
  } catch (_) {
    return false
  }
}
