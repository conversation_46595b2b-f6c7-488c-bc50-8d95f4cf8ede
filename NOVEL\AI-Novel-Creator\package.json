{"name": "ai-novel-creator", "version": "1.0.0", "description": "AI驱动的网文小说创作助手", "main": "src/main/index.js", "scripts": {"start": "node start-app.js", "dev": "concurrently \"npm run dev:renderer\" \"npm run dev:main:delayed\"", "dev:main:delayed": "wait-on http://localhost:3000 && npm run dev:main", "dev:main": "electron src/main/index.js", "dev:renderer": "vite", "build": "vite build", "build:electron": "electron-builder --config electron-builder.json", "build:win": "electron-builder --win --config electron-builder.json", "build:mac": "electron-builder --mac --config electron-builder.json", "build:linux": "electron-builder --linux --config electron-builder.json", "build:android": "npm run build && npx cap copy android && npx cap build android", "preview": "vite preview", "dist": "npm run build && npm run build:electron"}, "keywords": ["AI", "小说", "创作", "Gemini", "网文"], "author": "Novel Creator Team", "license": "MIT", "devDependencies": {"@capacitor/android": "^5.4.0", "@capacitor/app": "^5.0.6", "@capacitor/cli": "^5.4.0", "@capacitor/core": "^5.4.0", "@capacitor/device": "^5.0.6", "@capacitor/filesystem": "^5.1.4", "@capacitor/haptics": "^5.0.6", "@capacitor/keyboard": "^5.0.6", "@capacitor/network": "^5.0.6", "@capacitor/share": "^5.0.6", "@capacitor/status-bar": "^5.0.6", "@types/node": "^24.0.14", "@vitejs/plugin-vue": "^4.4.0", "concurrently": "^8.2.2", "electron": "^26.6.10", "electron-builder": "^24.6.4", "typescript": "^5.8.3", "vite": "^4.4.9", "vue-tsc": "^3.0.1", "wait-on": "^8.0.3"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "@google/generative-ai": "^0.1.3", "axios": "^1.5.0", "element-plus": "^2.3.14", "fs-extra": "^11.1.1", "pinia": "^2.1.6", "vue": "^3.3.4", "vue-router": "^4.2.5"}, "build": {"appId": "com.novelcreator.app", "productName": "AI Novel Creator", "directories": {"output": "dist"}, "files": ["src/main/**/*", "src/renderer/dist/**/*", "package.json"], "win": {"target": "nsis", "icon": "public/icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}}}