<template>
  <div class="ui-showcase">
    <!-- 页面标题 -->
    <div class="page-header beautiful-card rainbow-border fade-in-up">
      <div class="header-content">
        <h1 class="page-title gradient-text elegant">UI组件展示</h1>
        <p class="page-subtitle">现代美学设计组件库</p>
      </div>
      <div class="header-decoration floating">
        <div class="decoration-icon pulsing">
          <el-icon><StarFilled /></el-icon>
        </div>
      </div>
    </div>

    <!-- 美观组件展示 -->
    <div class="showcase-section beautiful-card glow-effect fade-in-up">
      <h2 class="section-title gradient-text royal">美观组件系统</h2>
      <div class="beautiful-components">
        <div class="component-demo">
          <h3>精美卡片</h3>
          <div class="demo-cards">
            <div class="demo-card beautiful-card">
              <h4>标准卡片</h4>
              <p>具有玻璃态效果和精美阴影</p>
            </div>
            <div class="demo-card beautiful-card glow-effect">
              <h4>发光卡片</h4>
              <p>悬浮时具有发光效果</p>
            </div>
            <div class="demo-card beautiful-card rainbow-border">
              <h4>彩虹边框</h4>
              <p>动态彩虹边框效果</p>
            </div>
          </div>
        </div>

        <div class="component-demo">
          <h3>渐变按钮</h3>
          <div class="demo-buttons">
            <button class="gradient-btn">渐变按钮</button>
            <button class="elegant-btn">优雅按钮</button>
            <button class="gradient-btn glow-effect">发光按钮</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 配色展示 -->
    <div class="showcase-section beautiful-card fade-in-up">
      <h2 class="section-title gradient-text purple">精致配色系统</h2>
      <div class="color-palette">
        <div class="color-group">
          <h3>传统墨色</h3>
          <div class="color-swatches">
            <div class="color-swatch" style="background: var(--ink-jiao);" title="墨焦"></div>
            <div class="color-swatch" style="background: var(--ink-nong);" title="墨浓"></div>
            <div class="color-swatch" style="background: var(--ink-zhong);" title="墨重"></div>
            <div class="color-swatch" style="background: var(--ink-dan);" title="墨淡"></div>
            <div class="color-swatch" style="background: var(--ink-qing);" title="墨清"></div>
          </div>
        </div>

        <div class="color-group">
          <h3>文雅色彩</h3>
          <div class="color-swatches">
            <div class="color-swatch" style="background: var(--mo-lan);" title="墨兰"></div>
            <div class="color-swatch" style="background: var(--zhu-sha);" title="朱砂"></div>
            <div class="color-swatch" style="background: var(--song-lv);" title="松绿"></div>
            <div class="color-swatch" style="background: var(--zi-tan);" title="紫檀"></div>
            <div class="color-swatch" style="background: var(--yan-zhi);" title="胭脂"></div>
          </div>
        </div>

        <div class="color-group">
          <h3>文雅渐变</h3>
          <div class="gradient-swatches">
            <div class="gradient-swatch" style="background: var(--gradient-elegant);" title="典雅渐变"></div>
            <div class="gradient-swatch" style="background: var(--gradient-purple);" title="墨兰渐变"></div>
            <div class="gradient-swatch" style="background: var(--gradient-warm);" title="温润渐变"></div>
            <div class="gradient-swatch" style="background: var(--gradient-cool);" title="清雅渐变"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 按钮展示 -->
    <div class="showcase-section elegant-card fade-in-up">
      <h2 class="section-title gradient-text jade">现代化按钮</h2>
      <div class="component-grid">
        <div class="component-item">
          <h3>基础按钮</h3>
          <div class="button-group">
            <ModernButton variant="primary" text="主要按钮" />
            <ModernButton variant="secondary" text="次要按钮" />
            <ModernButton variant="success" text="成功按钮" />
            <ModernButton variant="warning" text="警告按钮" />
            <ModernButton variant="danger" text="危险按钮" />
          </div>
        </div>
        
        <div class="component-item">
          <h3>特效按钮</h3>
          <div class="button-group">
            <ModernButton variant="primary" text="渐变按钮" gradient />
            <ModernButton variant="secondary" text="玻璃按钮" glass />
            <ModernButton variant="info" text="新拟态按钮" neumorphism />
            <ModernButton variant="primary" text="装饰按钮" decorative />
          </div>
        </div>
        
        <div class="component-item">
          <h3>图标按钮</h3>
          <div class="button-group">
            <ModernButton variant="primary" text="带图标" icon="StarFilled" />
            <ModernButton variant="secondary" text="浮动图标" icon="EditPen" icon-float />
            <ModernButton variant="success" icon="CircleCheck" icon-only />
            <ModernButton variant="primary" text="加载中" loading />
          </div>
        </div>
      </div>
    </div>

    <!-- 加载组件展示 -->
    <div class="showcase-section glass-card fade-in-up">
      <h2 class="section-title gradient-text">加载组件</h2>
      <div class="component-grid">
        <div class="component-item">
          <h3>基础加载</h3>
          <ModernLoading title="正在处理" text="请稍候..." />
        </div>
        
        <div class="component-item">
          <h3>进度加载</h3>
          <ModernLoading 
            title="正在生成内容" 
            text="AI正在为您创作..." 
            :progress="loadingProgress"
            show-progress
            cancelable
          />
        </div>
      </div>
      
      <div class="demo-controls">
        <ModernButton 
          variant="primary" 
          text="显示全屏加载" 
          @click="showFullscreenLoading"
        />
        <ModernButton 
          variant="secondary" 
          text="显示通知" 
          @click="showToast"
        />
      </div>
    </div>

    <!-- 卡片展示 -->
    <div class="showcase-section glass-card fade-in-up">
      <h2 class="section-title gradient-text">卡片组件</h2>
      <div class="cards-grid">
        <div class="demo-card glass-card">
          <div class="card-header">
            <div class="card-icon floating">
              <el-icon><Document /></el-icon>
            </div>
            <div class="card-content">
              <h3 class="card-title gradient-text">玻璃态卡片</h3>
              <p class="card-desc">现代化的玻璃态效果</p>
            </div>
          </div>
        </div>
        
        <div class="demo-card neumorphism-card">
          <div class="card-header">
            <div class="card-icon">
              <el-icon><EditPen /></el-icon>
            </div>
            <div class="card-content">
              <h3 class="card-title">新拟态卡片</h3>
              <p class="card-desc">柔和的新拟态设计</p>
            </div>
          </div>
        </div>
        
        <div class="demo-card gradient-card">
          <div class="card-header">
            <div class="card-icon">
              <el-icon><StarFilled /></el-icon>
            </div>
            <div class="card-content">
              <h3 class="card-title">渐变卡片</h3>
              <p class="card-desc">优雅的渐变背景</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 动画展示 -->
    <div class="showcase-section glass-card fade-in-up">
      <h2 class="section-title gradient-text">动画效果</h2>
      <div class="animation-grid">
        <div class="animation-item">
          <div class="demo-element floating">
            <el-icon><StarFilled /></el-icon>
            <span>浮动动画</span>
          </div>
        </div>
        
        <div class="animation-item">
          <div class="demo-element pulsing">
            <el-icon><CircleCheck /></el-icon>
            <span>脉冲动画</span>
          </div>
        </div>
        
        <div class="animation-item">
          <div class="demo-element gradient-text">
            <span>渐变文字</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 全屏加载 -->
    <ModernLoading 
      v-if="showLoading"
      title="正在处理您的请求"
      text="请稍候，这可能需要几秒钟..."
      :progress="fullscreenProgress"
      show-progress
      fullscreen
      cancelable
      @cancel="hideFullscreenLoading"
    />

    <!-- 通知容器 -->
    <div class="toast-container">
      <ModernToast 
        v-for="toast in toasts"
        :key="toast.id"
        :type="toast.type"
        :title="toast.title"
        :message="toast.message"
        @close="removeToast(toast.id)"
      />
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'
import ModernButton from '@/components/ModernButton.vue'
import ModernLoading from '@/components/ModernLoading.vue'
import ModernToast from '@/components/ModernToast.vue'

export default {
  name: 'UIShowcase',
  components: {
    ModernButton,
    ModernLoading,
    ModernToast
  },
  setup() {
    const loadingProgress = ref(0)
    const showLoading = ref(false)
    const fullscreenProgress = ref(0)
    const toasts = ref([])
    
    let progressInterval = null
    let fullscreenInterval = null

    // 模拟进度更新
    const startProgress = () => {
      progressInterval = setInterval(() => {
        loadingProgress.value += Math.random() * 10
        if (loadingProgress.value >= 100) {
          loadingProgress.value = 100
          setTimeout(() => {
            loadingProgress.value = 0
          }, 1000)
        }
      }, 500)
    }

    // 显示全屏加载
    const showFullscreenLoading = () => {
      showLoading.value = true
      fullscreenProgress.value = 0
      
      fullscreenInterval = setInterval(() => {
        fullscreenProgress.value += Math.random() * 15
        if (fullscreenProgress.value >= 100) {
          fullscreenProgress.value = 100
          setTimeout(() => {
            hideFullscreenLoading()
          }, 1000)
        }
      }, 300)
    }

    // 隐藏全屏加载
    const hideFullscreenLoading = () => {
      showLoading.value = false
      if (fullscreenInterval) {
        clearInterval(fullscreenInterval)
        fullscreenInterval = null
      }
    }

    // 显示通知
    const showToast = () => {
      const types = ['success', 'warning', 'error', 'info']
      const messages = [
        { title: '操作成功', message: '您的操作已成功完成！' },
        { title: '注意', message: '请检查您的输入信息。' },
        { title: '错误', message: '操作失败，请重试。' },
        { title: '提示', message: '这是一条信息提示。' }
      ]
      
      const randomType = types[Math.floor(Math.random() * types.length)]
      const randomMessage = messages[Math.floor(Math.random() * messages.length)]
      
      const toast = {
        id: Date.now(),
        type: randomType,
        title: randomMessage.title,
        message: randomMessage.message
      }
      
      toasts.value.push(toast)
    }

    // 移除通知
    const removeToast = (id) => {
      const index = toasts.value.findIndex(toast => toast.id === id)
      if (index > -1) {
        toasts.value.splice(index, 1)
      }
    }

    onMounted(() => {
      startProgress()
    })

    onUnmounted(() => {
      if (progressInterval) clearInterval(progressInterval)
      if (fullscreenInterval) clearInterval(fullscreenInterval)
    })

    return {
      loadingProgress,
      showLoading,
      fullscreenProgress,
      toasts,
      showFullscreenLoading,
      hideFullscreenLoading,
      showToast,
      removeToast
    }
  }
}
</script>

<style scoped>
/* 引入水墨书香主题 */
@import '@/assets/styles/modern-theme.css';

.ui-showcase {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3xl);
}

/* 页面标题 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-xl);
}

.header-content {
  flex: 1;
}

.page-title {
  font-family: var(--font-calligraphy);
  font-size: 32px;
  font-weight: var(--font-weight-semibold);
  margin: 0 0 var(--spacing-sm) 0;
}

.page-subtitle {
  font-family: var(--font-elegant);
  font-size: 14px;
  color: var(--text-muted);
  margin: 0;
}

.header-decoration {
  width: 64px;
  height: 64px;
  background: var(--gradient-gold);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: var(--ink-jiao);
}

/* 展示区域 */
.showcase-section {
  padding: var(--spacing-3xl);
}

.section-title {
  font-family: var(--font-calligraphy);
  font-size: 24px;
  font-weight: var(--font-weight-semibold);
  margin: 0 0 var(--spacing-xl) 0;
  text-align: center;
}

/* 组件网格 */
.component-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-xl);
}

.component-item {
  text-align: center;
}

.component-item h3 {
  font-family: var(--font-calligraphy);
  font-size: 16px;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-lg) 0;
}

.button-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  align-items: center;
}

/* 演示控制 */
.demo-controls {
  display: flex;
  justify-content: center;
  gap: var(--spacing-lg);
  margin-top: var(--spacing-xl);
  padding-top: var(--spacing-xl);
  border-top: 1px solid var(--border-light);
}

/* 卡片网格 */
.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-xl);
}

.demo-card {
  padding: var(--spacing-xl);
  border-radius: var(--radius-xl);
  transition: all var(--duration-normal) var(--ease-paper);
}

.demo-card:hover {
  transform: translateY(-4px);
}

.neumorphism-card {
  background: var(--bg-primary);
  box-shadow: var(--neumorphism-light);
}

.gradient-card {
  background: var(--gradient-warm);
  color: var(--ink-jiao);
}

.card-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.card-icon {
  width: 48px;
  height: 48px;
  background: var(--huang-jin);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: var(--ink-jiao);
}

.card-content {
  flex: 1;
}

.card-title {
  font-family: var(--font-calligraphy);
  font-size: 16px;
  margin: 0 0 var(--spacing-xs) 0;
}

.card-desc {
  font-family: var(--font-elegant);
  font-size: 13px;
  color: var(--text-muted);
  margin: 0;
}

/* 动画网格 */
.animation-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-xl);
}

.animation-item {
  text-align: center;
  padding: var(--spacing-xl);
  background: var(--bg-elevated);
  border-radius: var(--radius-lg);
}

.demo-element {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
  font-family: var(--font-elegant);
  font-size: 14px;
  color: var(--text-primary);
}

.demo-element .el-icon {
  font-size: 24px;
}

/* 通知容器 */
.toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  pointer-events: none;
}

.toast-container > * {
  pointer-events: auto;
}

/* 配色展示 */
.color-palette {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-xl);
  margin-top: var(--spacing-xl);
}

.color-group h3 {
  font-family: var(--font-calligraphy);
  font-size: 16px;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-md) 0;
  text-align: center;
}

.color-swatches,
.gradient-swatches {
  display: flex;
  gap: var(--spacing-sm);
  justify-content: center;
  flex-wrap: wrap;
}

.color-swatch,
.gradient-swatch {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid var(--border-light);
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-paper);
  position: relative;
}

.color-swatch:hover,
.gradient-swatch:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-paper-md);
}

.gradient-swatch {
  border-radius: var(--radius-lg);
}

/* 美观组件展示 */
.beautiful-components {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3xl);
  margin-top: var(--spacing-xl);
}

.component-demo h3 {
  font-family: var(--font-calligraphy);
  font-size: 18px;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-lg) 0;
  text-align: center;
}

.demo-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
}

.demo-card {
  padding: var(--spacing-xl);
  text-align: center;
}

.demo-card h4 {
  font-family: var(--font-calligraphy);
  font-size: 16px;
  margin: 0 0 var(--spacing-sm) 0;
  background: var(--gradient-royal);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.demo-card p {
  font-family: var(--font-elegant);
  font-size: 13px;
  color: var(--text-muted);
  margin: 0;
  line-height: 1.5;
}

.demo-buttons {
  display: flex;
  justify-content: center;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: var(--spacing-lg);
    text-align: center;
  }

  .demo-controls {
    flex-direction: column;
    align-items: center;
  }

  .component-grid,
  .cards-grid,
  .animation-grid,
  .color-palette {
    grid-template-columns: 1fr;
  }

  .demo-cards {
    grid-template-columns: 1fr;
  }

  .demo-buttons {
    flex-direction: column;
    align-items: center;
  }

  .toast-container {
    left: 20px;
    right: 20px;
  }

  .color-swatches,
  .gradient-swatches {
    justify-content: center;
  }
}
</style>
