<template>
  <div class="ink-projects">
    <!-- 页面标题 -->
    <div class="page-header beautiful-card fade-in-up">
      <div class="header-content">
        <h1 class="page-title gradient-text elegant">书案管理</h1>
        <p class="page-subtitle">管理您的创作项目</p>
      </div>
      <button class="create-btn gradient-btn floating" @click="showCreateDialog = true">
        <el-icon><CirclePlus /></el-icon>
        <span>新建项目</span>
      </button>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <div class="stat-card beautiful-card glow-effect fade-in-up">
        <div class="stat-icon floating">
          <el-icon><Document /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number gradient-text purple">{{ projectStats.total }}</div>
          <div class="stat-label">总项目数</div>
        </div>
      </div>

      <div class="stat-card beautiful-card glow-effect fade-in-up" style="animation-delay: 0.1s;">
        <div class="stat-icon floating" style="animation-delay: 0.5s;">
          <el-icon><EditPen /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number gradient-text jade">{{ projectStats.totalChapters }}</div>
          <div class="stat-label">总章节数</div>
        </div>
      </div>

      <div class="stat-card beautiful-card glow-effect fade-in-up" style="animation-delay: 0.2s;">
        <div class="stat-icon floating" style="animation-delay: 1s;">
          <el-icon><Notebook /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number gradient-text warm">{{ formatNumber(projectStats.totalWords) }}</div>
          <div class="stat-label">总字数</div>
        </div>
      </div>
    </div>

    <!-- 项目列表 -->
    <div class="projects-section">
      <div v-if="projects.length === 0" class="empty-state">
        <div class="empty-icon">
          <el-icon><FolderOpened /></el-icon>
        </div>
        <h3>暂无项目</h3>
        <p>开始您的第一个创作项目吧</p>
        <button class="create-first-btn" @click="showCreateDialog = true">
          <el-icon><CirclePlus /></el-icon>
          创建第一个项目
        </button>
      </div>
      
      <div v-else class="projects-grid">
        <div v-for="project in projects" :key="project.id" class="project-card">
          <div class="project-header">
            <h3 class="project-title">{{ project.title }}</h3>
            <div class="project-meta">
              <span class="project-genre">{{ project.genre }}</span>
              <span class="project-category">{{ project.category }}</span>
            </div>
          </div>
          
          <div class="project-description">
            {{ project.description || '暂无描述' }}
          </div>
          
          <div class="project-stats">
            <div class="stat-item">
              <span class="stat-value">{{ project.chaptersCount || 0 }}</span>
              <span class="stat-unit">章</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">{{ formatNumber(project.totalWords || 0) }}</span>
              <span class="stat-unit">字</span>
            </div>
          </div>
          
          <div class="project-actions">
            <button class="action-btn primary" @click="openProject(project)">
              <el-icon><EditPen /></el-icon>
              继续创作
            </button>
            <button class="action-btn secondary" @click="editProject(project)">
              <el-icon><Setting /></el-icon>
              编辑
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建项目对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="新建项目"
      width="500px"
      class="ink-dialog"
    >
      <el-form :model="newProject" label-width="80px">
        <el-form-item label="项目名称">
          <el-input v-model="newProject.title" placeholder="请输入项目名称" />
        </el-form-item>
        <el-form-item label="题材">
          <el-select v-model="newProject.genre" placeholder="请选择题材" style="width: 100%">
            <el-option label="玄幻" value="玄幻" />
            <el-option label="都市" value="都市" />
            <el-option label="科幻" value="科幻" />
            <el-option label="历史" value="历史" />
            <el-option label="军事" value="军事" />
            <el-option label="游戏" value="游戏" />
          </el-select>
        </el-form-item>
        <el-form-item label="类型">
          <el-select v-model="newProject.category" placeholder="请选择类型" style="width: 100%">
            <el-option label="男频" value="男频" />
            <el-option label="女频" value="女频" />
          </el-select>
        </el-form-item>
        <el-form-item label="项目描述">
          <el-input 
            v-model="newProject.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入项目描述（可选）"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="createProject">创建</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

export default {
  name: 'ProjectsSimple',
  setup() {
    const router = useRouter()
    
    // 响应式数据
    const showCreateDialog = ref(false)
    const projects = ref([])
    
    // 新建项目表单
    const newProject = ref({
      title: '',
      genre: '',
      category: '',
      description: ''
    })

    // 项目统计
    const projectStats = computed(() => {
      const stats = {
        total: projects.value.length,
        totalChapters: 0,
        totalWords: 0
      }
      
      projects.value.forEach(project => {
        stats.totalChapters += project.chaptersCount || 0
        stats.totalWords += project.totalWords || 0
      })
      
      return stats
    })

    // 格式化数字
    const formatNumber = (num) => {
      if (num >= 10000) {
        return (num / 10000).toFixed(1) + '万'
      }
      return num.toString()
    }

    // 创建项目
    const createProject = () => {
      if (!newProject.value.title || !newProject.value.genre || !newProject.value.category) {
        ElMessage({
          message: '请填写完整的项目信息',
          type: 'warning'
        })
        return
      }

      const project = {
        id: Date.now().toString(),
        title: newProject.value.title,
        genre: newProject.value.genre,
        category: newProject.value.category,
        description: newProject.value.description,
        chaptersCount: 0,
        totalWords: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      projects.value.push(project)
      
      ElMessage({
        message: '项目创建成功',
        type: 'success'
      })

      // 重置表单
      newProject.value = {
        title: '',
        genre: '',
        category: '',
        description: ''
      }
      
      showCreateDialog.value = false
    }

    // 打开项目
    const openProject = (project) => {
      ElMessage({
        message: `已打开项目：${project.title}`,
        type: 'success'
      })
      router.push('/editor')
    }

    // 编辑项目
    const editProject = (project) => {
      ElMessage({
        message: '编辑功能开发中...',
        type: 'info'
      })
    }

    return {
      showCreateDialog,
      projects,
      newProject,
      projectStats,
      formatNumber,
      createProject,
      openProject,
      editProject
    }
  }
}
</script>

<style scoped>
/* 引入水墨书香主题 */
@import '@/assets/styles/modern-theme.css';

.ink-projects {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0;
}

/* 页面标题 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-3xl);
  padding: var(--spacing-xl);
  background: var(--gradient-paper);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-paper-md);
}

.header-content {
  flex: 1;
}

.page-title {
  font-family: var(--font-calligraphy);
  font-size: 32px;
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-sm) 0;
  letter-spacing: 0.03em;
}

.page-subtitle {
  font-family: var(--font-elegant);
  font-size: 14px;
  color: var(--text-muted);
  margin: 0;
}

.create-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-xl);
  background: var(--gradient-gold);
  border: 2px solid var(--huang-jin);
  border-radius: var(--radius-lg);
  color: var(--ink-jiao);
  font-family: var(--font-elegant);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-paper);
}

.create-btn:hover {
  background: #f1c40f;
  transform: translateY(-2px);
  box-shadow: var(--shadow-paper-lg);
}

/* 统计卡片 */
.stats-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-3xl);
}

.stat-card {
  background: var(--bg-primary);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  box-shadow: var(--shadow-paper-md);
  transition: all var(--duration-normal) var(--ease-paper);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-paper-lg);
}

.stat-icon {
  width: 48px;
  height: 48px;
  background: var(--huang-jin);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--ink-jiao);
  font-size: 20px;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-family: var(--font-calligraphy);
  font-size: 24px;
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  font-family: var(--font-elegant);
  font-size: 14px;
  color: var(--text-muted);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: var(--spacing-4xl);
  background: var(--gradient-paper);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-paper-md);
}

.empty-icon {
  width: 80px;
  height: 80px;
  background: var(--bg-elevated);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--spacing-xl);
  font-size: 32px;
  color: var(--text-placeholder);
}

.empty-state h3 {
  font-family: var(--font-calligraphy);
  font-size: 24px;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-md) 0;
}

.empty-state p {
  font-family: var(--font-elegant);
  color: var(--text-muted);
  margin: 0 0 var(--spacing-xl) 0;
}

.create-first-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg) var(--spacing-xl);
  background: var(--gradient-gold);
  border: 2px solid var(--huang-jin);
  border-radius: var(--radius-lg);
  color: var(--ink-jiao);
  font-family: var(--font-elegant);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-paper);
}

.create-first-btn:hover {
  background: #f1c40f;
  transform: translateY(-2px);
  box-shadow: var(--shadow-paper-lg);
}

/* 项目网格 */
.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: var(--spacing-xl);
}

.project-card {
  background: var(--bg-primary);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-paper-md);
  transition: all var(--duration-normal) var(--ease-paper);
  position: relative;
  overflow: hidden;
}

.project-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--gradient-gold);
  opacity: 0.6;
}

.project-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-paper-xl);
  border-color: var(--border-accent);
}

.project-header {
  margin-bottom: var(--spacing-lg);
}

.project-title {
  font-family: var(--font-calligraphy);
  font-size: 18px;
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-sm) 0;
}

.project-meta {
  display: flex;
  gap: var(--spacing-sm);
}

.project-genre,
.project-category {
  font-family: var(--font-elegant);
  font-size: 12px;
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-sm);
  color: var(--text-muted);
}

.project-description {
  font-family: var(--font-elegant);
  font-size: 14px;
  color: var(--text-muted);
  line-height: 1.5;
  margin-bottom: var(--spacing-lg);
  min-height: 42px;
}

.project-stats {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md);
  background: var(--bg-elevated);
  border-radius: var(--radius-lg);
}

.stat-item {
  display: flex;
  align-items: baseline;
  gap: var(--spacing-xs);
}

.stat-value {
  font-family: var(--font-calligraphy);
  font-size: 16px;
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.stat-unit {
  font-family: var(--font-elegant);
  font-size: 12px;
  color: var(--text-muted);
}

.project-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid;
  border-radius: var(--radius-lg);
  font-family: var(--font-elegant);
  font-size: 13px;
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-paper);
}

.action-btn.primary {
  background: var(--gradient-jade);
  border-color: var(--song-lv);
  color: var(--paper-xuan);
}

.action-btn.primary:hover {
  background: var(--dan-qing);
  transform: translateY(-1px);
}

.action-btn.secondary {
  background: var(--bg-primary);
  border-color: var(--border-medium);
  color: var(--text-muted);
}

.action-btn.secondary:hover {
  border-color: var(--border-accent);
  color: var(--text-primary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: var(--spacing-lg);
    text-align: center;
  }
  
  .stats-section {
    grid-template-columns: 1fr;
  }
  
  .projects-grid {
    grid-template-columns: 1fr;
  }
}
</style>
