<template>
  <div class="simple-dashboard">
    <div class="welcome-card">
      <div class="logo">墨</div>
      <h1>AI小说创作助手</h1>
      <p>欢迎来到墨韵文轩</p>
      <div class="status">
        <p>✅ 应用已成功加载</p>
        <p>🎨 水墨书香界面正常显示</p>
        <p>🚀 所有功能准备就绪</p>
      </div>
      <div class="actions">
        <button @click="$router.push('/projects')" class="btn primary">
          📚 项目管理
        </button>
        <button @click="$router.push('/auto-create')" class="btn secondary">
          ✍️ 智能创作
        </button>
        <button @click="$router.push('/editor')" class="btn secondary">
          📝 编辑器
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SimpleDashboard',
  mounted() {
    console.log('SimpleDashboard component mounted')
  }
}
</script>

<style scoped>
.simple-dashboard {
  padding: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100%;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.welcome-card {
  background: white;
  padding: 40px;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  text-align: center;
  max-width: 500px;
  width: 100%;
}

.logo {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  font-size: 32px;
  font-weight: bold;
  color: white;
  font-family: 'Microsoft YaHei', serif;
}

h1 {
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 28px;
}

p {
  color: #7f8c8d;
  margin-bottom: 20px;
  font-size: 16px;
}

.status {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
  margin: 20px 0;
}

.status p {
  margin: 8px 0;
  font-size: 14px;
  color: #27ae60;
}

.actions {
  display: flex;
  gap: 10px;
  justify-content: center;
  flex-wrap: wrap;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.btn.primary {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
}

.btn.secondary {
  background: #f8f9fa;
  color: #2c3e50;
  border: 1px solid #ddd;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}
</style>
