// 简化版数据库服务 - 使用localStorage
class DatabaseService {
  constructor() {
    this.isInitialized = false
  }

  // 初始化数据库
  async initialize() {
    try {
      // 检查localStorage是否可用
      if (typeof Storage === 'undefined') {
        throw new Error('localStorage不可用')
      }
      
      // 初始化默认数据
      this.initializeDefaultData()
      
      this.isInitialized = true
      return true
    } catch (error) {
      console.error('Failed to initialize database:', error)
      throw error
    }
  }

  // 初始化默认数据
  initializeDefaultData() {
    if (!localStorage.getItem('novel_projects')) {
      localStorage.setItem('novel_projects', JSON.stringify([]))
    }
    if (!localStorage.getItem('novel_chapters')) {
      localStorage.setItem('novel_chapters', JSON.stringify([]))
    }
    if (!localStorage.getItem('novel_characters')) {
      localStorage.setItem('novel_characters', JSON.stringify([]))
    }
    if (!localStorage.getItem('novel_outlines')) {
      localStorage.setItem('novel_outlines', JSON.stringify([]))
    }
  }

  // 项目相关操作
  async createProject(project) {
    const projects = JSON.parse(localStorage.getItem('novel_projects') || '[]')
    const newProject = {
      ...project,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    projects.push(newProject)
    localStorage.setItem('novel_projects', JSON.stringify(projects))
    return newProject
  }

  async getProject(id) {
    const projects = JSON.parse(localStorage.getItem('novel_projects') || '[]')
    return projects.find(p => p.id === id)
  }

  async getAllProjects() {
    const projects = JSON.parse(localStorage.getItem('novel_projects') || '[]')
    return projects.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt))
  }

  async updateProject(id, updates) {
    const projects = JSON.parse(localStorage.getItem('novel_projects') || '[]')
    const index = projects.findIndex(p => p.id === id)
    if (index !== -1) {
      projects[index] = { 
        ...projects[index], 
        ...updates, 
        updatedAt: new Date().toISOString() 
      }
      localStorage.setItem('novel_projects', JSON.stringify(projects))
      return projects[index]
    }
    return null
  }

  async deleteProject(id) {
    const projects = JSON.parse(localStorage.getItem('novel_projects') || '[]')
    const filteredProjects = projects.filter(p => p.id !== id)
    localStorage.setItem('novel_projects', JSON.stringify(filteredProjects))
    
    // 删除相关章节
    const chapters = JSON.parse(localStorage.getItem('novel_chapters') || '[]')
    const filteredChapters = chapters.filter(c => c.projectId !== id)
    localStorage.setItem('novel_chapters', JSON.stringify(filteredChapters))
    
    return { changes: projects.length - filteredProjects.length }
  }

  // 章节相关操作
  async createChapter(chapter) {
    const chapters = JSON.parse(localStorage.getItem('novel_chapters') || '[]')
    const newChapter = {
      ...chapter,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    chapters.push(newChapter)
    localStorage.setItem('novel_chapters', JSON.stringify(chapters))
    return newChapter
  }

  async getChapter(id) {
    const chapters = JSON.parse(localStorage.getItem('novel_chapters') || '[]')
    return chapters.find(c => c.id === id)
  }

  async getProjectChapters(projectId) {
    const chapters = JSON.parse(localStorage.getItem('novel_chapters') || '[]')
    return chapters
      .filter(c => c.projectId === projectId)
      .sort((a, b) => a.chapterNumber - b.chapterNumber)
  }

  async updateChapter(id, updates) {
    const chapters = JSON.parse(localStorage.getItem('novel_chapters') || '[]')
    const index = chapters.findIndex(c => c.id === id)
    if (index !== -1) {
      chapters[index] = { 
        ...chapters[index], 
        ...updates, 
        updatedAt: new Date().toISOString() 
      }
      localStorage.setItem('novel_chapters', JSON.stringify(chapters))
      return chapters[index]
    }
    return null
  }

  async deleteChapter(id) {
    const chapters = JSON.parse(localStorage.getItem('novel_chapters') || '[]')
    const filteredChapters = chapters.filter(c => c.id !== id)
    localStorage.setItem('novel_chapters', JSON.stringify(filteredChapters))
    return { changes: chapters.length - filteredChapters.length }
  }

  // 人物相关操作
  async createCharacter(character) {
    const characters = JSON.parse(localStorage.getItem('novel_characters') || '[]')
    const newCharacter = {
      ...character,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    characters.push(newCharacter)
    localStorage.setItem('novel_characters', JSON.stringify(characters))
    return newCharacter
  }

  async getCharacter(id) {
    const characters = JSON.parse(localStorage.getItem('novel_characters') || '[]')
    return characters.find(c => c.id === id)
  }

  async getProjectCharacters(projectId) {
    const characters = JSON.parse(localStorage.getItem('novel_characters') || '[]')
    return characters.filter(c => c.projectId === projectId)
  }

  async updateCharacter(id, updates) {
    const characters = JSON.parse(localStorage.getItem('novel_characters') || '[]')
    const index = characters.findIndex(c => c.id === id)
    if (index !== -1) {
      characters[index] = { 
        ...characters[index], 
        ...updates, 
        updatedAt: new Date().toISOString() 
      }
      localStorage.setItem('novel_characters', JSON.stringify(characters))
      return characters[index]
    }
    return null
  }

  async deleteCharacter(id) {
    const characters = JSON.parse(localStorage.getItem('novel_characters') || '[]')
    const filteredCharacters = characters.filter(c => c.id !== id)
    localStorage.setItem('novel_characters', JSON.stringify(filteredCharacters))
    return { changes: characters.length - filteredCharacters.length }
  }

  // 大纲相关操作
  async saveOutline(outline) {
    const outlines = JSON.parse(localStorage.getItem('novel_outlines') || '[]')
    const existingIndex = outlines.findIndex(o => o.projectId === outline.projectId)
    
    const newOutline = {
      ...outline,
      id: outline.id || Date.now().toString(),
      createdAt: outline.createdAt || new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    
    if (existingIndex !== -1) {
      outlines[existingIndex] = newOutline
    } else {
      outlines.push(newOutline)
    }
    
    localStorage.setItem('novel_outlines', JSON.stringify(outlines))
    return newOutline
  }

  async getOutline(projectId) {
    const outlines = JSON.parse(localStorage.getItem('novel_outlines') || '[]')
    return outlines.find(o => o.projectId === projectId)
  }

  // 设置相关操作
  async setSetting(key, value, type = 'string') {
    localStorage.setItem(`novel_setting_${key}`, JSON.stringify({ value, type }))
    return { changes: 1 }
  }

  async getSetting(key) {
    const item = localStorage.getItem(`novel_setting_${key}`)
    return item ? JSON.parse(item) : null
  }

  async getAllSettings() {
    const settings = []
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith('novel_setting_')) {
        const settingKey = key.replace('novel_setting_', '')
        const value = JSON.parse(localStorage.getItem(key))
        settings.push({
          key: settingKey,
          value: value.value,
          type: value.type
        })
      }
    }
    return settings
  }

  // 创作历史
  async addWritingHistory(history) {
    // 简化实现，暂时不保存历史
    return { changes: 1 }
  }

  async getWritingHistory(projectId, limit = 50) {
    // 简化实现，返回空数组
    return []
  }

  // 关闭数据库连接
  async close() {
    // localStorage不需要关闭连接
    return
  }
}

// 创建单例实例
const dbService = new DatabaseService()

export default dbService