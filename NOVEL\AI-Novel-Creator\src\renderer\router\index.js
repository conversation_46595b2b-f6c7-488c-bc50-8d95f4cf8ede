import { createRouter, createWebHistory } from 'vue-router'
import Dashboard from '../views/Dashboard.vue'
import SimpleDashboard from '../views/SimpleDashboard.vue'
import Projects from '../views/ProjectsSimple.vue'
import AutoCreate from '../views/AutoCreateSimple.vue'
import Editor from '../views/EditorSimple.vue'
import Settings from '../views/SettingsSimple.vue'
import UIShowcase from '../views/UIShowcase.vue'

const routes = [
  {
    path: '/',
    name: 'Root',
    redirect: { name: 'Dashboard' }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: SimpleDashboard,
    meta: { title: '工作台' }
  },
  {
    path: '/dashboard-full',
    name: 'DashboardFull',
    component: Dashboard,
    meta: { title: '完整工作台' }
  },
  {
    path: '/projects',
    name: 'Projects',
    component: Projects,
    meta: { title: '项目管理' }
  },
  {
    path: '/auto-create',
    name: 'AutoCreate',
    component: AutoCreate,
    meta: { title: '全自动创作' }
  },
  {
    path: '/editor',
    name: 'Editor',
    component: Editor,
    meta: { title: '智能编辑器' }
  },
  {
    path: '/settings',
    name: 'Settings',
    component: Settings,
    meta: { title: '设置' }
  },
  {
    path: '/ui-showcase',
    name: 'UIShowcase',
    component: UIShowcase,
    meta: { title: 'UI展示' }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router