<template>
  <div class="editor">
    <div class="editor-container">
      <!-- 工具栏 -->
      <div class="toolbar">
        <div class="toolbar-left">
          <div class="button-group">
            <button class="toolbar-btn" @click="saveContent" title="保存">
              <svg viewBox="0 0 24 24" fill="none">
                <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z" stroke="currentColor" stroke-width="2"/>
                <polyline points="17,21 17,13 7,13 7,21" stroke="currentColor" stroke-width="2"/>
                <polyline points="7,3 7,8 15,8" stroke="currentColor" stroke-width="2"/>
              </svg>
              保存
            </button>
            <button class="toolbar-btn" @click="exportContent" title="导出">
              <svg viewBox="0 0 24 24" fill="none">
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" stroke="currentColor" stroke-width="2"/>
                <polyline points="7,10 12,15 17,10" stroke="currentColor" stroke-width="2"/>
                <line x1="12" y1="15" x2="12" y2="3" stroke="currentColor" stroke-width="2"/>
              </svg>
              导出
            </button>
          </div>
          
          <div class="toolbar-divider"></div>
          
          <div class="button-group">
            <button class="toolbar-btn" @click="undo" title="撤销">
              <svg viewBox="0 0 24 24" fill="none">
                <path d="M1 4v6h6M23 20v-6h-6" stroke="currentColor" stroke-width="2"/>
                <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15" stroke="currentColor" stroke-width="2"/>
              </svg>
              撤销
            </button>
            <button class="toolbar-btn" @click="redo" title="重做">
              <svg viewBox="0 0 24 24" fill="none">
                <path d="M23 4v6h-6M1 20v-6h6" stroke="currentColor" stroke-width="2"/>
                <path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 20l4.64-4.36A9 9 0 0 0 20.49 15" stroke="currentColor" stroke-width="2"/>
              </svg>
              重做
            </button>
          </div>
        </div>
        
        <div class="toolbar-right">
          <span class="word-count">字数: {{ wordCount }}</span>
          <div class="toolbar-divider"></div>
          <button class="toolbar-btn ai-btn" @click="showAssistPanel = !showAssistPanel" :class="{ active: showAssistPanel }" title="AI助手">
            <svg viewBox="0 0 24 24" fill="none">
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" stroke="currentColor" stroke-width="2"/>
            </svg>
            AI助手
          </button>
        </div>
      </div>
      
      <!-- 编辑区域 -->
      <div class="editor-content">
        <!-- 主编辑器 -->
        <div class="main-editor" :class="{ 'with-assist': showAssistPanel }">
          <div class="chapter-selector">
            <select v-model="currentChapter" @change="loadChapter" class="chapter-select">
              <option value="">选择章节</option>
              <option 
                v-for="chapter in chapters" 
                :key="chapter.id" 
                :value="chapter.id"
              >
                {{ chapter.title }}
              </option>
            </select>
            <button class="add-chapter-btn" @click="addChapter" title="新建章节">
              <svg viewBox="0 0 24 24" fill="none">
                <line x1="12" y1="5" x2="12" y2="19" stroke="currentColor" stroke-width="2"/>
                <line x1="5" y1="12" x2="19" y2="12" stroke="currentColor" stroke-width="2"/>
              </svg>
              新建章节
            </button>
          </div>
          
          <div class="chapter-title">
            <input 
              v-model="chapterTitle" 
              type="text"
              placeholder="章节标题"
              @input="updateChapterTitle"
              class="title-input"
            />
          </div>
          
          <div class="text-editor">
            <textarea 
              ref="textEditor"
              v-model="content"
              @input="handleInput"
              @select="handleTextSelect"
              placeholder="在这里开始写作..."
              class="editor-textarea"
            />
          </div>
        </div>
        
        <!-- AI辅助面板 -->
        <div v-if="showAssistPanel" class="assist-panel">
          <div class="assist-header">
            <h4>AI写作助手</h4>
            <button class="close-btn" @click="showAssistPanel = false" title="关闭">
              <svg viewBox="0 0 24 24" fill="none">
                <line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" stroke-width="2"/>
                <line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" stroke-width="2"/>
              </svg>
            </button>
          </div>
          
          <div class="assist-tabs">
            <div class="tab-nav">
              <button 
                v-for="tab in assistTabs" 
                :key="tab.key"
                class="tab-btn"
                :class="{ active: assistTab === tab.key }"
                @click="assistTab = tab.key"
              >
                {{ tab.label }}
              </button>
            </div>

            <div class="tab-content">
              <!-- 智能续写 -->
              <div v-if="assistTab === 'continue'" class="assist-content">
                <p class="assist-tip">选中文本后点击续写，AI将根据上下文继续创作</p>
                <button 
                  class="assist-btn primary" 
                  @click="continueWriting"
                  :disabled="!selectedText"
                  :class="{ loading: generating }"
                >
                  <svg v-if="!generating" viewBox="0 0 24 24" fill="none">
                    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" stroke="currentColor" stroke-width="2"/>
                    <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" stroke="currentColor" stroke-width="2"/>
                  </svg>
                  <svg v-else viewBox="0 0 24 24" fill="none" class="spinning">
                    <path d="M21 12a9 9 0 11-6.219-8.56" stroke="currentColor" stroke-width="2"/>
                  </svg>
                  {{ generating ? '生成中...' : '续写文本' }}
                </button>
                <div v-if="generatedText" class="generated-text">
                  <h5>AI生成内容：</h5>
                  <div class="text-preview">{{ generatedText }}</div>
                  <div class="text-actions">
                    <button class="action-btn apply" @click="applyGenerated">应用</button>
                    <button class="action-btn regenerate" @click="regenerateText">重新生成</button>
                  </div>
                </div>
              </div>
              
              <!-- 文笔优化 -->
              <div v-if="assistTab === 'optimize'" class="assist-content">
                <p class="assist-tip">选中需要优化的文本</p>
                <button 
                  class="assist-btn primary" 
                  @click="optimizeText"
                  :disabled="!selectedText"
                  :class="{ loading: generating }"
                >
                  <svg v-if="!generating" viewBox="0 0 24 24" fill="none">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" stroke="currentColor" stroke-width="2"/>
                  </svg>
                  <svg v-else viewBox="0 0 24 24" fill="none" class="spinning">
                    <path d="M21 12a9 9 0 11-6.219-8.56" stroke="currentColor" stroke-width="2"/>
                  </svg>
                  {{ generating ? '优化中...' : '优化文笔' }}
                </button>
                <div v-if="optimizedText" class="generated-text">
                  <h5>优化后内容：</h5>
                  <div class="text-preview">{{ optimizedText }}</div>
                  <div class="text-actions">
                    <button class="action-btn apply" @click="applyOptimized">应用</button>
                    <button class="action-btn regenerate" @click="reoptimizeText">重新优化</button>
                  </div>
                </div>
              </div>
              
              <!-- 情节建议 -->
              <div v-if="assistTab === 'plot'" class="assist-content">
                <p class="assist-tip">获取剧情发展建议</p>
                <button 
                  class="assist-btn primary" 
                  @click="getPlotSuggestions"
                  :class="{ loading: generating }"
                >
                  <svg v-if="!generating" viewBox="0 0 24 24" fill="none">
                    <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                    <path d="M12 1v6m0 6v6" stroke="currentColor" stroke-width="2"/>
                    <path d="M21 12h-6m-6 0H3" stroke="currentColor" stroke-width="2"/>
                  </svg>
                  <svg v-else viewBox="0 0 24 24" fill="none" class="spinning">
                    <path d="M21 12a9 9 0 11-6.219-8.56" stroke="currentColor" stroke-width="2"/>
                  </svg>
                  {{ generating ? '分析中...' : '获取建议' }}
                </button>
                <div v-if="plotSuggestions.length" class="suggestions">
                  <h5>剧情建议：</h5>
                  <div 
                    v-for="(suggestion, index) in plotSuggestions" 
                    :key="index" 
                    class="suggestion-item"
                    @click="applySuggestion(suggestion)"
                  >
                    {{ suggestion }}
                  </div>
                </div>
              </div>

              <!-- 角色对话 -->
              <div v-if="assistTab === 'dialogue'" class="assist-content">
                <p class="assist-tip">生成角色对话</p>
                <div class="dialogue-form">
                  <input 
                    v-model="dialogueCharacter" 
                    type="text" 
                    placeholder="角色名称"
                    class="dialogue-input"
                  />
                  <input 
                    v-model="dialogueContext" 
                    type="text" 
                    placeholder="对话情境"
                    class="dialogue-input"
                  />
                </div>
                <button 
                  class="assist-btn primary" 
                  @click="generateDialogue"
                  :disabled="!dialogueCharacter"
                  :class="{ loading: generating }"
                >
                  <svg v-if="!generating" viewBox="0 0 24 24" fill="none">
                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" stroke="currentColor" stroke-width="2"/>
                  </svg>
                  <svg v-else viewBox="0 0 24 24" fill="none" class="spinning">
                    <path d="M21 12a9 9 0 11-6.219-8.56" stroke="currentColor" stroke-width="2"/>
                  </svg>
                  {{ generating ? '生成中...' : '生成对话' }}
                </button>
                <div v-if="generatedDialogue" class="generated-text">
                  <h5>生成的对话：</h5>
                  <div class="text-preview">{{ generatedDialogue }}</div>
                  <div class="text-actions">
                    <button class="action-btn apply" @click="applyDialogue">应用</button>
                    <button class="action-btn regenerate" @click="regenerateDialogue">重新生成</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, nextTick } from 'vue'
import { aiCreationService } from '../services/aiCreationService.js'

export default {
  name: 'AIEditor',
  setup() {
    // 响应式数据
    const showAssistPanel = ref(false)
    const assistTab = ref('continue')
    const currentChapter = ref('')
    const chapterTitle = ref('')
    const content = ref('')
    const selectedText = ref('')
    const generating = ref(false)
    const generatedText = ref('')
    const optimizedText = ref('')
    const generatedDialogue = ref('')
    const dialogueCharacter = ref('')
    const dialogueContext = ref('')
    const plotSuggestions = ref([])

    // 章节数据
    const chapters = ref([
      { id: '1', title: '第一章 初入江湖' },
      { id: '2', title: '第二章 奇遇连连' },
      { id: '3', title: '第三章 实力觉醒' }
    ])

    // 辅助标签页
    const assistTabs = ref([
      { key: 'continue', label: '智能续写' },
      { key: 'optimize', label: '文笔优化' },
      { key: 'plot', label: '情节建议' },
      { key: 'dialogue', label: '角色对话' }
    ])

    // 计算属性
    const wordCount = computed(() => {
      return content.value.length
    })

    // 方法
    const saveContent = () => {
      alert('保存功能')
    }

    const exportContent = () => {
      alert('导出功能')
    }

    const undo = () => {
      document.execCommand('undo')
    }

    const redo = () => {
      document.execCommand('redo')
    }

    const loadChapter = () => {
      // 加载章节内容
      const chapter = chapters.value.find(c => c.id === currentChapter.value)
      if (chapter) {
        chapterTitle.value = chapter.title
        content.value = `这是${chapter.title}的内容...`
      }
    }

    const addChapter = () => {
      const newId = (chapters.value.length + 1).toString()
      const newChapter = {
        id: newId,
        title: `第${newId}章 新章节`
      }
      chapters.value.push(newChapter)
      currentChapter.value = newId
      loadChapter()
    }

    const updateChapterTitle = () => {
      const chapter = chapters.value.find(c => c.id === currentChapter.value)
      if (chapter) {
        chapter.title = chapterTitle.value
      }
    }

    const handleInput = () => {
      // 处理输入事件
    }

    const handleTextSelect = (event) => {
      const textarea = event.target
      const start = textarea.selectionStart
      const end = textarea.selectionEnd
      selectedText.value = textarea.value.substring(start, end)
    }

    const continueWriting = async () => {
      if (!selectedText.value) {
        alert('请先选择要续写的文本')
        return
      }

      if (!aiCreationService.isConfigured()) {
        alert('请先配置AI服务')
        return
      }

      generating.value = true
      try {
        console.log('开始AI续写...')

        // 获取上下文
        const context = content.value.substring(Math.max(0, content.value.indexOf(selectedText.value) - 500), content.value.indexOf(selectedText.value))

        const result = await aiCreationService.continueWriting(
          selectedText.value,
          context,
          '请保持文风一致，情节自然发展',
          1000
        )

        generatedText.value = result
        console.log('续写完成:', result.length, '字符')

      } catch (error) {
        console.error('续写失败:', error)
        alert('续写失败：' + error.message)
      } finally {
        generating.value = false
      }
    }

    const optimizeText = async () => {
      if (!selectedText.value) {
        alert('请先选择要优化的文本')
        return
      }

      if (!aiCreationService.isConfigured()) {
        alert('请先配置AI服务')
        return
      }

      generating.value = true
      try {
        console.log('开始文笔优化...')

        const result = await aiCreationService.optimizeText({
          text: selectedText.value,
          optimizeType: 'general'
        })

        optimizedText.value = result
        console.log('优化完成:', result.length, '字符')

      } catch (error) {
        console.error('优化失败:', error)
        alert('优化失败：' + error.message)
      } finally {
        generating.value = false
      }
    }

    const getPlotSuggestions = async () => {
      if (!aiCreationService.isConfigured()) {
        alert('请先配置AI服务')
        return
      }

      generating.value = true
      try {
        console.log('开始获取情节建议...')

        const context = content.value || '当前没有内容'
        const suggestions = await aiCreationService.generatePlotSuggestions(
          context,
          '请提供具体可行的情节发展建议'
        )

        plotSuggestions.value = suggestions
        console.log('获取建议完成:', suggestions.length, '个建议')

      } catch (error) {
        console.error('获取建议失败:', error)
        alert('获取建议失败：' + error.message)
      } finally {
        generating.value = false
      }
    }

    const generateDialogue = async () => {
      if (!dialogueCharacter.value) {
        alert('请先输入角色名称')
        return
      }

      if (!aiCreationService.isConfigured()) {
        alert('请先配置AI服务')
        return
      }

      generating.value = true
      try {
        console.log('开始生成对话...')

        const context = content.value || '当前没有上下文'
        const result = await aiCreationService.generateDialogue(
          dialogueCharacter.value,
          context,
          dialogueContext.value || '日常对话'
        )

        generatedDialogue.value = result
        console.log('对话生成完成:', result.length, '字符')

      } catch (error) {
        console.error('生成对话失败:', error)
        alert('生成对话失败：' + error.message)
      } finally {
        generating.value = false
      }
    }

    const applyGenerated = () => {
      const textarea = document.querySelector('.editor-textarea')
      const start = textarea.selectionStart
      const end = textarea.selectionEnd
      const before = content.value.substring(0, start)
      const after = content.value.substring(end)
      content.value = before + generatedText.value + after
      generatedText.value = ''
    }

    const applyOptimized = () => {
      const textarea = document.querySelector('.editor-textarea')
      const start = textarea.selectionStart
      const end = textarea.selectionEnd
      const before = content.value.substring(0, start)
      const after = content.value.substring(end)
      content.value = before + optimizedText.value + after
      optimizedText.value = ''
    }

    const applyDialogue = () => {
      content.value += '\n\n' + generatedDialogue.value
      generatedDialogue.value = ''
    }

    const applySuggestion = (suggestion) => {
      content.value += '\n\n' + suggestion
    }

    const regenerateText = () => {
      continueWriting()
    }

    const reoptimizeText = () => {
      optimizeText()
    }

    const regenerateDialogue = () => {
      generateDialogue()
    }

    return {
      showAssistPanel,
      assistTab,
      currentChapter,
      chapterTitle,
      content,
      selectedText,
      generating,
      generatedText,
      optimizedText,
      generatedDialogue,
      dialogueCharacter,
      dialogueContext,
      plotSuggestions,
      chapters,
      assistTabs,
      wordCount,
      saveContent,
      exportContent,
      undo,
      redo,
      loadChapter,
      addChapter,
      updateChapterTitle,
      handleInput,
      handleTextSelect,
      continueWriting,
      optimizeText,
      getPlotSuggestions,
      generateDialogue,
      applyGenerated,
      applyOptimized,
      applyDialogue,
      applySuggestion,
      regenerateText,
      reoptimizeText,
      regenerateDialogue
    }
  }
}
</script>

<style scoped>
.editor {
  height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.editor-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 工具栏 */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(148, 163, 184, 0.2);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.toolbar-left,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.toolbar-divider {
  width: 1px;
  height: 24px;
  background: rgba(148, 163, 184, 0.3);
}

.toolbar-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border: none;
  border-radius: 8px;
  background: transparent;
  color: #64748b;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.toolbar-btn:hover {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.toolbar-btn svg {
  width: 16px;
  height: 16px;
}

.ai-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.ai-btn:hover,
.ai-btn.active {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.word-count {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

/* 编辑区域 */
.editor-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.main-editor {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  margin: 16px;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.3);
  overflow: hidden;
  transition: all 0.3s ease;
}

.main-editor.with-assist {
  margin-right: 8px;
}

.chapter-selector {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
  background: rgba(248, 250, 252, 0.5);
}

.chapter-select {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: 8px;
  background: white;
  font-size: 14px;
  color: #1e293b;
}

.add-chapter-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border: none;
  border-radius: 8px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.add-chapter-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.add-chapter-btn svg {
  width: 16px;
  height: 16px;
}

.chapter-title {
  padding: 16px 20px;
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
}

.title-input {
  width: 100%;
  padding: 12px 0;
  border: none;
  background: transparent;
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
  outline: none;
}

.title-input::placeholder {
  color: #94a3b8;
}

.text-editor {
  flex: 1;
  padding: 20px;
}

.editor-textarea {
  width: 100%;
  height: 100%;
  border: none;
  background: transparent;
  font-size: 16px;
  line-height: 1.8;
  color: #1e293b;
  resize: none;
  outline: none;
  font-family: 'Georgia', 'Times New Roman', serif;
}

.editor-textarea::placeholder {
  color: #94a3b8;
}

/* AI辅助面板 */
.assist-panel {
  width: 350px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  margin: 16px 16px 16px 8px;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.3);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.assist-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
  background: rgba(248, 250, 252, 0.5);
}

.assist-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.close-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 6px;
  background: transparent;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.close-btn svg {
  width: 16px;
  height: 16px;
}

.assist-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.tab-nav {
  display: flex;
  background: rgba(248, 250, 252, 0.5);
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
}

.tab-btn {
  flex: 1;
  padding: 12px 8px;
  border: none;
  background: transparent;
  color: #64748b;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 2px solid transparent;
}

.tab-btn:hover {
  background: rgba(102, 126, 234, 0.05);
  color: #667eea;
}

.tab-btn.active {
  color: #667eea;
  border-bottom-color: #667eea;
  background: rgba(102, 126, 234, 0.05);
}

.tab-content {
  flex: 1;
  overflow-y: auto;
}

.assist-content {
  padding: 20px;
}

.assist-tip {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: #64748b;
  line-height: 1.5;
}

.assist-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 100%;
  padding: 12px 16px;
  border: none;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 16px;
}

.assist-btn svg {
  width: 16px;
  height: 16px;
}

.assist-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.25);
}

.assist-btn.primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.35);
}

.assist-btn:disabled {
  background: linear-gradient(135deg, #cbd5e1 0%, #94a3b8 100%);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.assist-btn.loading {
  pointer-events: none;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.generated-text {
  margin-top: 16px;
  padding: 16px;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(102, 126, 234, 0.1);
}

.generated-text h5 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #667eea;
}

.text-preview {
  padding: 12px;
  background: white;
  border-radius: 8px;
  font-size: 14px;
  line-height: 1.6;
  color: #1e293b;
  margin-bottom: 12px;
  white-space: pre-wrap;
}

.text-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  flex: 1;
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn.apply {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.action-btn.apply:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.action-btn.regenerate {
  background: rgba(148, 163, 184, 0.1);
  color: #64748b;
  border: 1px solid rgba(148, 163, 184, 0.2);
}

.action-btn.regenerate:hover {
  background: rgba(148, 163, 184, 0.2);
  color: #475569;
}

.suggestions {
  margin-top: 16px;
}

.suggestions h5 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #667eea;
}

.suggestion-item {
  padding: 12px;
  margin-bottom: 8px;
  background: white;
  border-radius: 8px;
  border: 1px solid rgba(148, 163, 184, 0.1);
  font-size: 14px;
  line-height: 1.5;
  color: #1e293b;
  cursor: pointer;
  transition: all 0.2s ease;
}

.suggestion-item:hover {
  background: rgba(102, 126, 234, 0.05);
  border-color: rgba(102, 126, 234, 0.2);
  transform: translateY(-1px);
}

.dialogue-form {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.dialogue-input {
  padding: 10px 12px;
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: 8px;
  background: white;
  font-size: 14px;
  color: #1e293b;
  transition: all 0.2s ease;
}

.dialogue-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.dialogue-input::placeholder {
  color: #94a3b8;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .assist-panel {
    width: 300px;
  }
}

@media (max-width: 768px) {
  .editor-content {
    flex-direction: column;
  }

  .main-editor {
    margin: 8px;
  }

  .main-editor.with-assist {
    margin-right: 8px;
    margin-bottom: 4px;
  }

  .assist-panel {
    width: auto;
    margin: 4px 8px 8px 8px;
    height: 300px;
  }

  .toolbar {
    flex-wrap: wrap;
    gap: 8px;
  }

  .toolbar-left,
  .toolbar-right {
    gap: 8px;
  }

  .button-group {
    gap: 4px;
  }

  .toolbar-btn {
    padding: 6px 8px;
    font-size: 12px;
  }

  .toolbar-btn svg {
    width: 14px;
    height: 14px;
  }
}
</style>
