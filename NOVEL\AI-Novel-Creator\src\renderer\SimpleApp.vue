<template>
  <div id="app">
    <el-container class="app-container">
      <!-- 侧边栏 -->
      <el-aside width="250px" class="sidebar">
        <div class="logo">
          <h2>AI创作助手</h2>
        </div>
        <el-menu
          :default-active="$route.path"
          class="sidebar-menu"
          router
          background-color="#2c3e50"
          text-color="#ecf0f1"
          active-text-color="#3498db"
        >
          <el-menu-item index="/dashboard">
            <el-icon><House /></el-icon>
            <span>工作台</span>
          </el-menu-item>
          <el-menu-item index="/projects">
            <el-icon><Document /></el-icon>
            <span>项目管理</span>
          </el-menu-item>
          <el-menu-item index="/auto-create">
            <el-icon><Star /></el-icon>
            <span>全自动创作</span>
          </el-menu-item>
          <el-menu-item index="/editor">
            <el-icon><Edit /></el-icon>
            <span>智能编辑器</span>
          </el-menu-item>
          <el-menu-item index="/settings">
            <el-icon><Setting /></el-icon>
            <span>设置</span>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <!-- 主内容区 -->
      <el-container>
        <el-header class="header">
          <div class="header-left">
            <el-breadcrumb separator="/">
              <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
              <el-breadcrumb-item>{{ $route.meta.title || '页面' }}</el-breadcrumb-item>
            </el-breadcrumb>
          </div>
          <div class="header-right">
            <!-- AI状态指示器 -->
            <el-tag v-if="aiStore.isReady" type="success" size="small">
              <el-icon><Check /></el-icon>
              AI已连接
            </el-tag>
            <el-tag v-else-if="appStore.config.geminiApiKey" type="warning" size="small">
              <el-icon><Warning /></el-icon>
              AI未连接
            </el-tag>
            <el-tag v-else type="info" size="small">
              <el-icon><Warning /></el-icon>
              未配置API Key
            </el-tag>
            
            <el-button type="primary" size="small" @click="showAbout">
              <el-icon><QuestionFilled /></el-icon>
              关于
            </el-button>
          </div>
        </el-header>

        <el-main class="main-content">
          <router-view />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script>
import { onMounted } from 'vue'
import { useAppStore } from '@/store/app'
import { useAIStore } from '@/store/ai'
import { ElMessage } from 'element-plus'

export default {
  name: 'SimpleApp',
  setup() {
    const appStore = useAppStore()
    const aiStore = useAIStore()
    
    onMounted(async () => {
      try {
        await appStore.initializeApp()
      } catch (error) {
        console.error('App initialization failed:', error)
      }
    })
    
    const showAbout = () => {
      ElMessage({
        message: 'AI网文小说创作助手 v1.0.0',
        type: 'info',
        duration: 3000
      })
    }
    
    return {
      appStore,
      aiStore,
      showAbout
    }
  }
}
</script>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

#app {
  font-family: 'Helvetica Neue', Arial, sans-serif;
  height: 100vh;
  overflow: hidden;
}

.app-container {
  height: 100vh;
}

.sidebar {
  background-color: #2c3e50;
  color: #ecf0f1;
}

.logo {
  padding: 20px;
  text-align: center;
  border-bottom: 1px solid #34495e;
}

.logo h2 {
  color: #3498db;
  font-size: 18px;
  margin: 0;
}

.sidebar-menu {
  border: none;
}

.header {
  background-color: #ffffff;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
  flex: 1;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 10px;
}

.main-content {
  background-color: #f8f9fa;
  overflow-y: auto;
  padding: 20px;
}
</style>