<template>
  <div class="sidebar" :class="{ 'sidebar--collapsed': isCollapsed, 'sidebar--dark': isDarkMode, 'sidebar--light': !isDarkMode }">
    <!-- 用户信息区域 -->
    <div class="sidebar__user">
      <div class="user-avatar">
        <div class="avatar-circle"></div>
      </div>
      <div class="user-info" v-show="!isCollapsed">
        <div class="user-name">用户名</div>
        <div class="user-role">创作工坊</div>
      </div>
      <!-- 收缩按钮 -->
      <button class="collapse-btn" @click="toggleCollapse">
        <div class="collapse-icon" :class="{ 'collapsed': isCollapsed }">
          <span class="icon-bar"></span>
          <span class="icon-bar"></span>
        </div>
      </button>
    </div>

    <!-- 主菜单 -->
    <nav class="sidebar__nav">
      <div class="nav-item" :class="{ 'nav-item--active': activeItem === 'home' }" @click="setActive('home')" :title="isCollapsed ? '首页' : ''">
        <div class="nav-icon">
          <SvgIcon name="home" :size="20" />
        </div>
        <span class="nav-text" v-show="!isCollapsed">首页</span>
      </div>

      <div class="nav-item" :class="{ 'nav-item--active': activeItem === 'projects' }" @click="setActive('projects')" :title="isCollapsed ? '项目' : ''">
        <div class="nav-icon">
          <SvgIcon name="projects" :size="20" />
        </div>
        <span class="nav-text" v-show="!isCollapsed">项目</span>
      </div>

      <div class="nav-item" :class="{ 'nav-item--active': activeItem === 'garden' }" @click="setActive('garden')" :title="isCollapsed ? '妙笔生花' : ''">
        <div class="nav-icon">
          <SvgIcon name="garden" :size="20" />
        </div>
        <span class="nav-text" v-show="!isCollapsed">妙笔生花</span>
      </div>

      <div class="nav-item" :class="{ 'nav-item--active': activeItem === 'library' }" @click="setActive('library')" :title="isCollapsed ? '书架' : ''">
        <div class="nav-icon">
          <SvgIcon name="library" :size="20" />
        </div>
        <span class="nav-text" v-show="!isCollapsed">书架</span>
      </div>

      <div class="nav-item" :class="{ 'nav-item--active': activeItem === 'edit' }" @click="setActive('edit')" :title="isCollapsed ? '挥毫泼墨' : ''">
        <div class="nav-icon">
          <SvgIcon name="edit" :size="20" />
        </div>
        <span class="nav-text" v-show="!isCollapsed">挥毫泼墨</span>
      </div>
    </nav>

    <!-- 工具栏 -->
    <div class="sidebar__tools">
      <div
        class="tool-item"
        :class="{ 'tool-item--active': activeItem === 'settings' }"
        @click="toggleSettings"
        :title="isCollapsed ? '设置' : ''"
      >
        <div class="tool-icon">
          <SvgIcon name="settings" :size="20" />
        </div>
        <span class="tool-text" v-show="!isCollapsed">设置</span>
      </div>
    </div>

    <!-- 底部工具 -->
    <div class="sidebar__bottom">
      <div class="bottom-item" @click="toggleTheme" :title="isCollapsed ? (isDarkMode ? '日间模式' : '夜间模式') : ''">
        <div class="bottom-icon">
          <SvgIcon :name="isDarkMode ? 'light' : 'dark'" :size="20" />
        </div>
        <span class="bottom-text" v-show="!isCollapsed">{{ isDarkMode ? '日间模式' : '夜间模式' }}</span>
      </div>

      <div class="bottom-item" @click="logout" :title="isCollapsed ? '注销' : ''">
        <div class="bottom-icon">
          <SvgIcon name="logout" :size="20" />
        </div>
        <span class="bottom-text" v-show="!isCollapsed">注销</span>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'
import SvgIcon from './SvgIcon.vue'

export default {
  name: 'Sidebar',
  emits: ['navigate'],
  components: {
    SvgIcon
  },
  setup(props, { emit }) {
    // 从localStorage恢复状态
    const isCollapsed = ref(localStorage.getItem('sidebar-collapsed') === 'true')
    const activeItem = ref(localStorage.getItem('sidebar-active-item') || 'home')
    const isDarkMode = ref(localStorage.getItem('sidebar-dark-mode') !== 'false')

    const toggleCollapse = () => {
      isCollapsed.value = !isCollapsed.value
      // 保存状态到localStorage
      localStorage.setItem('sidebar-collapsed', isCollapsed.value.toString())
    }

    const setActive = (item) => {
      activeItem.value = item
      // 保存活跃项到localStorage
      localStorage.setItem('sidebar-active-item', item)

      // 映射导航项到页面
      const pageMap = {
        'home': 'home',
        'projects': 'home', // 暂时导航到首页
        'garden': 'create', // 妙笔生花导航到创作页面
        'library': 'home', // 暂时导航到首页
        'settings': 'settings'
      }

      emit('navigate', pageMap[item] || 'home')
    }

    const toggleTheme = () => {
      isDarkMode.value = !isDarkMode.value
      // 保存主题状态到localStorage
      localStorage.setItem('sidebar-dark-mode', isDarkMode.value.toString())
      // 这里可以添加主题切换逻辑
    }

    const toggleSettings = () => {
      setActive('settings')
    }

    const logout = () => {
      // 注销功能
      console.log('用户注销')
    }

    // 键盘快捷键支持
    const handleKeydown = (event) => {
      // Ctrl+B 或 Cmd+B 切换侧边栏
      if ((event.ctrlKey || event.metaKey) && event.key === 'b') {
        event.preventDefault()
        toggleCollapse()
      }
      // ESC 键收起侧边栏
      if (event.key === 'Escape' && !isCollapsed.value) {
        toggleCollapse()
      }
    }

    onMounted(() => {
      document.addEventListener('keydown', handleKeydown)
    })

    onUnmounted(() => {
      document.removeEventListener('keydown', handleKeydown)
    })

    return {
      isCollapsed,
      activeItem,
      isDarkMode,
      toggleCollapse,
      setActive,
      toggleTheme,
      toggleSettings,
      logout
    }
  }
}
</script>

<style scoped>
.sidebar {
  width: 252px;
  height: 100vh;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  position: relative;
  overflow: visible;
}

.sidebar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 240px;
  width: 12px;
  height: 100%;
  background: transparent;
  pointer-events: none;
}

/* 夜间模式样式 */
.sidebar--dark {
  background: rgba(45, 45, 45, 0.85);
  color: #ffffff;
  box-shadow: 2px 0 20px rgba(0, 0, 0, 0.3);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

/* 日间模式样式 */
.sidebar--light {
  background: rgba(255, 255, 255, 0.9);
  color: #333333;
  box-shadow: 2px 0 20px rgba(0, 0, 0, 0.1);
  border-right: 1px solid rgba(0, 0, 0, 0.1);
}

.sidebar--dark::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.02) 100%
  );
  pointer-events: none;
  z-index: 1;
}

.sidebar--light::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.05) 0%,
    rgba(0, 0, 0, 0.02) 50%,
    rgba(0, 0, 0, 0.01) 100%
  );
  pointer-events: none;
  z-index: 1;
}

.sidebar > * {
  position: relative;
  z-index: 2;
}

.sidebar--collapsed {
  width: 76px;
}

/* 用户信息区域 */
.sidebar__user {
  padding: 20px 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
}

.sidebar--dark .sidebar__user {
  border-bottom: 1px solid #404040;
}

.sidebar--light .sidebar__user {
  border-bottom: 1px solid #e0e0e0;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-circle {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.avatar-circle::after {
  content: '';
  width: 20px;
  height: 20px;
  background: white;
  border-radius: 50%;
  opacity: 0.8;
}

.user-info {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-role {
  font-size: 12px;
  opacity: 0.8;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.collapse-btn {
  position: absolute;
  top: 50%;
  right: -12px;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  border: none;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;
  overflow: hidden;
}

.collapse-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.sidebar--dark .collapse-btn::before {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.02) 100%
  );
}

.sidebar--light .collapse-btn::before {
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.05) 0%,
    rgba(0, 0, 0, 0.02) 50%,
    rgba(0, 0, 0, 0.01) 100%
  );
}

.collapse-btn > * {
  position: relative;
  z-index: 2;
}

.sidebar--dark .collapse-btn {
  background: rgba(45, 45, 45, 0.85);
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.2);
}

.sidebar--light .collapse-btn {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(0, 0, 0, 0.15);
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.1);
}

.sidebar--dark .collapse-btn:hover {
  background: rgba(45, 45, 45, 0.95);
  transform: translateY(-50%) scale(1.05);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.25);
}

.sidebar--light .collapse-btn:hover {
  background: rgba(255, 255, 255, 0.95);
  transform: translateY(-50%) scale(1.05);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  border-color: rgba(0, 0, 0, 0.25);
}

.collapse-icon {
  width: 12px;
  height: 9px;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  transition: all 0.3s ease;
}

.icon-bar {
  width: 100%;
  height: 1.5px;
  border-radius: 1px;
  transition: all 0.3s ease;
  transform-origin: center;
}

.sidebar--dark .icon-bar {
  background: rgba(255, 255, 255, 0.9);
}

.sidebar--light .icon-bar {
  background: rgba(0, 0, 0, 0.7);
}

.collapse-icon.collapsed .icon-bar:first-child {
  transform: rotate(45deg) translate(3px, 3px);
}

.collapse-icon.collapsed .icon-bar:last-child {
  transform: rotate(-45deg) translate(3px, -3px);
}

.sidebar--dark .collapse-btn:hover .icon-bar {
  background: rgba(255, 255, 255, 1);
}

.sidebar--light .collapse-btn:hover .icon-bar {
  background: rgba(0, 0, 0, 0.9);
}

/* 收缩状态下的图标条样式 */
.sidebar--collapsed.sidebar--dark .icon-bar {
  background: rgba(255, 255, 255, 0.9);
}

.sidebar--collapsed.sidebar--light .icon-bar {
  background: rgba(0, 0, 0, 0.7);
}

.sidebar--collapsed.sidebar--dark .collapse-btn:hover .icon-bar {
  background: rgba(255, 255, 255, 1);
}

.sidebar--collapsed.sidebar--light .collapse-btn:hover .icon-bar {
  background: rgba(0, 0, 0, 0.9);
}

/* 主导航 */
.sidebar__nav {
  flex: 1;
  padding: 20px 0;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  gap: 12px;
  margin: 2px 8px;
  border-radius: 8px;
  transform: translateX(0);
  will-change: transform, background, box-shadow;
}

.sidebar--dark .nav-item:hover {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  transform: translateX(4px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.sidebar--light .nav-item:hover {
  background: rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
  transform: translateX(4px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.nav-item--active {
  background: linear-gradient(135deg, rgba(74, 144, 226, 0.9) 0%, rgba(53, 122, 189, 0.8) 100%);
  backdrop-filter: blur(15px);
  transform: translateX(6px);
  box-shadow:
    0 4px 20px rgba(74, 144, 226, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(74, 144, 226, 0.5);
  position: relative;
  overflow: hidden;
}

.nav-item--active::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: shimmer 2s infinite;
}

.nav-item--active::after {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(180deg, #ffffff 0%, rgba(255, 255, 255, 0.8) 100%);
  border-radius: 0 2px 2px 0;
  box-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.nav-item--active::before {
  display: none;
}

.nav-icon {
  width: 24px;
  height: 24px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-icon svg {
  width: 20px;
  height: 20px;
  fill: currentColor;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-item:hover .nav-icon {
  transform: scale(1.1);
}

.nav-item--active .nav-icon {
  transform: scale(1.1);
}

.nav-item--active .nav-icon svg {
  filter: brightness(1.3) drop-shadow(0 0 4px rgba(255, 255, 255, 0.3));
}

.nav-item--active .nav-text {
  font-weight: 600;
  text-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
  color: #ffffff;
}

.nav-text {
  font-size: 13px;
  font-weight: 400;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 工具栏 */
.sidebar__tools {
  padding: 8px 0 16px 0;
}

.sidebar--dark .sidebar__tools {
  border-bottom: 1px solid #404040;
}

.sidebar--light .sidebar__tools {
  border-bottom: 1px solid #e0e0e0;
}

.tool-item {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  gap: 12px;
  margin: 2px 8px;
  border-radius: 8px;
}

.sidebar--dark .tool-item:hover {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(10px);
}

.sidebar--light .tool-item:hover {
  background: rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
}

.tool-item--active {
  background: rgba(74, 144, 226, 0.15) !important;
  color: #4a90e2;
}

.sidebar--dark .tool-item--active {
  background: rgba(74, 144, 226, 0.2) !important;
  color: #6bb6ff;
}

.tool-icon {
  width: 24px;
  height: 24px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tool-icon svg {
  width: 20px;
  height: 20px;
  fill: currentColor;
}

.tool-text {
  font-size: 13px;
  font-weight: 400;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 底部工具 */
.sidebar__bottom {
  padding: 16px 0;
}

.bottom-item {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  gap: 12px;
  margin: 2px 8px;
  border-radius: 8px;
}

.sidebar--dark .bottom-item:hover {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(10px);
}

.sidebar--light .bottom-item:hover {
  background: rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
}

.bottom-icon {
  width: 24px;
  height: 24px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bottom-icon svg {
  width: 20px;
  height: 20px;
  fill: currentColor;
}

.bottom-text {
  font-size: 13px;
  font-weight: 400;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 收起状态下的样式调整 */
.sidebar--collapsed .sidebar__user {
  padding: 20px 12px;
  justify-content: center;
}

.sidebar--collapsed .user-avatar {
  margin: 0 auto;
}

.sidebar--collapsed .sidebar__user {
  padding: 20px 12px;
  justify-content: center;
}

.sidebar--collapsed .user-avatar {
  margin: 0 auto;
}

.sidebar--collapsed .collapse-btn {
  right: -12px;
  top: 50%;
  transform: translateY(-50%);
}

.sidebar--collapsed.sidebar--dark .collapse-btn:hover {
  transform: translateY(-50%) scale(1.05);
}

.sidebar--collapsed.sidebar--light .collapse-btn:hover {
  transform: translateY(-50%) scale(1.05);
}

.sidebar--collapsed .nav-item,
.sidebar--collapsed .tool-item,
.sidebar--collapsed .bottom-item {
  padding: 10px 12px;
  justify-content: center;
  margin: 2px 4px;
}

.sidebar--collapsed .nav-item--active {
  background: linear-gradient(135deg, rgba(74, 144, 226, 0.9) 0%, rgba(53, 122, 189, 0.8) 100%);
  border-radius: 8px;
  transform: none;
  box-shadow:
    0 4px 15px rgba(74, 144, 226, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(74, 144, 226, 0.6);
  position: relative;
}

/* 响应式 */
@media (max-width: 1024px) {
  .sidebar {
    position: fixed;
    left: 0;
    top: 0;
    z-index: 1000;
    height: 100vh;
    box-shadow: 2px 0 20px rgba(0, 0, 0, 0.3);
  }
}

@media (max-width: 768px) {
  .sidebar {
    width: 280px;
    transform: translateX(-100%);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .sidebar.sidebar--open {
    transform: translateX(0);
  }

  .sidebar--collapsed {
    width: 280px;
    transform: translateX(-100%);
  }

  .sidebar--collapsed.sidebar--open {
    transform: translateX(0);
  }
}

@media (max-width: 480px) {
  .sidebar {
    width: 100vw;
  }

  .sidebar--collapsed {
    width: 100vw;
  }
}
</style>
