<template>
  <div :class="cardClasses" @click="handleClick">
    <!-- 卡片装饰边框 -->
    <div v-if="accent" class="card-accent" :class="`accent-${accent}`"></div>
    
    <!-- 卡片头部 -->
    <div v-if="$slots.header || title" class="card-header">
      <slot name="header">
        <div class="header-content">
          <h3 v-if="title" class="card-title">{{ title }}</h3>
          <p v-if="subtitle" class="card-subtitle">{{ subtitle }}</p>
        </div>
      </slot>
    </div>

    <!-- 卡片主体内容 -->
    <div class="card-body">
      <slot />
    </div>

    <!-- 卡片底部 -->
    <div v-if="$slots.footer" class="card-footer">
      <slot name="footer" />
    </div>

    <!-- 装饰性元素 -->
    <div v-if="decoration" class="card-decoration">
      <div class="decoration-pattern"></div>
    </div>

    <!-- 悬浮效果 -->
    <div v-if="hoverable" class="card-hover-effect"></div>
  </div>
</template>

<script>
import { computed } from 'vue'

export default {
  name: 'InkCard',
  props: {
    type: {
      type: String,
      default: 'default',
      validator: (value) => ['default', 'paper', 'scroll', 'elevated'].includes(value)
    },
    title: {
      type: String,
      default: ''
    },
    subtitle: {
      type: String,
      default: ''
    },
    accent: {
      type: String,
      default: '',
      validator: (value) => ['', 'gold', 'jade', 'vermillion', 'ink'].includes(value)
    },
    hoverable: {
      type: Boolean,
      default: true
    },
    clickable: {
      type: Boolean,
      default: false
    },
    decoration: {
      type: Boolean,
      default: false
    },
    shadow: {
      type: String,
      default: 'medium',
      validator: (value) => ['none', 'small', 'medium', 'large'].includes(value)
    }
  },
  emits: ['click'],
  setup(props, { emit }) {
    const cardClasses = computed(() => [
      'ink-card',
      `ink-card--${props.type}`,
      `ink-card--shadow-${props.shadow}`,
      {
        'is-hoverable': props.hoverable,
        'is-clickable': props.clickable
      }
    ])

    const handleClick = (event) => {
      if (props.clickable) {
        emit('click', event)
      }
    }

    return {
      cardClasses,
      handleClick
    }
  }
}
</script>

<style scoped>
.ink-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-xl);
  overflow: hidden;
  transition: all var(--duration-normal) var(--ease-paper);
  position: relative;
}

/* 阴影变体 */
.ink-card--shadow-none {
  box-shadow: none;
}

.ink-card--shadow-small {
  box-shadow: var(--shadow-paper-sm);
}

.ink-card--shadow-medium {
  box-shadow: var(--shadow-paper-md);
}

.ink-card--shadow-large {
  box-shadow: var(--shadow-paper-lg);
}

/* 类型变体 */
.ink-card--default {
  background: var(--bg-primary);
}

.ink-card--paper {
  background: var(--gradient-paper);
  position: relative;
}

.ink-card--paper::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(212, 175, 55, 0.02) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(74, 103, 65, 0.015) 0%, transparent 50%);
  pointer-events: none;
  border-radius: inherit;
}

.ink-card--scroll {
  background: var(--gradient-paper);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-2xl);
  position: relative;
}

.ink-card--scroll::before {
  content: '';
  position: absolute;
  top: 8px;
  left: 8px;
  right: 8px;
  bottom: 8px;
  border: 1px solid var(--border-hair);
  border-radius: var(--radius-xl);
  pointer-events: none;
}

.ink-card--scroll::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: var(--gradient-gold);
  opacity: 0.6;
}

.ink-card--elevated {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-paper-lg);
  position: relative;
}

.ink-card--elevated::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(212, 175, 55, 0.02) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(74, 103, 65, 0.015) 0%, transparent 50%);
  pointer-events: none;
  border-radius: inherit;
}

/* 交互状态 */
.ink-card.is-hoverable:hover {
  border-color: var(--border-medium);
  box-shadow: var(--shadow-paper-lg);
  transform: translateY(-2px);
}

.ink-card.is-clickable {
  cursor: pointer;
}

.ink-card.is-clickable:hover {
  border-color: var(--border-accent);
  box-shadow: var(--shadow-paper-xl);
  transform: translateY(-4px);
}

/* 装饰边框 */
.card-accent {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  z-index: 10;
}

.accent-gold {
  background: var(--gradient-gold);
}

.accent-jade {
  background: var(--gradient-jade);
}

.accent-vermillion {
  background: var(--zhu-sha);
}

.accent-ink {
  background: var(--gradient-ink);
}

/* 卡片头部 */
.card-header {
  padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-lg);
  border-bottom: 1px solid var(--border-light);
  background: var(--gradient-paper);
  position: relative;
  z-index: 5;
}

.header-content {
  position: relative;
}

.card-title {
  font-family: var(--font-calligraphy);
  font-size: 18px;
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xs) 0;
  letter-spacing: 0.02em;
}

.card-subtitle {
  font-family: var(--font-elegant);
  font-size: 13px;
  color: var(--text-muted);
  margin: 0;
  line-height: 1.4;
}

/* 卡片主体 */
.card-body {
  padding: var(--spacing-xl);
  position: relative;
  z-index: 5;
}

/* 卡片底部 */
.card-footer {
  padding: var(--spacing-lg) var(--spacing-xl) var(--spacing-xl);
  border-top: 1px solid var(--border-light);
  background: var(--bg-elevated);
  position: relative;
  z-index: 5;
}

/* 装饰性元素 */
.card-decoration {
  position: absolute;
  top: 0;
  right: 0;
  width: 80px;
  height: 80px;
  pointer-events: none;
  z-index: 1;
}

.decoration-pattern {
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle at 50% 50%, var(--huang-jin) 1px, transparent 1px),
    radial-gradient(circle at 25% 25%, var(--song-lv) 0.5px, transparent 0.5px);
  background-size: 8px 8px, 4px 4px;
  opacity: 0.05;
  border-radius: 0 var(--radius-xl) 0 var(--radius-xl);
}

/* 悬浮效果 */
.card-hover-effect {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(212, 175, 55, 0.02) 50%, transparent 100%);
  opacity: 0;
  transition: opacity var(--duration-normal) var(--ease-paper);
  pointer-events: none;
  border-radius: inherit;
}

.ink-card:hover .card-hover-effect {
  opacity: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .card-header {
    padding: var(--spacing-lg) var(--spacing-lg) var(--spacing-md);
  }
  
  .card-body {
    padding: var(--spacing-lg);
  }
  
  .card-footer {
    padding: var(--spacing-md) var(--spacing-lg) var(--spacing-lg);
  }
  
  .card-title {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .ink-card {
    border-radius: var(--radius-lg);
  }
  
  .card-header {
    padding: var(--spacing-md);
  }
  
  .card-body {
    padding: var(--spacing-md);
  }
  
  .card-footer {
    padding: var(--spacing-md);
  }
}
</style>
