<template>
  <div class="ai-config-section">
    <div class="section-header">
      <h2 class="section-title">AI服务配置</h2>
      <p class="section-subtitle">配置AI服务以启用智能创作功能</p>
    </div>

    <div class="config-form">
      <!-- AI服务商选择 -->
      <div class="form-group">
        <label class="form-label">AI服务商</label>
        <select v-model="localConfig.provider" @change="onProviderChange" class="form-select">
          <option value="">请选择AI服务提供商</option>
          <option v-for="(provider, key) in aiProviders" :key="key" :value="key">
            {{ provider.name }} - {{ provider.description }}
          </option>
        </select>
      </div>

      <!-- API Key配置 -->
      <div class="form-group" v-if="localConfig.provider">
        <label class="form-label">API Key</label>
        <div class="api-key-input">
          <input 
            v-model="localConfig.apiKey" 
            :type="showApiKey ? 'text' : 'password'"
            class="form-input"
            placeholder="请输入您的API Key"
          />
          <button 
            type="button" 
            class="toggle-btn"
            @click="showApiKey = !showApiKey"
          >
            <svg v-if="showApiKey" viewBox="0 0 24 24" fill="none">
              <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7z" stroke="currentColor" stroke-width="2"/>
              <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
            </svg>
            <svg v-else viewBox="0 0 24 24" fill="none">
              <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24" stroke="currentColor" stroke-width="2"/>
              <line x1="1" y1="1" x2="23" y2="23" stroke="currentColor" stroke-width="2"/>
            </svg>
          </button>
        </div>
        <div class="form-hint">
          <span>需要API Key？</span>
          <button type="button" class="help-link" @click="openApiKeyHelp">
            获取帮助
          </button>
        </div>
      </div>

      <!-- 模型选择 -->
      <div class="form-group" v-if="localConfig.provider && availableModels.length > 0">
        <label class="form-label">模型选择</label>
        <select v-model="localConfig.model" class="form-select">
          <option value="">请选择模型</option>
          <optgroup label="推荐模型">
            <option 
              v-for="model in recommendedModels" 
              :key="model.id" 
              :value="model.id"
            >
              {{ model.name }} - {{ model.description }}
            </option>
          </optgroup>
          <optgroup label="其他模型" v-if="otherModels.length > 0">
            <option 
              v-for="model in otherModels" 
              :key="model.id" 
              :value="model.id"
            >
              {{ model.name }}
            </option>
          </optgroup>
        </select>
      </div>

      <!-- 高级设置 -->
      <div class="advanced-settings" v-if="localConfig.provider">
        <div class="settings-header">
          <h3 class="settings-title">高级设置</h3>
          <button 
            type="button" 
            class="toggle-advanced"
            @click="showAdvanced = !showAdvanced"
          >
            <svg :class="{ 'rotated': showAdvanced }" viewBox="0 0 24 24" fill="none">
              <path d="M6 9l6 6 6-6" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            </svg>
          </button>
        </div>

        <div v-show="showAdvanced" class="advanced-content">
          <!-- 温度参数 -->
          <div class="form-group">
            <label class="form-label">
              温度参数 ({{ localConfig.temperature }})
            </label>
            <input 
              type="range" 
              v-model.number="localConfig.temperature"
              min="0" 
              max="2" 
              step="0.1"
              class="form-range"
            />
            <div class="form-hint">控制AI回复的创造性，0为最保守，2为最创新</div>
          </div>

          <!-- 超时设置 -->
          <div class="form-group">
            <label class="form-label">请求超时 (秒)</label>
            <input 
              type="number" 
              v-model.number="localConfig.timeout"
              min="5" 
              max="120" 
              step="5"
              class="form-input"
            />
          </div>

          <!-- 重试次数 -->
          <div class="form-group">
            <label class="form-label">最大重试次数</label>
            <input 
              type="number" 
              v-model.number="localConfig.maxRetries"
              min="0" 
              max="5"
              class="form-input"
            />
          </div>

          <!-- 流式输出 -->
          <div class="form-group">
            <label class="form-checkbox">
              <input type="checkbox" v-model="localConfig.stream" />
              <span class="checkbox-mark"></span>
              启用流式输出
            </label>
            <div class="form-hint">实时显示AI生成内容</div>
          </div>
        </div>
      </div>

      <!-- 连接状态 -->
      <div class="connection-status" v-if="localConfig.provider && localConfig.apiKey">
        <div class="status-indicator" :class="connectionStatus">
          <div class="status-dot"></div>
          <span class="status-text">{{ statusText }}</span>
        </div>
        <button 
          type="button" 
          class="test-btn"
          @click="testConnection"
          :disabled="testing"
        >
          {{ testing ? '测试中...' : '测试连接' }}
        </button>
      </div>

      <!-- 操作按钮 -->
      <div class="form-actions">
        <button type="button" class="save-btn" @click="saveConfig">
          保存配置
        </button>
        <button type="button" class="reset-btn" @click="resetConfig">
          重置设置
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'

export default {
  name: 'AIConfigSection',
  props: {
    modelValue: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:modelValue', 'save', 'test'],
  setup(props, { emit }) {
    const localConfig = ref({ ...props.modelValue })
    const showApiKey = ref(false)
    const showAdvanced = ref(false)
    const testing = ref(false)
    const connectionStatus = ref('disconnected') // connected, error, disconnected

    // AI服务提供商配置
    const aiProviders = {
      gemini: {
        name: 'Google Gemini',
        description: '强大的多模态AI',
        models: [
          { id: 'gemini-pro', name: 'Gemini Pro', description: '最新的高性能模型', recommended: true },
          { id: 'gemini-pro-vision', name: 'Gemini Pro Vision', description: '支持图像理解', recommended: true }
        ]
      },
      openai: {
        name: 'OpenAI',
        description: 'GPT系列模型',
        models: [
          { id: 'gpt-4', name: 'GPT-4', description: '最强大的语言模型', recommended: true },
          { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', description: '快速且经济', recommended: true },
          { id: 'gpt-4-turbo', name: 'GPT-4 Turbo', description: '更快的GPT-4' }
        ]
      },
      claude: {
        name: 'Anthropic Claude',
        description: '安全可靠的AI助手',
        models: [
          { id: 'claude-3-opus', name: 'Claude 3 Opus', description: '最强大的Claude模型', recommended: true },
          { id: 'claude-3-sonnet', name: 'Claude 3 Sonnet', description: '平衡性能与速度', recommended: true }
        ]
      }
    }

    // 可用模型
    const availableModels = computed(() => {
      if (!localConfig.value.provider) return []
      return aiProviders[localConfig.value.provider]?.models || []
    })

    // 推荐模型
    const recommendedModels = computed(() => {
      return availableModels.value.filter(model => model.recommended)
    })

    // 其他模型
    const otherModels = computed(() => {
      return availableModels.value.filter(model => !model.recommended)
    })

    // 状态文本
    const statusText = computed(() => {
      switch (connectionStatus.value) {
        case 'connected': return 'AI服务已连接'
        case 'error': return 'AI服务连接失败'
        default: return '未连接'
      }
    })

    // 服务商变更
    const onProviderChange = () => {
      localConfig.value.model = ''
      localConfig.value.apiKey = ''
      connectionStatus.value = 'disconnected'
    }

    // 测试连接
    const testConnection = async () => {
      testing.value = true
      connectionStatus.value = 'disconnected'
      
      try {
        emit('test', localConfig.value)
        // 模拟测试
        await new Promise(resolve => setTimeout(resolve, 2000))
        connectionStatus.value = 'connected'
      } catch (error) {
        connectionStatus.value = 'error'
      } finally {
        testing.value = false
      }
    }

    // 保存配置
    const saveConfig = () => {
      emit('update:modelValue', localConfig.value)
      emit('save')
    }

    // 重置配置
    const resetConfig = () => {
      localConfig.value = {
        provider: '',
        apiKey: '',
        model: '',
        temperature: 0.7,
        timeout: 30,
        maxRetries: 3,
        stream: true
      }
      connectionStatus.value = 'disconnected'
    }

    // 打开API Key帮助
    const openApiKeyHelp = () => {
      const urls = {
        gemini: 'https://aistudio.google.com/app/apikey',
        openai: 'https://platform.openai.com/api-keys',
        claude: 'https://console.anthropic.com/'
      }
      
      const url = urls[localConfig.value.provider]
      if (url) {
        window.open(url, '_blank')
      }
    }

    // 监听props变化
    watch(() => props.modelValue, (newValue) => {
      localConfig.value = { ...newValue }
    }, { deep: true })

    return {
      localConfig,
      showApiKey,
      showAdvanced,
      testing,
      connectionStatus,
      aiProviders,
      availableModels,
      recommendedModels,
      otherModels,
      statusText,
      onProviderChange,
      testConnection,
      saveConfig,
      resetConfig,
      openApiKeyHelp
    }
  }
}
</script>

<style scoped>
.ai-config-section {
  max-width: 600px;
}

.section-header {
  margin-bottom: 32px;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: inherit;
}

.section-subtitle {
  font-size: 14px;
  opacity: 0.7;
  margin: 0;
}

.config-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  font-size: 14px;
  font-weight: 500;
  color: inherit;
}

.form-select,
.form-input {
  padding: 12px 16px;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  font-size: 14px;
  transition: all 0.2s ease;
}

.form-select:focus,
.form-input:focus {
  outline: none;
  border-color: #4a90e2;
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.api-key-input {
  position: relative;
  display: flex;
  align-items: center;
}

.api-key-input .form-input {
  flex: 1;
  padding-right: 48px;
}

.toggle-btn {
  position: absolute;
  right: 12px;
  width: 24px;
  height: 24px;
  border: none;
  background: none;
  cursor: pointer;
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.toggle-btn:hover {
  opacity: 1;
}

.toggle-btn svg {
  width: 16px;
  height: 16px;
}

.form-hint {
  font-size: 12px;
  opacity: 0.6;
  display: flex;
  align-items: center;
  gap: 8px;
}

.help-link {
  background: none;
  border: none;
  color: #4a90e2;
  cursor: pointer;
  text-decoration: underline;
  font-size: 12px;
}

.help-link:hover {
  color: #357abd;
}

.advanced-settings {
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  overflow: hidden;
  background: rgba(0, 0, 0, 0.02);
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.settings-title {
  font-size: 16px;
  font-weight: 500;
  margin: 0;
}

.toggle-advanced {
  width: 24px;
  height: 24px;
  border: none;
  background: none;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.toggle-advanced svg {
  width: 16px;
  height: 16px;
}

.toggle-advanced svg.rotated {
  transform: rotate(180deg);
}

.advanced-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-range {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: rgba(0, 0, 0, 0.1);
  outline: none;
  -webkit-appearance: none;
}

.form-range::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #4a90e2;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(74, 144, 226, 0.3);
}

.form-checkbox {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  font-size: 14px;
}

.form-checkbox input[type="checkbox"] {
  display: none;
}

.checkbox-mark {
  width: 18px;
  height: 18px;
  border: 2px solid rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  position: relative;
  transition: all 0.2s ease;
}

.form-checkbox input[type="checkbox"]:checked + .checkbox-mark {
  background: #4a90e2;
  border-color: #4a90e2;
}

.form-checkbox input[type="checkbox"]:checked + .checkbox-mark::after {
  content: '';
  position: absolute;
  left: 5px;
  top: 2px;
  width: 4px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.connection-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ccc;
}

.status-indicator.connected .status-dot {
  background: #4caf50;
}

.status-indicator.error .status-dot {
  background: #f44336;
}

.status-text {
  font-size: 14px;
  font-weight: 500;
}

.test-btn {
  padding: 8px 16px;
  border: 1px solid #4a90e2;
  border-radius: 6px;
  background: transparent;
  color: #4a90e2;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.test-btn:hover:not(:disabled) {
  background: #4a90e2;
  color: white;
}

.test-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.form-actions {
  display: flex;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.save-btn,
.reset-btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.save-btn {
  background: #4a90e2;
  color: white;
  border: none;
}

.save-btn:hover {
  background: #357abd;
  transform: translateY(-1px);
}

.reset-btn {
  background: transparent;
  color: #666;
  border: 1px solid rgba(0, 0, 0, 0.2);
}

.reset-btn:hover {
  background: rgba(0, 0, 0, 0.05);
}
</style>
