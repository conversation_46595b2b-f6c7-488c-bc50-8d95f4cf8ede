// AI创作服务
import { testAIConnection } from '@/utils/aiTester.js'
import { MemoryDatabase } from './memoryDatabase.js'
import { ConsistencyChecker } from './consistencyChecker.js'

/**
 * AI创作服务类
 */
export class AICreationService {
  constructor() {
    this.config = null
    this.loadConfig()
    this.memoryDatabase = null // 记忆数据库实例
    this.currentProject = null // 当前项目信息
    this.consistencyChecker = null // 一致性检查器
  }

  // 加载AI配置
  loadConfig() {
    try {
      const savedConfig = localStorage.getItem('aiSettings')
      if (savedConfig) {
        this.config = JSON.parse(savedConfig)
      }
    } catch (error) {
      console.error('加载AI配置失败:', error)
    }
  }

  // 检查配置是否有效
  isConfigured() {
    // 每次检查时重新加载配置，确保获取最新状态
    this.loadConfig()

    const isValid = this.config &&
           this.config.provider &&
           this.config.apiKey &&
           this.config.model

    console.log('AI配置检查:', {
      config: this.config,
      isValid,
      provider: this.config?.provider,
      hasApiKey: !!this.config?.apiKey,
      model: this.config?.model
    })

    return isValid
  }

  // 获取流式输出设置
  getStreamSetting() {
    try {
      const advancedSettings = localStorage.getItem('advancedSettings')
      if (advancedSettings) {
        const settings = JSON.parse(advancedSettings)
        return settings.stream || false
      }
    } catch (error) {
      console.error('获取流式设置失败:', error)
    }
    return false
  }

  // 调用AI API的通用方法（带重试机制）
  async callAI(prompt, options = {}) {
    const maxRetries = 3
    let lastError = null

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await this._callAIInternal(prompt, options)
      } catch (error) {
        lastError = error
        console.warn(`AI调用失败 (尝试 ${attempt}/${maxRetries}):`, error.message)

        // 如果是503错误（服务过载），等待后重试
        if (error.message.includes('503') || error.message.includes('overloaded')) {
          if (attempt < maxRetries) {
            const delay = attempt * 5000 // 5秒、10秒、15秒
            console.log(`等待 ${delay/1000} 秒后重试...`)
            await new Promise(resolve => setTimeout(resolve, delay))
            continue
          }
        }

        // 其他错误直接抛出
        if (attempt === maxRetries) {
          throw lastError
        }
      }
    }

    throw lastError
  }

  // 内部AI调用方法
  async _callAIInternal(prompt, options = {}) {
    if (!this.isConfigured()) {
      throw new Error('AI服务未配置，请先在设置中配置AI服务')
    }

    const { provider, apiKey, apiUrl, model } = this.config
    const { temperature = 0.8, stream = false, onProgress, maxTokens } = options

    try {
      switch (provider) {
        case 'gemini':
          if (stream && onProgress) {
            return await this.callGeminiStream(prompt, { temperature, onProgress, maxTokens })
          }
          return await this.callGemini(prompt, { temperature, maxTokens })
        case 'openai':
          if (stream && onProgress) {
            return await this.callOpenAIStream(prompt, { temperature, onProgress, maxTokens })
          }
          return await this.callOpenAI(prompt, { temperature, maxTokens })
        case 'claude':
          if (stream && onProgress) {
            return await this.callClaudeStream(prompt, { temperature, onProgress, maxTokens })
          }
          return await this.callClaude(prompt, { temperature, maxTokens })
        default:
          throw new Error(`不支持的AI服务商: ${provider}`)
      }
    } catch (error) {
      console.error('AI调用失败:', error)
      throw new Error(`AI服务调用失败: ${error.message}`)
    }
  }

  // 调用Gemini API
  async callGemini(prompt, options) {
    const { apiKey, apiUrl, model } = this.config
    const baseUrl = apiUrl || 'https://generativelanguage.googleapis.com/v1beta'
    
    const url = `${baseUrl}/models/${model}:generateContent?key=${apiKey}`
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: prompt
          }]
        }],
        generationConfig: {
          // 使用动态计算的maxOutputTokens，确保输出完整但不超限
          ...(options.maxTokens && { maxOutputTokens: options.maxTokens }),
          temperature: options.temperature,
          topP: 0.8,
          topK: 40
        }
      })
    })

    if (!response.ok) {
      const error = await response.text()
      throw new Error(`Gemini API错误: ${response.status} - ${error}`)
    }

    const data = await response.json()
    const result = data.candidates?.[0]?.content?.parts?.[0]?.text || ''

    console.log('Gemini API详细响应:', {
      requestTokens: options.maxTokens,
      responseLength: result.length,
      finishReason: data.candidates?.[0]?.finishReason,
      safetyRatings: data.candidates?.[0]?.safetyRatings,
      promptFeedback: data.promptFeedback,
      usageMetadata: data.usageMetadata,
      fullResponse: data
    })

    // 检查输出完整性并提供建议
    const finishReason = data.candidates?.[0]?.finishReason
    if (finishReason === 'MAX_TOKENS') {
      console.warn('⚠️ Gemini输出被截断 - 达到最大token限制')
      console.log('💡 建议：尝试减少prompt长度或增加maxTokens设置')

      // 对于某些类型的生成，允许截断
      const allowTruncation = options.allowTruncation || false
      if (!allowTruncation) {
        throw new Error('输出被截断：内容超出token限制，请尝试简化需求或分批生成')
      } else {
        console.log('🔄 允许截断模式：继续处理部分内容')
      }
    }
    if (finishReason === 'SAFETY') {
      console.warn('⚠️ Gemini输出被安全过滤器阻止')
      throw new Error('内容被安全过滤器阻止，请调整创作要求')
    }

    return result
  }

  // 调用Gemini流式API
  async callGeminiStream(prompt, options) {
    const { apiKey, apiUrl, model } = this.config
    const baseUrl = apiUrl || 'https://generativelanguage.googleapis.com/v1beta'

    // Gemini的流式API使用不同的端点
    const url = `${baseUrl}/models/${model}:streamGenerateContent?alt=sse&key=${apiKey}`

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: prompt
          }]
        }],
        generationConfig: {
          // 使用动态计算的maxOutputTokens，确保输出完整但不超限
          ...(options.maxTokens && { maxOutputTokens: options.maxTokens }),
          temperature: options.temperature,
          topP: 0.8,
          topK: 40
        }
      })
    })

    if (!response.ok) {
      const error = await response.text()
      throw new Error(`Gemini API错误: ${response.status} - ${error}`)
    }

    let fullText = ''
    const reader = response.body.getReader()
    const decoder = new TextDecoder()

    try {
      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value, { stream: true })
        const lines = chunk.split('\n').filter(line => line.trim())

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const jsonStr = line.slice(6).trim()
              if (jsonStr === '[DONE]') break

              const data = JSON.parse(jsonStr)
              const text = data.candidates?.[0]?.content?.parts?.[0]?.text || ''
              if (text) {
                fullText += text
                if (options.onProgress) {
                  options.onProgress(text) // 传递增量文本，而不是全部文本
                }
              }
            } catch (e) {
              console.warn('解析Gemini流式响应失败:', e, line)
            }
          } else if (line.trim() && !line.startsWith('event:')) {
            // 有些情况下可能直接是JSON，没有data:前缀
            try {
              const data = JSON.parse(line)
              const text = data.candidates?.[0]?.content?.parts?.[0]?.text || ''
              if (text) {
                fullText += text
                if (options.onProgress) {
                  options.onProgress(text) // 传递增量文本，而不是全部文本
                }
              }
            } catch (e) {
              // 忽略解析错误
            }
          }
        }
      }
    } finally {
      reader.releaseLock()
    }

    return fullText
  }

  // 调用OpenAI API
  async callOpenAI(prompt, options) {
    const { apiKey, apiUrl, model } = this.config
    const baseUrl = apiUrl || 'https://api.openai.com/v1'
    
    const url = `${baseUrl}/chat/completions`
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model,
        messages: [{
          role: 'user',
          content: prompt
        }],
        max_tokens: options.maxTokens,
        temperature: options.temperature
      })
    })

    if (!response.ok) {
      const error = await response.text()
      throw new Error(`OpenAI API错误: ${response.status} - ${error}`)
    }

    const data = await response.json()
    return data.choices?.[0]?.message?.content || ''
  }

  // 调用OpenAI流式API
  async callOpenAIStream(prompt, options) {
    const { apiKey, apiUrl, model } = this.config
    const baseUrl = apiUrl || 'https://api.openai.com/v1'

    const url = `${baseUrl}/chat/completions`

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model,
        messages: [{
          role: 'user',
          content: prompt
        }],
        max_tokens: options.maxTokens,
        temperature: options.temperature,
        stream: true
      })
    })

    if (!response.ok) {
      const error = await response.text()
      throw new Error(`OpenAI API错误: ${response.status} - ${error}`)
    }

    let fullText = ''
    const reader = response.body.getReader()
    const decoder = new TextDecoder()

    try {
      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value, { stream: true })
        const lines = chunk.split('\n').filter(line => line.trim())

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6)
            if (data === '[DONE]') break

            try {
              const parsed = JSON.parse(data)
              const content = parsed.choices?.[0]?.delta?.content || ''
              if (content) {
                fullText += content
                if (options.onProgress) {
                  options.onProgress(fullText)
                }
              }
            } catch (e) {
              // 忽略解析错误
            }
          }
        }
      }
    } finally {
      reader.releaseLock()
    }

    return fullText
  }

  // 调用Claude API
  async callClaude(prompt, options) {
    const { apiKey, apiUrl, model } = this.config
    const baseUrl = apiUrl || 'https://api.anthropic.com/v1'
    
    const url = `${baseUrl}/messages`
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': apiKey,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify({
        model,
        max_tokens: options.maxTokens,
        messages: [{
          role: 'user',
          content: prompt
        }]
      })
    })

    if (!response.ok) {
      const error = await response.text()
      throw new Error(`Claude API错误: ${response.status} - ${error}`)
    }

    const data = await response.json()
    return data.content?.[0]?.text || ''
  }

  // 调用Claude流式API
  async callClaudeStream(prompt, options) {
    const { apiKey, apiUrl, model } = this.config
    const baseUrl = apiUrl || 'https://api.anthropic.com/v1'

    const url = `${baseUrl}/messages`

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': apiKey,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify({
        model,
        max_tokens: options.maxTokens,
        messages: [{
          role: 'user',
          content: prompt
        }],
        stream: true
      })
    })

    if (!response.ok) {
      const error = await response.text()
      throw new Error(`Claude API错误: ${response.status} - ${error}`)
    }

    let fullText = ''
    const reader = response.body.getReader()
    const decoder = new TextDecoder()

    try {
      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value, { stream: true })
        const lines = chunk.split('\n').filter(line => line.trim())

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6)
            if (data === '[DONE]') break

            try {
              const parsed = JSON.parse(data)
              if (parsed.type === 'content_block_delta') {
                const content = parsed.delta?.text || ''
                if (content) {
                  fullText += content
                  if (options.onProgress) {
                    options.onProgress(fullText)
                  }
                }
              }
            } catch (e) {
              // 忽略解析错误
            }
          }
        }
      }
    } finally {
      reader.releaseLock()
    }

    return fullText
  }

  // 初始化新项目的记忆系统
  initializeProject(params) {
    this.memoryDatabase = new MemoryDatabase()
    this.consistencyChecker = new ConsistencyChecker(this.memoryDatabase)
    this.currentProject = {
      title: params.title,
      genre: params.genre,
      category: params.category,
      requirements: params.requirements,
      creativity: params.creativity, // 用户创意
      chapterCount: params.chapterCount,
      wordsPerChapter: params.wordsPerChapter,
      createdAt: new Date().toISOString()
    }

    console.log('🎯 项目记忆系统和一致性检查器初始化完成:', this.currentProject.title)
  }

  // 生成故事大纲 - 统一使用分层规划策略
  async generateOutline(params, onProgress = null) {
    // 初始化记忆系统
    this.initializeProject(params)

    // 生成分层规划
    const outline = await this.generateLayeredOutline(params, onProgress)

    // 将结果存入记忆库
    if (this.memoryDatabase) {
      this.memoryDatabase.initializeFromPreparation({
        outline,
        characters: [], // 稍后会生成
        creativity: params.creativity,
        metadata: this.currentProject
      })
    }

    return outline
  }

  // 分层规划生成大纲（适用于大型网文）
  async generateLayeredOutline(params, onProgress = null) {
    const { title, genre, category, requirements, chapterCount, wordsPerChapter = 3000 } = params

    try {
      // 第一层：生成总体大纲和分卷规划
      if (onProgress) onProgress('正在生成总体大纲和分卷规划...')

      const volumeCount = Math.ceil(chapterCount / 100) // 每卷约100章
      const volumePrompt = `为${genre}小说《${title}》制定分层规划（${chapterCount}章，${volumeCount}卷）：

## 基本信息
题材：${genre} | 读者：${category} | 单章：${wordsPerChapter}字
要求：${requirements}

## 输出格式

### 1. 核心设定
**背景**：[简述世界观和时代背景]
**主角**：[姓名、身份、目标]
**女主**：[姓名、身份、与主角关系]
**反派**：[姓名、身份、动机]
**主线**：[核心冲突和故事走向]

### 2. 分卷规划
${Array.from({length: volumeCount}, (_, i) => {
  const startChapter = i * 100 + 1
  const endChapter = Math.min((i + 1) * 100, chapterCount)
  return `**第${i + 1}卷**（第${startChapter}-${endChapter}章）：[卷名] - [主要剧情]`
}).join('\n')}

### 3. 关键节点
**开篇**：第1-10章 - [设定介绍]
**转折**：第${Math.floor(chapterCount * 0.3)}章左右 - [重要转折]
**高潮**：第${Math.floor(chapterCount * 0.8)}章左右 - [故事高潮]
**结局**：第${chapterCount}章 - [结局安排]

要求：简洁明确，重点突出，为后续详细规划提供框架。`

      let layeredOutline = ''

      // 根据章节数量动态调整token限制
      const dynamicTokens = Math.min(8000, Math.max(4000, chapterCount * 8))
      console.log(`🎯 分层规划使用token限制: ${dynamicTokens} (章节数: ${chapterCount})`)

      await this.callAI(volumePrompt, {
        temperature: 0.7,
        stream: true,
        maxTokens: dynamicTokens,
        onProgress: (chunk) => {
          layeredOutline += chunk
          if (onProgress) {
            onProgress(layeredOutline)
          }
        }
      })

      return layeredOutline

    } catch (error) {
      console.error('分层大纲生成失败:', error)

      // 如果是token限制问题，尝试简化版本
      if (error.message.includes('token限制')) {
        console.log('🔄 尝试生成简化版分层规划...')

        const simplePrompt = `为${genre}小说《${title}》生成简化规划（${chapterCount}章）：

## 核心设定
- 背景：[简述]
- 主角：[姓名、目标]
- 主线：[核心冲突]

## 分卷安排（${volumeCount}卷）
${Array.from({length: Math.min(volumeCount, 5)}, (_, i) =>
  `第${i + 1}卷：[卷名] - [主要内容]`
).join('\n')}

## 重要节点
- 开篇：第1-10章
- 转折：第${Math.floor(chapterCount * 0.3)}章
- 高潮：第${Math.floor(chapterCount * 0.8)}章
- 结局：第${chapterCount}章`

        try {
          let simpleOutline = ''
          await this.callAI(simplePrompt, {
            temperature: 0.7,
            stream: true,
            maxTokens: 2000, // 更保守的token限制
            onProgress: (chunk) => {
              simpleOutline += chunk
              if (onProgress) {
                onProgress(simpleOutline)
              }
            }
          })

          return simpleOutline + '\n\n[注：由于内容较多，已生成简化版规划。可后续按需生成详细内容。]'

        } catch (retryError) {
          console.error('简化版生成也失败:', retryError)
          throw new Error(`分层规划生成失败: ${error.message}`)
        }
      }

      throw new Error(`分层大纲生成失败: ${error.message}`)
    }
  }

  // 生成指定卷的详细章节概要
  async generateVolumeChapters(params, volumeNumber, startChapter, endChapter, onProgress = null) {
    const { title, genre, category, requirements, wordsPerChapter = 3000 } = params

    try {
      if (onProgress) onProgress(`正在生成第${volumeNumber}卷详细章节概要...`)

      const chapterCount = endChapter - startChapter + 1
      const volumePrompt = `为${genre}小说《${title}》第${volumeNumber}卷生成详细章节概要：

## 基本信息
- 章节范围：第${startChapter}章 - 第${endChapter}章（共${chapterCount}章）
- 单章字数：${wordsPerChapter}字
- 题材：${genre}
- 读者群体：${category}
- 特殊要求：${requirements}

## 请为每一章生成以下内容：

### 第X章：[章节标题]
- **主要情节**：本章的核心剧情发展
- **关键事件**：重要的情节转折或事件
- **角色发展**：主要角色在本章的变化和成长
- **情感线**：感情戏份的推进
- **悬念设置**：为下一章埋下的伏笔
- **字数分配**：约${wordsPerChapter}字

要求：
1. 每章概要详细具体，包含足够的细节供后续写作参考
2. 章节间要有连贯性和逻辑性
3. 适当设置悬念和转折点
4. 符合${genre}小说的特点和节奏`

      let volumeChapters = ''
      await this.callAI(volumePrompt, {
        temperature: 0.7,
        stream: true,
        maxTokens: Math.min(8000, chapterCount * 200), // 每章约200token的概要
        onProgress: (chunk) => {
          volumeChapters += chunk
          if (onProgress) {
            onProgress(volumeChapters)
          }
        }
      })

      return volumeChapters

    } catch (error) {
      console.error('卷章节概要生成失败:', error)
      throw new Error(`第${volumeNumber}卷章节概要生成失败: ${error.message}`)
    }
  }

  // 生成单章内容（带记忆系统）
  async generateChapterWithMemory(chapterNumber, chapterTitle, onProgress = null) {
    if (!this.memoryDatabase) {
      throw new Error('记忆系统未初始化，请先生成大纲')
    }

    try {
      console.log(`📝 开始生成第${chapterNumber}章，使用记忆系统`)

      // 获取相关记忆
      const memory = this.memoryDatabase.getRelevantMemory(chapterNumber, chapterTitle)
      console.log('📚 获取到的记忆数据:', memory)

      // 验证记忆数据结构
      if (!memory || !memory.core) {
        console.error('❌ 记忆数据结构异常:', memory)
        throw new Error('记忆系统返回的数据结构不完整')
      }

      // 构建包含记忆的prompt
      const memoryPrompt = this.buildChapterPromptWithMemory(chapterNumber, chapterTitle, memory)

      // 生成章节内容
      let chapterContent = ''
      await this.callAI(memoryPrompt, {
        temperature: 0.7,
        stream: true,
        maxTokens: this.currentProject.wordsPerChapter * 2, // 预留足够空间
        onProgress: (chunk) => {
          chapterContent += chunk
          if (onProgress) {
            onProgress(chapterContent)
          }
        }
      })

      // 更新记忆库
      this.updateMemoryAfterChapter(chapterNumber, chapterTitle, chapterContent)

      // 执行一致性检查
      let consistencyIssues = []
      if (this.consistencyChecker) {
        try {
          consistencyIssues = await this.consistencyChecker.performFullCheck(chapterNumber, chapterContent)
        } catch (error) {
          console.error('一致性检查失败:', error)
        }
      }

      console.log(`✅ 第${chapterNumber}章生成完成，长度: ${chapterContent.length}字，发现${consistencyIssues.length}个一致性问题`)

      return {
        content: chapterContent,
        consistencyIssues: consistencyIssues
      }

    } catch (error) {
      console.error(`第${chapterNumber}章生成失败:`, error)
      throw new Error(`第${chapterNumber}章生成失败: ${error.message}`)
    }
  }

  // 构建包含记忆的章节prompt
  buildChapterPromptWithMemory(chapterNumber, chapterTitle, memory) {
    // 安全获取记忆数据，处理新的记忆结构
    const coreMemory = memory.core || {}
    const dynamicMemory = memory.dynamic || {}
    const contextualMemory = memory.contextual || {}

    return `请为${this.currentProject.genre}小说《${this.currentProject.title}》写第${chapterNumber}章：

## 必须遵守的记忆信息（不可忽略）

### 核心创意设定
${coreMemory.creativity || '待设定'}

### 主角信息
姓名：${coreMemory.mainCharacter?.name || '待设定'}
角色：${coreMemory.mainCharacter?.role || '主角'}
特点：${coreMemory.mainCharacter?.traits?.join('、') || '待设定'}

### 世界观规则
${coreMemory.worldRules?.join('、') || '待设定'}

### 当前剧情阶段
${coreMemory.currentPlotStage || '开始阶段'}

### 前一章节详细回顾（重要：确保连贯性）
${this.getPreviousChapterDetail(chapterNumber)}

### 相关角色状态
${dynamicMemory.activeCharacters && dynamicMemory.activeCharacters.length > 0
  ? dynamicMemory.activeCharacters.map(char =>
      `${char.name}（${char.role}）：${char.status}，最后出现在第${char.lastAppeared}章`
    ).join('\n')
  : '暂无相关角色信息'}

### 未解决的伏笔
${dynamicMemory.pendingForeshadowing && dynamicMemory.pendingForeshadowing.length > 0
  ? dynamicMemory.pendingForeshadowing.map(f =>
      `第${f.chapter}章埋下：${f.content}`
    ).join('\n')
  : '暂无'}

## 第${chapterNumber}章创作要求

### 章节标题
${chapterTitle}

### 创作指导（重点：确保连贯性）
1. **连贯性第一**：必须与前一章自然衔接，角色状态、场景位置要保持一致
2. **承上启下**：开头要呼应前章结尾，结尾要为下章做铺垫
3. **角色一致性**：角色的性格、能力、状态必须与之前保持一致
4. **情节推进**：在前章基础上合理推进，不要突兀跳跃
5. **字数控制**：${this.currentProject.wordsPerChapter}字左右
6. **核心创意**：体现"${coreMemory.creativity || '待设定'}"
7. **文体风格**：保持${this.currentProject.genre}小说的特点
8. **节奏把控**：情节有起伏，对话生动自然

## 第${chapterNumber}章：${chapterTitle}

请基于以上所有信息，特别注意与前章的连贯性，直接输出章节内容：`
  }

  // 章节生成后更新记忆库
  updateMemoryAfterChapter(chapterNumber, chapterTitle, content) {
    if (!this.memoryDatabase) return

    // 添加章节摘要（传递完整内容用于深度分析）
    const summary = this.generateChapterSummary(content)
    this.memoryDatabase.addChapterSummary(chapterNumber, summary, content)

    // 更新角色出现记录
    this.updateCharacterAppearances(chapterNumber, content)

    // 检测新的伏笔
    this.detectNewForeshadowing(chapterNumber, content)

    console.log(`📚 第${chapterNumber}章记忆已更新`)
  }

  // 生成章节摘要（增强版，提取关键信息）
  generateChapterSummary(content) {
    // 提取关键信息
    const summary = {
      brief: content.substring(0, 200) + '...',
      ending: this.extractChapterEnding(content),
      mainEvents: this.extractMainEvents(content),
      characterStates: this.extractCharacterStates(content),
      sceneLocation: this.extractSceneLocation(content)
    }

    return `${summary.brief}

关键信息：
- 章节结尾：${summary.ending}
- 主要事件：${summary.mainEvents}
- 角色状态：${summary.characterStates}
- 场景位置：${summary.sceneLocation}`
  }

  // 提取章节结尾（用于下章衔接）
  extractChapterEnding(content) {
    const sentences = content.split(/[。！？]/)
    const lastSentences = sentences.slice(-3, -1).join('。')
    return lastSentences || '章节结尾信息不明确'
  }

  // 提取主要事件
  extractMainEvents(content) {
    // 简单的事件提取，寻找动作词
    const events = []
    if (content.includes('战斗') || content.includes('打斗')) events.push('战斗')
    if (content.includes('对话') || content.includes('说道')) events.push('对话')
    if (content.includes('修炼') || content.includes('练功')) events.push('修炼')
    if (content.includes('发现') || content.includes('找到')) events.push('发现')
    return events.length > 0 ? events.join('、') : '日常情节'
  }

  // 提取角色状态
  extractCharacterStates(content) {
    const states = []
    for (const [name] of this.memoryDatabase.characters) {
      if (content.includes(name)) {
        // 简单的状态判断
        if (content.includes(`${name}受伤`)) states.push(`${name}受伤`)
        else if (content.includes(`${name}愤怒`)) states.push(`${name}愤怒`)
        else if (content.includes(`${name}高兴`)) states.push(`${name}高兴`)
        else states.push(`${name}正常`)
      }
    }
    return states.length > 0 ? states.join('、') : '状态正常'
  }

  // 提取场景位置
  extractSceneLocation(content) {
    const locations = ['宗门', '山洞', '城市', '森林', '宫殿', '客栈', '家中']
    for (const location of locations) {
      if (content.includes(location)) {
        return location
      }
    }
    return '位置不明确'
  }

  // 更新角色出现记录
  updateCharacterAppearances(chapterNumber, content) {
    for (const [name] of this.memoryDatabase.characters) {
      if (content.includes(name)) {
        this.memoryDatabase.recordCharacterAppearance(name, chapterNumber)
      }
    }
  }

  // 检测新的伏笔
  detectNewForeshadowing(chapterNumber, content) {
    // 简单的伏笔检测，寻找关键词
    const foreshadowKeywords = ['神秘', '秘密', '预言', '传说', '将来', '未来']

    for (const keyword of foreshadowKeywords) {
      if (content.includes(keyword)) {
        const context = this.extractContext(content, keyword)
        this.memoryDatabase.addForeshadowing(
          chapterNumber,
          context,
          `第${chapterNumber + 50}章左右解决`
        )
        break // 每章最多添加一个伏笔
      }
    }
  }

  // 获取前一章的详细信息以确保连贯性（增强版）
  getPreviousChapterDetail(currentChapter) {
    if (currentChapter <= 1) {
      return '这是开篇章节，请设置好故事背景和主角初始状态'
    }

    const previousChapter = currentChapter - 1
    const chapterSummary = this.memoryDatabase.chapterSummaries[previousChapter - 1]

    if (!chapterSummary) {
      return `第${previousChapter}章信息缺失，请确保故事连贯性`
    }

    // 构建超详细的前章回顾
    let detail = `第${previousChapter}章结束时的详细状态：

## 基本信息
- 主要情节：${chapterSummary.summary}
- 关键事件：${chapterSummary.keyEvents?.join('、') || '无'}

## 章节结尾状态（重要：必须自然承接）`

    // 添加详细的结尾信息
    if (chapterSummary.chapterEnding) {
      detail += `
- 结尾描述：${chapterSummary.chapterEnding.lastSentences}
- 结尾情绪：${chapterSummary.chapterEnding.endingMood}
- 是否有悬念：${chapterSummary.chapterEnding.cliffhanger ? '是' : '否'}
- 下章提示：${chapterSummary.chapterEnding.nextChapterHint || '无'}`
    }

    // 添加环境信息
    detail += `

## 环境状态
- 场景位置：${chapterSummary.sceneLocation || '位置不明确'}
- 时间：${chapterSummary.timeOfDay || '时间不明确'}
- 天气氛围：${chapterSummary.weatherMood || '天气不明确'}
- 整体情感基调：${chapterSummary.emotionalTone || 'neutral'}`

    // 添加角色状态
    if (chapterSummary.characterStates && Object.keys(chapterSummary.characterStates).length > 0) {
      detail += `

## 角色状态（必须保持连续）`
      for (const [name, state] of Object.entries(chapterSummary.characterStates)) {
        detail += `
- ${name}：
  * 情绪：${state.mood}
  * 行动：${state.action}
  * 位置：${state.location}
  * 伤势：${state.injury}
  * 实力：${state.power}`
      }
    }

    // 添加对话风格
    if (chapterSummary.characterDialogueStyle && Object.keys(chapterSummary.characterDialogueStyle).length > 0) {
      detail += `

## 角色对话风格（保持一致）`
      for (const [name, style] of Object.entries(chapterSummary.characterDialogueStyle)) {
        detail += `
- ${name}：${style}风格`
      }
    }

    // 添加情节线索
    if (chapterSummary.plotThreads && chapterSummary.plotThreads.length > 0) {
      detail += `

## 进行中的情节线索`
      chapterSummary.plotThreads.forEach(thread => {
        detail += `
- ${thread.type}：${thread.description}`
      })
    }

    // 添加冲突状态
    if (chapterSummary.conflictStatus && chapterSummary.conflictStatus.type !== 'none') {
      detail += `

## 冲突状态
- 类型：${chapterSummary.conflictStatus.type}
- 强度：${chapterSummary.conflictStatus.intensity}
- 是否解决：${chapterSummary.conflictStatus.resolved ? '是' : '否'}`
    }

    detail += `

## 第${currentChapter}章连贯性要求
1. 开头必须自然承接上述结尾状态
2. 角色的情绪、位置、伤势要保持连续
3. 场景转换要有合理说明
4. 对话风格要与角色设定一致
5. 情节发展要在前章基础上推进
6. 时间流逝要合理自然`

    return detail
  }

  // 提取关键词上下文
  extractContext(content, keyword) {
    const index = content.indexOf(keyword)
    if (index === -1) return keyword

    const start = Math.max(0, index - 50)
    const end = Math.min(content.length, index + 50)
    return content.substring(start, end)
  }

  // 动态计算合适的token数量
  calculateOptimalTokens(targetWords, contentType = 'chapter') {
    // 中文字符与token的大致比例：1个中文字符 ≈ 1.5-2个token
    // 为了确保输出完整，我们使用较宽松的比例
    const chineseCharToTokenRatio = 2.5

    // 根据内容类型调整基础token数
    const baseTokens = {
      'outline': Math.max(500, targetWords * chineseCharToTokenRatio * 0.3), // 大纲相对简洁
      'chapter': Math.max(800, targetWords * chineseCharToTokenRatio), // 章节内容详细
      'character': Math.max(600, targetWords * chineseCharToTokenRatio * 0.8), // 角色描述中等
      'ending': Math.max(400, targetWords * chineseCharToTokenRatio * 0.5) // 结局简洁有力
    }

    const calculatedTokens = baseTokens[contentType] || baseTokens['chapter']

    // 设置合理的上下限
    const minTokens = 300
    const maxTokens = 4096 // Gemini的单次输出限制

    const finalTokens = Math.min(Math.max(calculatedTokens, minTokens), maxTokens)

    console.log(`📊 Token计算 - 目标字数: ${targetWords}, 类型: ${contentType}, 计算tokens: ${finalTokens}`)

    return Math.round(finalTokens)
  }

  // 检查输出是否完整
  checkOutputCompleteness(text, expectedLength, contentType) {
    const actualLength = text.length
    const completenessRatio = actualLength / expectedLength

    // 不同内容类型的完整性阈值
    const thresholds = {
      'outline': 0.6,   // 大纲允许较短
      'chapter': 0.7,   // 章节需要相对完整
      'character': 0.65, // 角色描述中等要求
      'ending': 0.6     // 结局允许简洁
    }

    const threshold = thresholds[contentType] || 0.7
    const isComplete = completenessRatio >= threshold

    console.log(`🔍 完整性检查 - 类型: ${contentType}, 实际长度: ${actualLength}, 期望长度: ${expectedLength}, 完整度: ${(completenessRatio * 100).toFixed(1)}%, 阈值: ${(threshold * 100)}%, 完整: ${isComplete}`)

    return {
      isComplete,
      actualLength,
      expectedLength,
      completenessRatio,
      suggestion: isComplete ? '输出完整' : '输出可能被截断，建议增加token限制'
    }
  }

  // 传统的完整生成方法已删除，统一使用分层规划



  // 生成角色设定 - 简化版本
  async generateCharacter(params, onProgress = null) {
    const { name, role, genre, background } = params

    try {
      // 简化的一次性角色生成
      const characterPrompt = `为${genre}小说创建${role}角色：

## 角色信息
- 姓名：${name}
- 角色：${role}
- 背景：${background}

## 请生成完整角色设定：

### 基本信息
- 年龄、身份、职业
- 性格特点（主要特征）
- 外貌特征（简洁描述）

### 背景故事
- 出身经历
- 核心动机
- 重要关系

### 能力特长
- 主要技能
- 特殊能力（如适用）

要求：内容完整但简洁，符合${genre}风格。`

      if (onProgress) onProgress('正在生成角色设定...')
      const characterInfo = await this.callAI(characterPrompt, {
        maxTokens: 4000,
        temperature: 0.8,
        allowTruncation: true
      })

      // 简化版本：直接返回角色信息
      if (onProgress) onProgress(characterInfo)
      return characterInfo

    } catch (error) {
      console.error('角色设定生成失败:', error)
      throw error
    }
  }

  // 生成章节内容 - 分段策略
  async generateChapter(params, onProgress = null) {
    const { chapterTitle, outline, previousContent, requirements } = params

    try {
      // 第一部分：章节开头
      const beginningPrompt = `请创作章节《${chapterTitle}》的开头部分：

章节大纲：${outline}
${previousContent ? `前文内容：${previousContent.slice(-300)}` : ''}
创作要求：${requirements}

请创作章节开头（约800-1000字），要求：
1. 引人入胜的开头
2. 承接前文（如果有）
3. 设定场景和氛围
4. 介绍主要人物和情况

请直接输出开头内容，文风生动有趣。`

      if (onProgress) onProgress('正在创作章节开头...')
      const beginning = await this.callAI(beginningPrompt, { maxTokens: 2000, temperature: 0.7 })

      // 第二部分：章节发展
      const developmentPrompt = `基于章节《${chapterTitle}》的开头，请继续创作发展部分：

章节开头：
${beginning}

章节大纲：${outline}
创作要求：${requirements}

请创作章节发展部分（约1000-1500字），要求：
1. 推进主要情节
2. 人物对话生动自然
3. 场景描写细腻
4. 心理描写深入

请直接输出发展内容，与开头自然衔接。`

      if (onProgress) onProgress('正在创作章节发展...')
      const development = await this.callAI(developmentPrompt, { maxTokens: 2500, temperature: 0.7 })

      // 第三部分：章节结尾
      const endingPrompt = `基于章节《${chapterTitle}》的前半部分，请创作结尾：

章节前半部分：
${beginning}
${development}

章节大纲：${outline}
创作要求：${requirements}

请创作章节结尾（约800-1000字），要求：
1. 完成本章主要情节
2. 设置悬念或转折
3. 为下章做铺垫
4. 情感高潮或冲突解决

请直接输出结尾内容，确保整章完整。`

      if (onProgress) onProgress('正在创作章节结尾...')
      const ending = await this.callAI(endingPrompt, { maxTokens: 2000, temperature: 0.7 })

      // 合并所有部分
      const fullChapter = `${beginning}

${development}

${ending}`

      if (onProgress) onProgress(fullChapter)
      return fullChapter

    } catch (error) {
      console.error('分段生成章节失败:', error)
      // 如果分段失败，回退到简化版本
      const simplePrompt = `请创作章节《${chapterTitle}》：

章节大纲：${outline}
创作要求：${requirements}

请创作一个完整的章节（2000-3000字），包含开头、发展、结尾。
要求情节紧凑，对话生动，描写细腻。

请直接输出章节内容。`

      return await this.callAI(simplePrompt, { maxTokens: 4000, temperature: 0.7 })
    }
  }

  // 续写内容
  async continueWriting(params) {
    const { existingContent, requirements, length = 1000 } = params
    
    const prompt = `请根据以下已有内容进行续写：

已有内容：
${existingContent.slice(-1000)}

续写要求：${requirements}
续写长度：约${length}字

请保持文风一致，情节自然衔接，继续推进故事发展。`

    return await this.callAI(prompt, { maxTokens: length * 1.5, temperature: 0.7 })
  }

  // 优化文本
  async optimizeText(params) {
    const { text, optimizeType = 'general' } = params
    
    const optimizePrompts = {
      general: '请优化以下文本，使其更加生动、流畅、富有感染力',
      dialogue: '请优化以下对话，使其更加自然、生动、符合人物性格',
      description: '请优化以下描写，使其更加细腻、形象、富有画面感',
      plot: '请优化以下情节，使其更加紧凑、合理、引人入胜'
    }
    
    const prompt = `${optimizePrompts[optimizeType]}：

原文：
${text}

请保持原意的基础上进行优化，直接输出优化后的内容。`

    return await this.callAI(prompt, { maxTokens: text.length * 1.5, temperature: 0.6 })
  }

  // 测试API连接和输出长度
  async testAPIOutput() {
    const testPrompt = `请生成一个详细的测试故事大纲，包含以下内容：

## 测试小说大纲

### 1. 故事背景
请详细描述一个现代都市背景的故事设定，包括时间、地点、社会环境等。

### 2. 主要角色
- 主角：详细描述主角的姓名、年龄、职业、性格特点、背景故事
- 女主角：详细描述女主角的基本信息和特点
- 反派：详细描述反派角色的动机和能力
- 配角：列出3-5个重要配角及其作用

### 3. 故事情节
请为这个故事设计10个章节的详细情节：

第1章：（章节标题）- （详细情节描述，至少200字）
第2章：（章节标题）- （详细情节描述，至少200字）
第3章：（章节标题）- （详细情节描述，至少200字）
第4章：（章节标题）- （详细情节描述，至少200字）
第5章：（章节标题）- （详细情节描述，至少200字）
第6章：（章节标题）- （详细情节描述，至少200字）
第7章：（章节标题）- （详细情节描述，至少200字）
第8章：（章节标题）- （详细情节描述，至少200字）
第9章：（章节标题）- （详细情节描述，至少200字）
第10章：（章节标题）- （详细情节描述，至少200字）

### 4. 故事结局
详细描述故事的高潮和结局。

要求：
- 每个部分都要详细完整
- 总字数应该在3000字以上
- 不要省略任何章节
- 确保内容完整，不要中途截断

请按照上述要求生成完整的测试大纲。`

    console.log('🧪 开始API输出测试...')
    const startTime = Date.now()

    try {
      const result = await this.callAI(testPrompt, { maxTokens: 8000, temperature: 0.7 })
      const endTime = Date.now()

      console.log('🧪 API测试结果:', {
        耗时: `${endTime - startTime}ms`,
        输出长度: result.length,
        字符数: result.replace(/\s/g, '').length,
        是否包含第10章: result.includes('第10章'),
        是否包含结局: result.includes('故事结局') || result.includes('高潮'),
        输出预览: result.substring(0, 200) + '...'
      })

      return result
    } catch (error) {
      console.error('🧪 API测试失败:', error)
      throw error
    }
  }
}

// 导出单例实例
export const aiCreationService = new AICreationService()
