<template>
  <div class="chapter-review-panel">
    <div class="review-header">
      <h3>📝 第{{ chapterNumber }}章深度审核</h3>
      <div class="review-stats">
        <el-tag :type="qualityScore >= 80 ? 'success' : qualityScore >= 60 ? 'warning' : 'danger'">
          质量评分: {{ qualityScore }}/100
        </el-tag>
        <el-tag type="info">字数: {{ chapterContent.length }}</el-tag>
        <el-tag type="info">审核时间: {{ reviewTime }}分钟</el-tag>
      </div>
    </div>

    <el-tabs v-model="activeTab" class="review-tabs">
      <!-- 内容审核 -->
      <el-tab-pane label="内容审核" name="content">
        <div class="content-review">
          <div class="content-display">
            <h4>章节内容（支持划词分析与修改）</h4>
            <TextSelectionAnalyzer
              :chapter-content="editableContent"
              :chapter-number="chapterNumber"
              @content-updated="handleContentUpdate"
              @modification-applied="handleModificationApplied"
            />
          </div>

          <div class="content-editor" v-if="showRawEditor">
            <h4>传统文本编辑器</h4>
            <p class="editor-description">
              <el-icon><InfoFilled /></el-icon>
              用于大段修改、复制粘贴或当划词功能不可用时使用
            </p>
            <el-input
              v-model="editableContent"
              type="textarea"
              :rows="15"
              placeholder="章节内容..."
              class="content-textarea"
            />
          </div>

          <div class="content-actions">
            <el-button
              @click="showRawEditor = !showRawEditor"
              type="info"
              size="small"
            >
              {{ showRawEditor ? '隐藏' : '显示' }}原始编辑器
            </el-button>
            <el-button @click="resetContent">恢复原文</el-button>
            <el-button type="primary" @click="saveContentChanges">保存修改</el-button>
          </div>
        </div>
      </el-tab-pane>

      <!-- 一致性检查 -->
      <el-tab-pane label="一致性检查" name="consistency">
        <div class="consistency-check">
          <div class="check-section">
            <h4>🎭 角色一致性</h4>
            <div v-for="issue in characterIssues" :key="issue.id" class="issue-item">
              <div class="issue-header">
                <el-icon class="issue-icon" :class="issue.severity">
                  <Warning v-if="issue.severity === 'high'" />
                  <InfoFilled v-else />
                </el-icon>
                <span class="issue-title">{{ issue.title }}</span>
                <el-tag :type="issue.severity === 'high' ? 'danger' : 'warning'" size="small">
                  {{ issue.severity === 'high' ? '严重' : '轻微' }}
                </el-tag>
              </div>
              <p class="issue-description">{{ issue.description }}</p>
              <div class="issue-actions">
                <el-button size="small" @click="fixIssue(issue)">修复</el-button>
                <el-button size="small" @click="ignoreIssue(issue)">忽略</el-button>
              </div>
            </div>
          </div>

          <div class="check-section">
            <h4>📖 情节一致性</h4>
            <div v-for="issue in plotIssues" :key="issue.id" class="issue-item">
              <div class="issue-header">
                <el-icon class="issue-icon" :class="issue.severity">
                  <Warning v-if="issue.severity === 'high'" />
                  <InfoFilled v-else />
                </el-icon>
                <span class="issue-title">{{ issue.title }}</span>
                <el-tag :type="issue.severity === 'high' ? 'danger' : 'warning'" size="small">
                  {{ issue.severity === 'high' ? '严重' : '轻微' }}
                </el-tag>
              </div>
              <p class="issue-description">{{ issue.description }}</p>
              <div class="issue-actions">
                <el-button size="small" @click="fixIssue(issue)">修复</el-button>
                <el-button size="small" @click="ignoreIssue(issue)">忽略</el-button>
              </div>
            </div>
          </div>

          <div class="check-section">
            <h4>💡 创意符合度</h4>
            <div class="creativity-check">
              <div class="creativity-core">
                <strong>核心创意:</strong> {{ coreCreativity }}
              </div>
              <div class="creativity-implementation">
                <el-rate
                  v-model="creativityScore"
                  :max="5"
                  show-text
                  :texts="['很差', '较差', '一般', '良好', '优秀']"
                />
                <p>本章对核心创意的体现程度</p>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 记忆库状态 -->
      <el-tab-pane label="记忆库状态" name="memory">
        <div class="memory-status">
          <div class="memory-section">
            <h4>🎭 活跃角色</h4>
            <div class="character-grid">
              <div v-for="character in activeCharacters" :key="character.name" class="character-card">
                <div class="character-header">
                  <strong>{{ character.name }}</strong>
                  <el-tag size="small">{{ character.role }}</el-tag>
                </div>
                <div class="character-info">
                  <p><strong>状态:</strong> {{ character.status }}</p>
                  <p><strong>最后出现:</strong> 第{{ character.lastAppeared }}章</p>
                  <p><strong>特征:</strong> {{ character.traits?.join('、') || '无' }}</p>
                </div>
                <div class="character-actions">
                  <el-button size="small" @click="editCharacter(character)">编辑</el-button>
                </div>
              </div>
            </div>
          </div>

          <div class="memory-section">
            <h4>📚 近期章节</h4>
            <div class="recent-chapters">
              <div v-for="chapter in recentChapters" :key="chapter.chapter" class="chapter-summary">
                <div class="chapter-header">
                  <strong>第{{ chapter.chapter }}章</strong>
                  <span class="chapter-date">{{ formatDate(chapter.createdAt) }}</span>
                </div>
                <p class="chapter-content">{{ chapter.summary }}</p>
              </div>
            </div>
          </div>

          <div class="memory-section">
            <h4>🔮 未解决伏笔</h4>
            <div class="foreshadowing-list">
              <div v-for="foreshadow in pendingForeshadowing" :key="foreshadow.id" class="foreshadow-item">
                <div class="foreshadow-header">
                  <strong>第{{ foreshadow.chapter }}章</strong>
                  <el-tag size="small" type="warning">未解决</el-tag>
                </div>
                <p class="foreshadow-content">{{ foreshadow.content }}</p>
                <p class="foreshadow-plan">计划解决: {{ foreshadow.plannedResolution }}</p>
                <div class="foreshadow-actions">
                  <el-button size="small" @click="resolveForeshadowing(foreshadow)">标记为已解决</el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 质量评估 -->
      <el-tab-pane label="质量评估" name="quality">
        <div class="quality-assessment">
          <div class="assessment-form">
            <h4>📊 章节质量评估</h4>
            
            <div class="assessment-item">
              <label>情节发展 (1-10分)</label>
              <el-rate v-model="qualityRatings.plot" :max="10" show-score />
            </div>
            
            <div class="assessment-item">
              <label>角色塑造 (1-10分)</label>
              <el-rate v-model="qualityRatings.character" :max="10" show-score />
            </div>
            
            <div class="assessment-item">
              <label>文笔质量 (1-10分)</label>
              <el-rate v-model="qualityRatings.writing" :max="10" show-score />
            </div>
            
            <div class="assessment-item">
              <label>创意体现 (1-10分)</label>
              <el-rate v-model="qualityRatings.creativity" :max="10" show-score />
            </div>
            
            <div class="assessment-item">
              <label>整体满意度 (1-10分)</label>
              <el-rate v-model="qualityRatings.overall" :max="10" show-score />
            </div>

            <div class="assessment-notes">
              <label>审核备注</label>
              <el-input
                v-model="reviewNotes"
                type="textarea"
                :rows="4"
                placeholder="请记录您的审核意见和建议..."
              />
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 审核操作 -->
    <div class="review-actions">
      <div class="action-group">
        <el-button size="large" @click="rejectChapter">
          <el-icon><Close /></el-icon>
          拒绝此章节
        </el-button>
        
        <el-button size="large" @click="requestRevision">
          <el-icon><Refresh /></el-icon>
          要求修改
        </el-button>
        
        <el-button size="large" type="success" @click="approveChapter">
          <el-icon><Check /></el-icon>
          通过审核
        </el-button>
      </div>
      
      <div class="time-tracker">
        <span>审核用时: {{ reviewTime }}分钟</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Warning, InfoFilled, Close, Refresh, Check } from '@element-plus/icons-vue'
import TextSelectionAnalyzer from './TextSelectionAnalyzer.vue'

// Props
const props = defineProps({
  chapterNumber: {
    type: Number,
    required: true
  },
  chapterContent: {
    type: String,
    required: true
  },
  memoryData: {
    type: Object,
    required: true
  },
  coreCreativity: {
    type: String,
    required: true
  },
  consistencyIssues: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['approve', 'reject', 'revise', 'update-content'])

// 响应式数据
const activeTab = ref('content')
const editableContent = ref('')
const reviewStartTime = ref(Date.now())
const reviewTime = ref(0)
const showRawEditor = ref(false)
const modificationHistory = ref([])

// 质量评分
const qualityRatings = ref({
  plot: 7,
  character: 7,
  writing: 7,
  creativity: 7,
  overall: 7
})

const creativityScore = ref(3)
const reviewNotes = ref('')

// 一致性检查问题（从props获取真实数据）
const characterIssues = computed(() => {
  return props.consistencyIssues.filter(issue =>
    issue.type.startsWith('character')
  )
})

const plotIssues = computed(() => {
  return props.consistencyIssues.filter(issue =>
    issue.type === 'plot_progression' ||
    issue.type === 'foreshadowing' ||
    issue.type === 'creativity_implementation'
  )
})

const worldIssues = computed(() => {
  return props.consistencyIssues.filter(issue =>
    issue.type === 'world_consistency' ||
    issue.type === 'timeline'
  )
})

// 计算属性
const qualityScore = computed(() => {
  const ratings = qualityRatings.value
  const average = (ratings.plot + ratings.character + ratings.writing + ratings.creativity + ratings.overall) / 5
  let score = Math.round(average * 10)

  // 根据一致性问题调整评分
  const issues = props.consistencyIssues
  const highIssues = issues.filter(issue => issue.severity === 'high').length
  const mediumIssues = issues.filter(issue => issue.severity === 'medium').length
  const lowIssues = issues.filter(issue => issue.severity === 'low').length

  // 扣分：高严重性-10分，中等-5分，低-2分
  score -= (highIssues * 10 + mediumIssues * 5 + lowIssues * 2)

  return Math.max(0, Math.min(100, score))
})

const activeCharacters = computed(() => {
  return props.memoryData?.characters ? Object.values(props.memoryData.characters) : []
})

const recentChapters = computed(() => {
  return props.memoryData?.chapterSummaries?.slice(-5) || []
})

const pendingForeshadowing = computed(() => {
  return props.memoryData?.foreshadowing?.filter(f => !f.resolved) || []
})

// 方法
const resetContent = () => {
  editableContent.value = props.chapterContent
}

const saveContentChanges = () => {
  emit('update-content', editableContent.value)
  ElMessage.success('内容修改已保存')
}

// 处理文本选择分析器的内容更新
const handleContentUpdate = (newContent) => {
  editableContent.value = newContent
  emit('update-content', newContent)
}

// 处理修改记录
const handleModificationApplied = (modification) => {
  modificationHistory.value.push(modification)
  console.log('修改记录:', modification)

  // 可以在这里添加修改历史的UI显示
  ElMessage.success(`已应用修改：${modification.original.substring(0, 20)}...`)
}

const fixIssue = (issue) => {
  ElMessage.info('修复功能开发中...')
}

const ignoreIssue = (issue) => {
  ElMessage.info('已忽略此问题')
}

const editCharacter = (character) => {
  ElMessage.info('角色编辑功能开发中...')
}

const resolveForeshadowing = (foreshadow) => {
  ElMessage.success('伏笔已标记为解决')
}

const rejectChapter = () => {
  ElMessageBox.confirm('确定要拒绝这一章吗？', '确认拒绝', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    emit('reject', {
      reason: reviewNotes.value,
      ratings: qualityRatings.value
    })
  })
}

const requestRevision = () => {
  emit('revise', {
    suggestions: reviewNotes.value,
    issues: [...characterIssues.value, ...plotIssues.value]
  })
}

const approveChapter = () => {
  emit('approve', {
    ratings: qualityRatings.value,
    notes: reviewNotes.value,
    reviewTime: reviewTime.value
  })
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleString()
}

// 更新审核时间
const updateReviewTime = () => {
  reviewTime.value = Math.round((Date.now() - reviewStartTime.value) / 60000)
}

// 生命周期
onMounted(() => {
  editableContent.value = props.chapterContent

  // 每分钟更新一次审核时间
  setInterval(updateReviewTime, 60000)
})

// 监听内容变化
watch(() => props.chapterContent, (newContent) => {
  editableContent.value = newContent
})
</script>

<style scoped>
.chapter-review-panel {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e9ecef;
}

.review-stats {
  display: flex;
  gap: 10px;
}

.review-tabs {
  margin-bottom: 20px;
}

.content-editor {
  margin: 15px 0;
}

.content-textarea {
  font-family: 'Microsoft YaHei', sans-serif;
  line-height: 1.8;
}

.content-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.check-section {
  margin-bottom: 25px;
  padding: 15px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.issue-item {
  margin-bottom: 15px;
  padding: 12px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  background: #f8f9fa;
}

.issue-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.issue-icon.high {
  color: #f56565;
}

.issue-icon.medium {
  color: #ed8936;
}

.issue-actions {
  display: flex;
  gap: 8px;
  margin-top: 10px;
}

.creativity-check {
  padding: 15px;
  background: white;
  border-radius: 8px;
}

.creativity-core {
  margin-bottom: 15px;
  padding: 10px;
  background: #e3f2fd;
  border-radius: 6px;
}

.character-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 15px;
}

.character-card {
  padding: 15px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.character-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.recent-chapters {
  max-height: 300px;
  overflow-y: auto;
}

.chapter-summary {
  margin-bottom: 15px;
  padding: 12px;
  background: white;
  border-radius: 6px;
  border-left: 4px solid #007bff;
}

.chapter-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.assessment-item {
  margin-bottom: 20px;
}

.assessment-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
}

.assessment-notes {
  margin-top: 20px;
}

.review-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.action-group {
  display: flex;
  gap: 15px;
}

.time-tracker {
  color: #666;
  font-size: 14px;
}

.editor-description {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  padding: 8px 12px;
  background: #f0f9ff;
  border-radius: 6px;
  color: #1e40af;
  font-size: 14px;
  border: 1px solid #dbeafe;
}
</style>
