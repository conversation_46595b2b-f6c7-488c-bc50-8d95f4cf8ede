<template>
  <div class="ink-editor">
    <!-- 编辑器标题栏 -->
    <div class="editor-header glass-card">
      <div class="header-left">
        <h1 class="editor-title gradient-text">挥毫泼墨</h1>
        <span class="editor-subtitle">智能编辑器</span>
      </div>

      <div class="header-actions">
        <button class="action-btn neumorphism-btn" @click="saveDocument">
          <el-icon><DocumentCopy /></el-icon>
          保存
        </button>

        <button class="action-btn neumorphism-btn" @click="exportDocument">
          <el-icon><Download /></el-icon>
          导出
        </button>
      </div>
    </div>

    <!-- 编辑器主体 -->
    <div class="editor-body">
      <!-- 左侧工具栏 -->
      <div class="editor-sidebar">
        <div class="tool-section">
          <h3 class="tool-title">AI助手</h3>
          <div class="tool-buttons">
            <button class="tool-btn" @click="aiContinue">
              <el-icon><EditPen /></el-icon>
              续写
            </button>
            
            <button class="tool-btn" @click="aiOptimize">
              <el-icon><StarFilled /></el-icon>
              优化
            </button>
          </div>
        </div>
        
        <div class="tool-section">
          <h3 class="tool-title">文档结构</h3>
          <div class="chapter-list">
            <div 
              v-for="(chapter, index) in chapters" 
              :key="index"
              class="chapter-item"
              :class="{ active: currentChapter === index }"
              @click="switchChapter(index)"
            >
              <span class="chapter-number">第{{ index + 1 }}章</span>
              <span class="chapter-title">{{ chapter.title || '未命名章节' }}</span>
            </div>
            
            <button class="add-chapter-btn" @click="addChapter">
              <el-icon><CirclePlus /></el-icon>
              新增章节
            </button>
          </div>
        </div>
      </div>

      <!-- 主编辑区域 -->
      <div class="editor-main">
        <div class="editor-container">
          <!-- 章节标题 -->
          <div class="chapter-header">
            <el-input 
              v-model="currentChapterData.title"
              placeholder="请输入章节标题"
              class="chapter-title-input"
              size="large"
            />
          </div>
          
          <!-- 文本编辑器 -->
          <div class="text-editor">
            <el-input
              v-model="currentChapterData.content"
              type="textarea"
              placeholder="在此处开始您的创作..."
              :rows="20"
              class="content-textarea"
              resize="none"
            />
          </div>
          
          <!-- 编辑器状态栏 -->
          <div class="editor-status">
            <div class="status-left">
              <span class="word-count">字数: {{ wordCount }}</span>
              <span class="char-count">字符: {{ charCount }}</span>
            </div>
            
            <div class="status-right">
              <span class="save-status">{{ saveStatus }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'

export default {
  name: 'EditorSimple',
  setup() {
    // 响应式数据
    const currentChapter = ref(0)
    const saveStatus = ref('已保存')
    
    // 章节数据
    const chapters = ref([
      { title: '第一章 开篇', content: '这里是第一章的内容...' },
      { title: '第二章 发展', content: '这里是第二章的内容...' }
    ])

    // 当前章节数据
    const currentChapterData = computed(() => {
      return chapters.value[currentChapter.value] || { title: '', content: '' }
    })

    // 字数统计
    const wordCount = computed(() => {
      return currentChapterData.value.content.length
    })

    const charCount = computed(() => {
      return currentChapterData.value.content.replace(/\s/g, '').length
    })

    // 切换章节
    const switchChapter = (index) => {
      currentChapter.value = index
    }

    // 添加章节
    const addChapter = () => {
      const newChapter = {
        title: `第${chapters.value.length + 1}章`,
        content: ''
      }
      chapters.value.push(newChapter)
      currentChapter.value = chapters.value.length - 1
      
      ElMessage({
        message: '新章节已添加',
        type: 'success'
      })
    }

    // AI功能
    const aiContinue = () => {
      ElMessage({
        message: 'AI续写功能开发中...',
        type: 'info'
      })
    }

    const aiOptimize = () => {
      ElMessage({
        message: 'AI优化功能开发中...',
        type: 'info'
      })
    }

    // 文档操作
    const saveDocument = () => {
      saveStatus.value = '保存中...'
      setTimeout(() => {
        saveStatus.value = '已保存'
        ElMessage({
          message: '文档保存成功',
          type: 'success'
        })
      }, 1000)
    }

    const exportDocument = () => {
      ElMessage({
        message: '导出功能开发中...',
        type: 'info'
      })
    }

    return {
      currentChapter,
      saveStatus,
      chapters,
      currentChapterData,
      wordCount,
      charCount,
      switchChapter,
      addChapter,
      aiContinue,
      aiOptimize,
      saveDocument,
      exportDocument
    }
  }
}
</script>

<style scoped>
/* 引入水墨书香主题 */
@import '@/assets/styles/modern-theme.css';

.ink-editor {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--bg-secondary);
}

/* 编辑器标题栏 */
.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg) var(--spacing-xl);
  background: var(--gradient-paper);
  border-bottom: 2px solid var(--border-light);
  box-shadow: var(--shadow-paper-sm);
}

.header-left {
  display: flex;
  align-items: baseline;
  gap: var(--spacing-md);
}

.editor-title {
  font-family: var(--font-calligraphy);
  font-size: 24px;
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0;
}

.editor-subtitle {
  font-family: var(--font-elegant);
  font-size: 14px;
  color: var(--text-muted);
}

.header-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.action-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  color: var(--text-muted);
  font-family: var(--font-elegant);
  font-size: 13px;
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-paper);
}

.action-btn:hover {
  border-color: var(--huang-jin);
  color: var(--huang-jin);
}

/* 编辑器主体 */
.editor-body {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* 左侧工具栏 */
.editor-sidebar {
  width: 280px;
  background: var(--bg-primary);
  border-right: 2px solid var(--border-light);
  padding: var(--spacing-lg);
  overflow-y: auto;
}

.tool-section {
  margin-bottom: var(--spacing-xl);
}

.tool-title {
  font-family: var(--font-calligraphy);
  font-size: 16px;
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-md) 0;
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border-light);
}

.tool-buttons {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.tool-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  color: var(--text-secondary);
  font-family: var(--font-elegant);
  font-size: 14px;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-paper);
  text-align: left;
  width: 100%;
}

.tool-btn:hover {
  background: var(--huang-jin);
  color: var(--ink-jiao);
  transform: translateX(4px);
}

.chapter-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.chapter-item {
  display: flex;
  flex-direction: column;
  padding: var(--spacing-md);
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-paper);
}

.chapter-item:hover {
  border-color: var(--huang-jin);
  transform: translateX(2px);
}

.chapter-item.active {
  background: var(--huang-jin);
  color: var(--ink-jiao);
  border-color: var(--huang-jin);
}

.chapter-number {
  font-family: var(--font-elegant);
  font-size: 12px;
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-xs);
}

.chapter-title {
  font-family: var(--font-calligraphy);
  font-size: 14px;
  font-weight: var(--font-weight-medium);
}

.add-chapter-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  background: transparent;
  border: 2px dashed var(--border-medium);
  border-radius: var(--radius-lg);
  color: var(--text-muted);
  font-family: var(--font-elegant);
  font-size: 14px;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-paper);
  margin-top: var(--spacing-md);
}

.add-chapter-btn:hover {
  border-color: var(--huang-jin);
  color: var(--huang-jin);
}

/* 主编辑区域 */
.editor-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.editor-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: var(--spacing-xl);
  background: var(--bg-primary);
}

.chapter-header {
  margin-bottom: var(--spacing-lg);
}

.text-editor {
  flex: 1;
  margin-bottom: var(--spacing-lg);
}

.editor-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  font-family: var(--font-elegant);
  font-size: 13px;
  color: var(--text-muted);
}

.status-left {
  display: flex;
  gap: var(--spacing-lg);
}

.word-count,
.char-count {
  font-weight: var(--font-weight-medium);
}

.save-status {
  font-weight: var(--font-weight-medium);
  color: var(--song-lv);
}
</style>
