<template>
  <div class="demo-content">
    <div class="demo-header">
      <h1>Electron + Vue3 侧边栏演示</h1>
      <p>这是一个完全按照设计图实现的侧边栏组件</p>
    </div>

    <div class="demo-features">
      <div class="feature-card">
        <h3>🎨 设计特点</h3>
        <ul>
          <li>深色主题背景</li>
          <li>现代化UI设计</li>
          <li>圆角和阴影效果</li>
          <li>渐变色用户头像</li>
        </ul>
      </div>

      <div class="feature-card">
        <h3>⚡ 交互功能</h3>
        <ul>
          <li>展开/收起切换</li>
          <li>菜单项激活状态</li>
          <li>悬停动画效果</li>
          <li>工具提示显示</li>
        </ul>
      </div>

      <div class="feature-card">
        <h3>⌨️ 快捷键</h3>
        <ul>
          <li><kbd>Ctrl+B</kbd> 切换侧边栏</li>
          <li><kbd>ESC</kbd> 收起侧边栏</li>
          <li>状态自动保存</li>
          <li>响应式适配</li>
        </ul>
      </div>

      <div class="feature-card">
        <h3>📱 响应式</h3>
        <ul>
          <li>桌面端固定显示</li>
          <li>平板端浮层显示</li>
          <li>手机端全屏显示</li>
          <li>自适应布局</li>
        </ul>
      </div>
    </div>

    <div class="demo-actions">
      <h3>体验功能</h3>
      <div class="action-grid">
        <button class="action-btn primary" @click="$emit('navigate', 'create')">
          <div class="btn-icon">✨</div>
          <div class="btn-content">
            <strong>妙笔生花</strong>
            <span>AI全自动创作</span>
          </div>
        </button>

        <button class="action-btn secondary" @click="showEditor = !showEditor">
          <div class="btn-icon">📝</div>
          <div class="btn-content">
            <strong>AI编辑器</strong>
            <span>{{ showEditor ? '隐藏编辑器' : '智能写作助手' }}</span>
          </div>
        </button>

        <button class="action-btn secondary" @click="$emit('navigate', 'settings')">
          <div class="btn-icon">⚙️</div>
          <div class="btn-content">
            <strong>文房设置</strong>
            <span>配置AI服务</span>
          </div>
        </button>
      </div>
    </div>

    <!-- AI编辑器 -->
    <div v-if="showEditor" class="editor-section">
      <AIEditor />
    </div>

    <div class="demo-instructions">
      <h3>使用说明</h3>
      <div class="instruction-grid">
        <div class="instruction-item">
          <div class="instruction-icon">👆</div>
          <div class="instruction-text">
            <strong>点击箭头</strong><br>
            展开或收起侧边栏
          </div>
        </div>
        <div class="instruction-item">
          <div class="instruction-icon">🖱️</div>
          <div class="instruction-text">
            <strong>悬停菜单</strong><br>
            查看动画效果和工具提示
          </div>
        </div>
        <div class="instruction-item">
          <div class="instruction-icon">⌨️</div>
          <div class="instruction-text">
            <strong>键盘操作</strong><br>
            使用 Ctrl+B 或 ESC 键
          </div>
        </div>
        <div class="instruction-item">
          <div class="instruction-icon">💾</div>
          <div class="instruction-text">
            <strong>状态保存</strong><br>
            刷新页面状态不丢失
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import AIEditor from './AIEditor.vue'

export default {
  name: 'DemoContent',
  components: {
    AIEditor
  },
  emits: ['navigate'],
  setup() {
    const showEditor = ref(false)

    return {
      showEditor
    }
  }
}
</script>

<style scoped>
.demo-content {
  padding: 40px;
  max-width: 1200px;
  margin: 0 auto;
  color: #333;
}

.demo-header {
  text-align: center;
  margin-bottom: 40px;
}

.demo-header h1 {
  font-size: 2.5rem;
  margin-bottom: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.demo-header p {
  font-size: 1.2rem;
  color: #666;
}

.demo-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.feature-card {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.feature-card h3 {
  margin-bottom: 16px;
  font-size: 1.3rem;
  color: #333;
}

.feature-card ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.feature-card li {
  padding: 8px 0;
  color: #666;
  border-bottom: 1px solid #f0f0f0;
}

.feature-card li:last-child {
  border-bottom: none;
}

.demo-instructions {
  background: #f8f9fa;
  padding: 32px;
  border-radius: 16px;
  margin-top: 40px;
}

.demo-instructions h3 {
  text-align: center;
  margin-bottom: 32px;
  font-size: 1.5rem;
  color: #333;
}

.instruction-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 24px;
}

.instruction-item {
  text-align: center;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.instruction-icon {
  font-size: 2rem;
  margin-bottom: 12px;
}

.instruction-text {
  color: #666;
  line-height: 1.5;
}

.instruction-text strong {
  color: #333;
  display: block;
  margin-bottom: 4px;
}

kbd {
  background: #f1f3f4;
  border: 1px solid #dadce0;
  border-radius: 4px;
  color: #3c4043;
  font-family: monospace;
  font-size: 0.875rem;
  padding: 2px 6px;
}

/* 功能按钮区域 */
.demo-actions {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 32px;
  border-radius: 16px;
  margin-bottom: 40px;
  text-align: center;
}

.demo-actions h3 {
  color: white;
  margin-bottom: 24px;
  font-size: 1.5rem;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  max-width: 800px;
  margin: 0 auto;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  border: none;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.action-btn:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
}

.action-btn.primary {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.action-btn.primary .btn-content strong {
  color: white;
}

.action-btn.primary .btn-content span {
  color: rgba(255, 255, 255, 0.8);
}

.action-btn.secondary {
  background: rgba(255, 255, 255, 0.95);
}

.btn-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.btn-content {
  flex: 1;
}

.btn-content strong {
  display: block;
  font-size: 1.1rem;
  margin-bottom: 4px;
  color: #333;
}

.btn-content span {
  font-size: 0.9rem;
  color: #666;
}

/* 编辑器区域 */
.editor-section {
  margin-bottom: 40px;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
  .demo-content {
    padding: 20px;
  }
  
  .demo-header h1 {
    font-size: 2rem;
  }
  
  .demo-features {
    grid-template-columns: 1fr;
  }
  
  .instruction-grid {
    grid-template-columns: 1fr;
  }
}
</style>
