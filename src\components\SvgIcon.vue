<template>
  <div class="svg-icon" :style="{ width: size + 'px', height: size + 'px' }">
    <svg v-if="name === 'home'" viewBox="0 0 1024 1024" fill="currentColor">
      <path d="M468.906667 57.941333a68.394667 68.394667 0 0 1 86.165333 0l399.936 322.624c30.058667 24.234667 35.136 68.693333 11.328 99.328a69.269333 69.269333 0 0 1-37.802667 24.768l-3.712 0.810667 0.021334 361.514667c0 59.370667-44.48 108.48-102.528 113.877333l-4.736 0.362667-4.949334 0.106666H211.370667c-59.84 0-109.056-47.808-112.106667-109.312l-0.106667-5.034666-0.021333-361.514667-2.453333-0.490667c-29.696-6.848-52.16-33.088-53.909334-64.96L42.666667 436.010667c0-21.610667 9.685333-42.026667 26.325333-55.466667z m46.72 50.026667a5.76 5.76 0 0 0-7.253334 0L108.864 431.146667a6.058667 6.058667 0 0 0-2.218667 4.693333c0 3.328 2.624 6.016 5.866667 6.016h18.816c17.536-0.021333 31.744 14.506667 31.744 32.405333v392.533334l0.064 3.392C164.437333 896.533333 185.792 917.333333 211.712 917.333333h599.765333l3.328-0.085333c25.770667-1.322667 46.101333-23.146667 46.101334-49.621333V474.282667c0-17.92 14.208-32.426667 31.744-32.426667h18.816c1.792 0 3.477333-0.832 4.608-2.261333a6.101333 6.101333 0 0 0-0.96-8.426667z m138.133333 564.693333a32 32 0 0 1-3.754667 45.098667C610.858667 750.890667 568.106667 768 522.666667 768c-45.44 0-88.192-17.109333-127.36-50.24a32 32 0 1 1 41.386666-48.853333C464.704 692.650667 493.056 704 522.666667 704c29.589333 0 57.941333-11.349333 85.973333-35.093333a32 32 0 0 1 45.12 3.754666z"/>
    </svg>
    
    <svg v-else-if="name === 'library'" viewBox="0 0 1024 1024" fill="currentColor">
      <path d="M917.333333 0v774.592H247.082667c-42.218667 0-76.693333 32.149333-79.018667 71.637333l-0.149333 4.266667v35.626667c0 40 33.066667 73.557333 74.666666 75.818666l4.501334 0.128H917.333333V1024H247.082667C171.818667 1024 109.610667 965.12 106.773333 891.413333L106.666667 886.122667V173.504C106.666667 79.701333 183.253333 3.2 277.717333 0.106667L283.690667 0H917.333333z m-64 65.365333H285.504c-61.568 0-111.936 47.786667-114.709333 106.602667L170.666667 177.024 170.666667 740.757333l1.386666-0.917333a139.328 139.328 0 0 1 71.829334-23.104l5.333333-0.085333L853.333333 716.586667V65.365333z"/>
      <path d="M298.666667 43.584v718.976H234.666667V43.584h64zM917.333333 849.706667v65.365333H234.666667V849.706667h682.666666z"/>
    </svg>
    
    <svg v-else-if="name === 'garden'" viewBox="0 0 1024 1024" fill="currentColor">
      <path d="M913.317653 450.150478a35.215398 35.215398 0 0 0-44.06724 23.803988 459.058629 459.058629 0 0 1-44.707131 102.596041c-65.268999 109.890812-179.212462 172.920189-312.629976 172.920189-133.460173 0-247.424965-63.050707-312.693965-172.984178a460.125116 460.125116 0 0 1-44.643142-102.468063 35.279387 35.279387 0 0 0-44.06724-23.825318 35.791301 35.791301 0 0 0-23.548031 44.557824 531.899686 531.899686 0 0 0 51.703286 118.486696c71.305315 120.150416 195.892317 195.423063 337.927045 206.173251v168.718231c0 19.730008 15.784006 35.705982 35.322047 35.705982 19.516711 0 35.322047-15.997304 35.322047-35.705982v-168.718231c141.970739-10.750188 266.536411-86.001505 337.863056-206.109262a531.259794 531.259794 0 0 0 51.767275-118.593345 35.791301 35.791301 0 0 0-23.548031-44.557823z m16.103952-270.674381a69.833563 69.833563 0 0 0-63.136025-42.659476l-10.878167-0.085319c-62.922728 0-120.427703 19.111446-171.149821 40.398524C639.955726 100.21479 598.640022 48.916769 555.575281 17.071469a69.705585 69.705585 0 0 0-73.182333-11.432739 69.812234 69.812234 0 0 0-17.063791 10.344923c-42.872774 31.290726-85.57491 83.697893-130.111404 159.653092-50.124885-20.519208-106.712681-38.969432-167.331797-38.969432l-10.686199 0.106648a69.748245 69.748245 0 0 0-62.79475 42.616818C69.961127 237.386337 59.957479 306.49469 65.460552 379.229097a35.471355 35.471355 0 0 0 37.902945 32.890457 35.599333 35.599333 0 0 0 32.527851-38.32954c-4.607223-61.024382 3.412758-118.25207 23.164096-165.582759l9.171787-0.085319c58.870078 0 115.329895 22.460215 168.099669 45.475002 16.637196 7.252111 36.004598 0.639892 44.83511-15.400071 45.432343-82.37545 86.748046-136.297028 126.293381-164.836218 0.85319-0.597233 1.173136-1.535741 1.919676-2.218293 1.087817 1.002498 2.218293 1.941006 3.412758 2.794196 39.289378 28.624509 80.306465 82.866034 125.376203 165.860046a35.172739 35.172739 0 0 0 45.261704 15.46406c50.892756-22.80149 110.338737-47.117392 171.661735-47.117392l9.662371 0.106649c18.663521 44.664472 26.87547 98.543391 23.825318 155.984376-1.066487 19.687349 13.86433 36.516512 33.359711 37.582999 19.324743 1.386433 36.132577-14.013638 37.177734-33.722316 3.668715-68.596439-6.612219-133.844108-29.690996-188.640206z m-555.362398 126.485349c-19.516711 0-35.322047 15.975974-35.322047 35.705982v34.831463c0 19.730008 15.805336 35.727312 35.322047 35.727312 19.516711 0 35.322047-15.997304 35.322047-35.727312v-34.831463c0-19.730008-15.805336-35.705982-35.322047-35.705982z m275.708198 0c-19.516711 0-35.322047 15.975974-35.322047 35.705982v34.831463c0 19.730008 15.805336 35.727312 35.322047 35.727312 19.516711 0 35.322047-15.997304 35.322047-35.727312v-34.831463c0-19.730008-15.805336-35.705982-35.322047-35.705982z m-50.743447 161.722077a35.279387 35.279387 0 0 0-48.802442 10.024977c-0.149308 0.213297-15.400071 22.588193-38.649486 22.588192-22.609523 0-36.68715-20.711176-37.625658-22.161598a35.08742 35.08742 0 0 0-48.439836-11.155453 35.919279 35.919279 0 0 0-11.368751 49.186377c12.691194 20.625857 47.458668 55.563968 97.434245 55.563968 49.69829 0 84.935018-34.682155 97.903499-55.158703a35.769971 35.769971 0 0 0-10.451571-48.88776z m300.429365 323.294844a35.08742 35.08742 0 0 0-49.399674-7.508068L614.552007 959.419311a35.983269 35.983269 0 0 0-7.422749 49.954247 35.172739 35.172739 0 0 0 49.421004 7.486739l235.480312-175.906353c15.698687-11.731356 19.004797-34.084922 7.422749-49.954247z m-490.157388 168.419614L173.772963 783.44897a35.08742 35.08742 0 0 0-49.421003 7.508067 35.983269 35.983269 0 0 0 7.422749 49.954248l235.480311 175.906352a35.172739 35.172739 0 0 0 49.399674-7.508068 35.983269 35.983269 0 0 0-7.401419-49.932917z"/>
    </svg>
    
    <svg v-else-if="name === 'edit'" viewBox="0 0 1024 1024" fill="currentColor">
      <path d="M571.52 136.832a32 32 0 0 1 44.8 0.64l174.4 175.904a32 32 0 0 1-1.632 46.624L368.864 728.064a32 32 0 0 1-21.76 7.936l-155.776-3.328a32 32 0 0 1-31.328-32v-158.208a32 32 0 0 1 9.92-23.168z m42.464 514.496l239.84 4.672a32 32 0 1 1-1.248 64l-239.84-4.672a32 32 0 1 1 1.28-64zM592.96 204.8L224 556.16v113.184l112 2.4 385.312-337.472L592.96 204.8z m259.296 606.528a32 32 0 0 1 0.48 64l-628.48 4.672a32 32 0 0 1-0.48-64l628.48-4.672z"/>
    </svg>
    
    <svg v-else-if="name === 'settings'" viewBox="0 0 1024 1024" fill="currentColor">
      <path d="M637.461333 81.344a213.333333 213.333333 0 0 1 184.746667 106.666667L947.690667 405.333333a213.333333 213.333333 0 0 1 0 213.333334L822.186667 835.989333a213.333333 213.333333 0 0 1-184.746667 106.666667H386.56a213.333333 213.333333 0 0 1-184.746667-106.666667L76.309333 618.666667a213.333333 213.333333 0 0 1 0-213.333334L201.813333 188.010667a213.333333 213.333333 0 0 1 184.746667-106.666667z m0 85.333333H386.56a128 128 0 0 0-110.848 64L150.186667 448a128 128 0 0 0 0 128l125.482666 217.322667a128 128 0 0 0 110.848 64H637.44a128 128 0 0 0 110.848-64L873.813333 576a128 128 0 0 0 0-128l-125.482666-217.322667a128 128 0 0 0-110.848-64zM519.850667 362.666667c30.741333 0 53.888 4.992 75.285333 16.426666a120.192 120.192 0 0 1 49.749333 49.770667c11.456 21.397333 16.448 44.544 16.448 75.285333v15.701334c0 30.741333-4.992 53.888-16.426666 75.285333a120.192 120.192 0 0 1-49.770667 49.749333c-21.397333 11.456-44.544 16.448-75.285333 16.448h-15.701334c-30.741333 0-53.888-4.992-75.285333-16.426666a120.192 120.192 0 0 1-49.749333-49.770667c-11.456-21.397333-16.448-44.544-16.448-75.285333v-15.701334c0-30.741333 4.992-53.888 16.426666-75.285333a120.192 120.192 0 0 1 49.770667-49.749333c21.397333-11.456 44.544-16.448 75.285333-16.448z m0 85.333333h-15.701334c-17.514667 0-26.986667 2.048-35.029333 6.357333-6.485333 3.456-11.306667 8.277333-14.762667 14.762667-4.309333 8.042667-6.357333 17.493333-6.357333 35.029333v15.701334c0 17.514667 2.048 26.986667 6.357333 35.029333 3.456 6.485333 8.277333 11.306667 14.762667 14.762667 8.042667 4.309333 17.493333 6.357333 35.029333 6.357333h15.701334c17.514667 0 26.986667-2.048 35.029333-6.357333 6.485333-3.456 11.306667-8.277333 14.762667-14.762667 4.309333-8.042667 6.357333-17.493333 6.357333-35.029333v-15.701334c0-17.514667-2.048-26.986667-6.357333-35.029333a34.88 34.88 0 0 0-14.762667-14.762667c-8.042667-4.309333-17.493333-6.357333-35.029333-6.357333z"/>
    </svg>
    
    <svg v-else-if="name === 'projects'" viewBox="0 0 24 24" fill="currentColor">
      <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
    </svg>
    
    <svg v-else-if="name === 'dark'" viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 3c-4.97 0-9 4.03-9 9s4.03 9 9 9c.83 0 1.5-.67 1.5-1.5 0-.39-.15-.74-.39-1.01-.23-.26-.38-.61-.38-.99 0-.83.67-1.5 1.5-1.5H16c2.76 0 5-2.24 5-5 0-4.42-4.03-8-9-8z"/>
    </svg>

    <svg v-else-if="name === 'light'" viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 7c-2.76 0-5 2.24-5 5s2.24 5 5 5 5-2.24 5-5-2.24-5-5-5zM2 13h2c.55 0 1-.45 1-1s-.45-1-1-1H2c-.55 0-1 .45-1 1s.45 1 1 1zm18 0h2c.55 0 1-.45 1-1s-.45-1-1-1h-2c-.55 0-1 .45-1 1s.45 1 1 1zM11 2v2c0 .55.45 1 1 1s1-.45 1-1V2c0-.55-.45-1-1-1s-1 .45-1 1zm0 18v2c0 .55.45 1 1 1s1-.45 1-1v-2c0-.55-.45-1-1-1s-1 .45-1 1zM5.99 4.58c-.39-.39-1.03-.39-1.41 0-.39.39-.39 1.03 0 1.41l1.06 1.06c.39.39 1.03.39 1.41 0s.39-1.03 0-1.41L5.99 4.58zm12.37 12.37c-.39-.39-1.03-.39-1.41 0-.39.39-.39 1.03 0 1.41l1.06 1.06c.39.39 1.03.39 1.41 0 .39-.39.39-1.03 0-1.41l-1.06-1.06zm1.06-10.96c.39-.39.39-1.03 0-1.41-.39-.39-1.03-.39-1.41 0l-1.06 1.06c-.39.39-.39 1.03 0 1.41s1.03.39 1.41 0l1.06-1.06zM7.05 18.36c.39-.39.39-1.03 0-1.41-.39-.39-1.03-.39-1.41 0l-1.06 1.06c-.39.39-.39 1.03 0 1.41s1.03.39 1.41 0l1.06-1.06z"/>
    </svg>

    <!-- 机器人图标 -->
    <svg v-else-if="name === 'robot'" viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm6 8c-1.1 0-2 .9-2 2v6c0 1.1.9 2 2 2s2-.9 2-2v-6c0-1.1-.9-2-2-2zM6 10c-1.1 0-2 .9-2 2v6c0 1.1.9 2 2 2s2-.9 2-2v-6c0-1.1-.9-2-2-2zm6-2c-2.21 0-4 1.79-4 4v8c0 1.1.9 2 2 2h4c1.1 0 2-.9 2-2v-8c0-2.21-1.79-4-4-4z"/>
    </svg>

    <!-- 调色板图标 -->
    <svg v-else-if="name === 'palette'" viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 3c-4.97 0-9 4.03-9 9 0 4.97 4.03 9 9 9 .83 0 1.5-.67 1.5-1.5 0-.39-.15-.74-.39-1.01-.23-.26-.38-.61-.38-.99 0-.83.67-1.5 1.5-1.5H16c2.76 0 5-2.24 5-5 0-4.42-4.03-8-9-8z"/>
      <circle cx="6.5" cy="11.5" r="1.5"/>
      <circle cx="9.5" cy="7.5" r="1.5"/>
      <circle cx="14.5" cy="7.5" r="1.5"/>
      <circle cx="17.5" cy="11.5" r="1.5"/>
    </svg>

    <!-- 下载图标 -->
    <svg v-else-if="name === 'download'" viewBox="0 0 24 24" fill="currentColor">
      <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"/>
    </svg>

    <!-- 信息图标 -->
    <svg v-else-if="name === 'info'" viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"/>
    </svg>
    
    <svg v-else-if="name === 'logout'" viewBox="0 0 24 24" fill="currentColor">
      <path d="M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.58L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z"/>
    </svg>
  </div>
</template>

<script>
export default {
  name: 'SvgIcon',
  props: {
    name: {
      type: String,
      required: true
    },
    size: {
      type: Number,
      default: 20
    }
  }
}
</script>

<style scoped>
.svg-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.svg-icon svg {
  width: 100%;
  height: 100%;
}
</style>
