import { defineStore } from 'pinia'
import dbService from '../../database/localStorage'

export const useProjectStore = defineStore('project', {
  state: () => ({
    // 当前项目
    currentProject: null,
    
    // 项目列表
    projects: [],
    
    // 当前项目的章节
    chapters: [],
    
    // 当前项目的人物
    characters: [],
    
    // 当前项目的大纲
    outline: null,
    
    // 当前编辑的章节
    currentChapter: null,
    
    // 加载状态
    loading: false,
    
    // 错误信息
    error: null
  }),

  getters: {
    // 项目统计
    projectStats: (state) => {
      if (!state.currentProject) return null
      
      return {
        totalChapters: state.chapters.length,
        totalWords: state.chapters.reduce((sum, chapter) => sum + (chapter.wordCount || 0), 0),
        averageWords: state.chapters.length > 0 
          ? Math.round(state.chapters.reduce((sum, chapter) => sum + (chapter.wordCount || 0), 0) / state.chapters.length)
          : 0,
        completedChapters: state.chapters.filter(c => c.status === 'completed').length,
        draftChapters: state.chapters.filter(c => c.status === 'draft').length
      }
    },

    // 项目进度
    projectProgress: (state) => {
      if (!state.currentProject || !state.chapters.length) return 0
      
      const completed = state.chapters.filter(c => c.status === 'completed').length
      return Math.round((completed / state.chapters.length) * 100)
    },

    // 是否有当前项目
    hasCurrentProject: (state) => !!state.currentProject,

    // 主要人物列表
    mainCharacters: (state) => {
      return state.characters.filter(c => c.role === '主角' || c.role === '女主' || c.role === '反派')
    },

    // 最近更新的章节
    recentChapters: (state) => {
      return [...state.chapters]
        .sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt))
        .slice(0, 5)
    }
  },

  actions: {
    // 初始化数据库
    async initializeDatabase() {
      try {
        await dbService.initialize()
        await this.loadProjects()
      } catch (error) {
        this.setError(`数据库初始化失败: ${error.message}`)
        throw error
      }
    },

    // 加载所有项目
    async loadProjects() {
      try {
        this.setLoading(true)
        const projects = await dbService.getAllProjects()
        this.projects = projects
      } catch (error) {
        this.setError(`加载项目失败: ${error.message}`)
        throw error
      } finally {
        this.setLoading(false)
      }
    },

    // 创建新项目
    async createProject(projectData) {
      try {
        this.setLoading(true)
        
        const project = {
          id: Date.now().toString(),
          title: projectData.title,
          genre: projectData.genre,
          category: projectData.category,
          subGenre: projectData.subGenre,
          totalChapters: projectData.totalChapters || 50,
          wordsPerChapter: projectData.wordsPerChapter || 3000,
          style: projectData.style,
          background: projectData.background,
          protagonist: projectData.protagonist
        }

        const created = await dbService.createProject(project)
        this.projects.unshift(created)
        
        return { success: true, data: created }
      } catch (error) {
        this.setError(`创建项目失败: ${error.message}`)
        return { success: false, message: error.message }
      } finally {
        this.setLoading(false)
      }
    },

    // 设置当前项目
    async setCurrentProject(projectId) {
      try {
        this.setLoading(true)
        
        const project = await dbService.getProject(projectId)
        if (!project) {
          throw new Error('项目不存在')
        }
        
        this.currentProject = project
        
        // 加载项目相关数据
        await Promise.all([
          this.loadProjectChapters(projectId),
          this.loadProjectCharacters(projectId),
          this.loadProjectOutline(projectId)
        ])
        
        return { success: true, data: project }
      } catch (error) {
        this.setError(`加载项目失败: ${error.message}`)
        return { success: false, message: error.message }
      } finally {
        this.setLoading(false)
      }
    },

    // 更新项目
    async updateProject(projectId, updates) {
      try {
        const updated = await dbService.updateProject(projectId, updates)
        
        // 更新本地数据
        const index = this.projects.findIndex(p => p.id === projectId)
        if (index !== -1) {
          this.projects[index] = updated
        }
        
        if (this.currentProject && this.currentProject.id === projectId) {
          this.currentProject = updated
        }
        
        return { success: true, data: updated }
      } catch (error) {
        this.setError(`更新项目失败: ${error.message}`)
        return { success: false, message: error.message }
      }
    },

    // 删除项目
    async deleteProject(projectId) {
      try {
        await dbService.deleteProject(projectId)
        
        // 更新本地数据
        this.projects = this.projects.filter(p => p.id !== projectId)
        
        if (this.currentProject && this.currentProject.id === projectId) {
          this.currentProject = null
          this.chapters = []
          this.characters = []
          this.outline = null
        }
        
        return { success: true }
      } catch (error) {
        this.setError(`删除项目失败: ${error.message}`)
        return { success: false, message: error.message }
      }
    },

    // 加载项目章节
    async loadProjectChapters(projectId) {
      try {
        const chapters = await dbService.getProjectChapters(projectId)
        this.chapters = chapters
      } catch (error) {
        this.setError(`加载章节失败: ${error.message}`)
        throw error
      }
    },

    // 创建章节
    async createChapter(chapterData) {
      try {
        const chapter = {
          id: Date.now().toString(),
          projectId: this.currentProject.id,
          chapterNumber: chapterData.chapterNumber || this.chapters.length + 1,
          title: chapterData.title,
          content: chapterData.content || '',
          summary: chapterData.summary || '',
          wordCount: chapterData.content ? chapterData.content.replace(/\s/g, '').length : 0
        }

        const created = await dbService.createChapter(chapter)
        this.chapters.push(created)
        
        return { success: true, data: created }
      } catch (error) {
        this.setError(`创建章节失败: ${error.message}`)
        return { success: false, message: error.message }
      }
    },

    // 更新章节
    async updateChapter(chapterId, updates) {
      try {
        const updated = await dbService.updateChapter(chapterId, updates)
        
        // 更新本地数据
        const index = this.chapters.findIndex(c => c.id === chapterId)
        if (index !== -1) {
          this.chapters[index] = updated
        }
        
        if (this.currentChapter && this.currentChapter.id === chapterId) {
          this.currentChapter = updated
        }
        
        return { success: true, data: updated }
      } catch (error) {
        this.setError(`更新章节失败: ${error.message}`)
        return { success: false, message: error.message }
      }
    },

    // 删除章节
    async deleteChapter(chapterId) {
      try {
        await dbService.deleteChapter(chapterId)
        
        // 更新本地数据
        this.chapters = this.chapters.filter(c => c.id !== chapterId)
        
        if (this.currentChapter && this.currentChapter.id === chapterId) {
          this.currentChapter = null
        }
        
        return { success: true }
      } catch (error) {
        this.setError(`删除章节失败: ${error.message}`)
        return { success: false, message: error.message }
      }
    },

    // 设置当前章节
    setCurrentChapter(chapterId) {
      const chapter = this.chapters.find(c => c.id === chapterId)
      if (chapter) {
        this.currentChapter = chapter
      }
    },

    // 加载项目人物
    async loadProjectCharacters(projectId) {
      try {
        const characters = await dbService.getProjectCharacters(projectId)
        this.characters = characters
      } catch (error) {
        this.setError(`加载人物失败: ${error.message}`)
        throw error
      }
    },

    // 创建人物
    async createCharacter(characterData) {
      try {
        const character = {
          id: Date.now().toString(),
          projectId: this.currentProject.id,
          ...characterData
        }

        const created = await dbService.createCharacter(character)
        this.characters.push(created)
        
        return { success: true, data: created }
      } catch (error) {
        this.setError(`创建人物失败: ${error.message}`)
        return { success: false, message: error.message }
      }
    },

    // 更新人物
    async updateCharacter(characterId, updates) {
      try {
        const updated = await dbService.updateCharacter(characterId, updates)
        
        // 更新本地数据
        const index = this.characters.findIndex(c => c.id === characterId)
        if (index !== -1) {
          this.characters[index] = updated
        }
        
        return { success: true, data: updated }
      } catch (error) {
        this.setError(`更新人物失败: ${error.message}`)
        return { success: false, message: error.message }
      }
    },

    // 删除人物
    async deleteCharacter(characterId) {
      try {
        await dbService.deleteCharacter(characterId)
        
        // 更新本地数据
        this.characters = this.characters.filter(c => c.id !== characterId)
        
        return { success: true }
      } catch (error) {
        this.setError(`删除人物失败: ${error.message}`)
        return { success: false, message: error.message }
      }
    },

    // 加载项目大纲
    async loadProjectOutline(projectId) {
      try {
        const outline = await dbService.getOutline(projectId)
        this.outline = outline
      } catch (error) {
        this.setError(`加载大纲失败: ${error.message}`)
        throw error
      }
    },

    // 保存大纲
    async saveOutline(outlineData) {
      try {
        const outline = {
          projectId: this.currentProject.id,
          ...outlineData
        }

        const saved = await dbService.saveOutline(outline)
        this.outline = saved
        
        return { success: true, data: saved }
      } catch (error) {
        this.setError(`保存大纲失败: ${error.message}`)
        return { success: false, message: error.message }
      }
    },

    // 批量创建章节
    async createMultipleChapters(chaptersData) {
      try {
        this.setLoading(true)
        const results = []
        
        for (let i = 0; i < chaptersData.length; i++) {
          const chapterData = chaptersData[i]
          const result = await this.createChapter(chapterData)
          
          if (result.success) {
            results.push(result.data)
          } else {
            throw new Error(`创建第${i + 1}章失败: ${result.message}`)
          }
        }
        
        return { success: true, data: results }
      } catch (error) {
        this.setError(`批量创建章节失败: ${error.message}`)
        return { success: false, message: error.message }
      } finally {
        this.setLoading(false)
      }
    },

    // 导出项目
    async exportProject(format = 'txt') {
      try {
        if (!this.currentProject) {
          throw new Error('没有选择项目')
        }

        const chapters = this.chapters.sort((a, b) => a.chapterNumber - b.chapterNumber)
        let content = ''

        if (format === 'txt') {
          content = `${this.currentProject.title}\n\n`
          
          chapters.forEach(chapter => {
            content += `${chapter.title}\n\n`
            content += `${chapter.content}\n\n`
            content += '---\n\n'
          })
        }

        return { success: true, data: content }
      } catch (error) {
        this.setError(`导出失败: ${error.message}`)
        return { success: false, message: error.message }
      }
    },

    // 搜索章节
    searchChapters(keyword) {
      if (!keyword.trim()) return this.chapters
      
      return this.chapters.filter(chapter => 
        chapter.title.includes(keyword) || 
        (chapter.content && chapter.content.includes(keyword))
      )
    },

    // 获取章节统计
    getChapterStats(chapterId) {
      const chapter = this.chapters.find(c => c.id === chapterId)
      if (!chapter || !chapter.content) return null

      const content = chapter.content
      return {
        wordCount: content.replace(/\s/g, '').length,
        paragraphCount: content.split('\n').filter(p => p.trim()).length,
        dialogueCount: (content.match(/"/g) || []).length / 2,
        averageWordsPerParagraph: Math.round(content.replace(/\s/g, '').length / content.split('\n').filter(p => p.trim()).length)
      }
    },

    // 状态管理
    setLoading(loading) {
      this.loading = loading
    },

    setError(error) {
      this.error = error
    },

    clearError() {
      this.error = null
    },

    // 重置状态
    reset() {
      this.currentProject = null
      this.chapters = []
      this.characters = []
      this.outline = null
      this.currentChapter = null
      this.clearError()
      this.setLoading(false)
    }
  }
})