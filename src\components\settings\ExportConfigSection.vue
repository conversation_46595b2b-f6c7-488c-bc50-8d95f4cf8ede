<template>
  <div class="export-config-section">
    <div class="section-header">
      <h2 class="section-title">导出设置</h2>
      <p class="section-subtitle">配置文档导出选项</p>
    </div>

    <div class="config-form">
      <!-- 默认格式 -->
      <div class="settings-group">
        <h3 class="group-title">导出格式</h3>
        
        <div class="format-options">
          <div 
            v-for="format in exportFormats" 
            :key="format.value"
            class="format-option"
            :class="{ 'format-option--active': localConfig.defaultFormat === format.value }"
            @click="setDefaultFormat(format.value)"
          >
            <div class="format-icon">{{ format.icon }}</div>
            <div class="format-info">
              <h4 class="format-name">{{ format.name }}</h4>
              <p class="format-desc">{{ format.description }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 内容选项 -->
      <div class="settings-group">
        <h3 class="group-title">内容选项</h3>
        
        <div class="form-group">
          <label class="form-checkbox">
            <input type="checkbox" v-model="localConfig.includeChapterNumbers" />
            <span class="checkbox-mark"></span>
            包含章节号
          </label>
          <div class="form-hint">在导出文档中显示章节编号</div>
        </div>

        <div class="form-group">
          <label class="form-checkbox">
            <input type="checkbox" v-model="localConfig.includeTableOfContents" />
            <span class="checkbox-mark"></span>
            包含目录
          </label>
          <div class="form-hint">自动生成文档目录</div>
        </div>

        <div class="form-group">
          <label class="form-checkbox">
            <input type="checkbox" v-model="localConfig.includeMetadata" />
            <span class="checkbox-mark"></span>
            包含元数据
          </label>
          <div class="form-hint">包含作者、创建时间等信息</div>
        </div>

        <div class="form-group">
          <label class="form-checkbox">
            <input type="checkbox" v-model="localConfig.includeWordCount" />
            <span class="checkbox-mark"></span>
            包含字数统计
          </label>
          <div class="form-hint">在文档末尾显示字数统计</div>
        </div>
      </div>

      <!-- 格式化选项 -->
      <div class="settings-group">
        <h3 class="group-title">格式化选项</h3>
        
        <div class="form-group">
          <label class="form-label">段落间距</label>
          <select v-model="localConfig.paragraphSpacing" class="form-select">
            <option value="single">单倍行距</option>
            <option value="1.5">1.5倍行距</option>
            <option value="double">双倍行距</option>
          </select>
        </div>

        <div class="form-group">
          <label class="form-label">页边距</label>
          <select v-model="localConfig.pageMargin" class="form-select">
            <option value="narrow">窄边距</option>
            <option value="normal">标准边距</option>
            <option value="wide">宽边距</option>
          </select>
        </div>

        <div class="form-group">
          <label class="form-label">字体大小</label>
          <select v-model="localConfig.exportFontSize" class="form-select">
            <option value="10">10pt</option>
            <option value="11">11pt</option>
            <option value="12">12pt</option>
            <option value="14">14pt</option>
            <option value="16">16pt</option>
          </select>
        </div>
      </div>

      <!-- 文件命名 -->
      <div class="settings-group">
        <h3 class="group-title">文件命名</h3>
        
        <div class="form-group">
          <label class="form-label">命名模式</label>
          <select v-model="localConfig.namingPattern" class="form-select">
            <option value="title">使用标题</option>
            <option value="title-date">标题 + 日期</option>
            <option value="title-time">标题 + 时间戳</option>
            <option value="custom">自定义前缀</option>
          </select>
        </div>

        <div class="form-group" v-if="localConfig.namingPattern === 'custom'">
          <label class="form-label">自定义前缀</label>
          <input 
            type="text" 
            v-model="localConfig.customPrefix"
            class="form-input"
            placeholder="输入文件名前缀"
          />
        </div>

        <div class="form-group">
          <label class="form-checkbox">
            <input type="checkbox" v-model="localConfig.replaceSpaces" />
            <span class="checkbox-mark"></span>
            替换空格为下划线
          </label>
          <div class="form-hint">将文件名中的空格替换为下划线</div>
        </div>
      </div>

      <!-- 预览示例 -->
      <div class="preview-section">
        <h3 class="group-title">文件名预览</h3>
        <div class="filename-preview">
          <span class="preview-filename">{{ previewFilename }}</span>
          <span class="preview-extension">.{{ localConfig.defaultFormat }}</span>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="form-actions">
        <button type="button" class="save-btn" @click="saveConfig">
          保存设置
        </button>
        <button type="button" class="reset-btn" @click="resetConfig">
          重置默认
        </button>
        <button type="button" class="test-btn" @click="testExport">
          测试导出
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'

export default {
  name: 'ExportConfigSection',
  props: {
    modelValue: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:modelValue', 'save'],
  setup(props, { emit }) {
    const localConfig = ref({
      defaultFormat: 'txt',
      includeChapterNumbers: true,
      includeTableOfContents: true,
      includeMetadata: false,
      includeWordCount: true,
      paragraphSpacing: '1.5',
      pageMargin: 'normal',
      exportFontSize: '12',
      namingPattern: 'title-date',
      customPrefix: '',
      replaceSpaces: true,
      ...props.modelValue
    })

    // 导出格式选项
    const exportFormats = [
      {
        value: 'txt',
        name: 'TXT文本',
        description: '纯文本格式，兼容性最好',
        icon: '📄'
      },
      {
        value: 'docx',
        name: 'Word文档',
        description: '微软Word格式，支持丰富格式',
        icon: '📘'
      },
      {
        value: 'pdf',
        name: 'PDF文档',
        description: '便携式文档格式，适合分享',
        icon: '📕'
      },
      {
        value: 'md',
        name: 'Markdown',
        description: '轻量级标记语言',
        icon: '📝'
      }
    ]

    // 文件名预览
    const previewFilename = computed(() => {
      const title = '我的小说作品'
      const now = new Date()
      const dateStr = now.toISOString().split('T')[0]
      const timeStr = now.getTime().toString()

      let filename = ''
      switch (localConfig.value.namingPattern) {
        case 'title':
          filename = title
          break
        case 'title-date':
          filename = `${title}_${dateStr}`
          break
        case 'title-time':
          filename = `${title}_${timeStr}`
          break
        case 'custom':
          filename = localConfig.value.customPrefix ? 
            `${localConfig.value.customPrefix}_${title}` : title
          break
        default:
          filename = title
      }

      return localConfig.value.replaceSpaces ? 
        filename.replace(/\s+/g, '_') : filename
    })

    // 设置默认格式
    const setDefaultFormat = (format) => {
      localConfig.value.defaultFormat = format
    }

    // 保存配置
    const saveConfig = () => {
      emit('update:modelValue', localConfig.value)
      emit('save')
    }

    // 重置配置
    const resetConfig = () => {
      localConfig.value = {
        defaultFormat: 'txt',
        includeChapterNumbers: true,
        includeTableOfContents: true,
        includeMetadata: false,
        includeWordCount: true,
        paragraphSpacing: '1.5',
        pageMargin: 'normal',
        exportFontSize: '12',
        namingPattern: 'title-date',
        customPrefix: '',
        replaceSpaces: true
      }
    }

    // 测试导出
    const testExport = () => {
      console.log('测试导出配置:', localConfig.value)
      // TODO: 实现测试导出功能
    }

    // 监听props变化
    watch(() => props.modelValue, (newValue) => {
      localConfig.value = { ...localConfig.value, ...newValue }
    }, { deep: true })

    return {
      localConfig,
      exportFormats,
      previewFilename,
      setDefaultFormat,
      saveConfig,
      resetConfig,
      testExport
    }
  }
}
</script>

<style scoped>
/* 基础样式与其他组件类似，这里简化处理 */
.export-config-section {
  max-width: 600px;
}

.section-header {
  margin-bottom: 32px;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.section-subtitle {
  font-size: 14px;
  opacity: 0.7;
  margin: 0;
}

.config-form {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.settings-group {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.group-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.format-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.format-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border: 2px solid rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.format-option:hover {
  border-color: rgba(74, 144, 226, 0.5);
}

.format-option--active {
  border-color: #4a90e2;
  background: rgba(74, 144, 226, 0.1);
}

.format-icon {
  font-size: 24px;
}

.format-name {
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 4px 0;
}

.format-desc {
  font-size: 12px;
  opacity: 0.7;
  margin: 0;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  font-size: 14px;
  font-weight: 500;
}

.form-select,
.form-input {
  padding: 12px 16px;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.8);
  font-size: 14px;
}

.form-checkbox {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  font-size: 14px;
}

.form-checkbox input[type="checkbox"] {
  display: none;
}

.checkbox-mark {
  width: 18px;
  height: 18px;
  border: 2px solid rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  position: relative;
  transition: all 0.2s ease;
}

.form-checkbox input[type="checkbox"]:checked + .checkbox-mark {
  background: #4a90e2;
  border-color: #4a90e2;
}

.form-checkbox input[type="checkbox"]:checked + .checkbox-mark::after {
  content: '';
  position: absolute;
  left: 5px;
  top: 2px;
  width: 4px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.form-hint {
  font-size: 12px;
  opacity: 0.6;
}

.preview-section {
  padding: 20px;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.filename-preview {
  font-family: 'Courier New', monospace;
  font-size: 14px;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.preview-filename {
  color: #333;
}

.preview-extension {
  color: #666;
  font-weight: bold;
}

.form-actions {
  display: flex;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.save-btn,
.reset-btn,
.test-btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.save-btn {
  background: #4a90e2;
  color: white;
  border: none;
}

.save-btn:hover {
  background: #357abd;
}

.reset-btn {
  background: transparent;
  color: #666;
  border: 1px solid rgba(0, 0, 0, 0.2);
}

.test-btn {
  background: #34c759;
  color: white;
  border: none;
}

.test-btn:hover {
  background: #28a745;
}
</style>
