// 记忆数据库 - 用于维护长篇网文的一致性
export class MemoryDatabase {
  constructor() {
    this.characters = new Map() // 角色信息
    this.worldSettings = {} // 世界观设定
    this.plotLines = {} // 情节线
    this.foreshadowing = [] // 伏笔
    this.chapterSummaries = [] // 章节摘要
    this.relationships = new Map() // 角色关系
    this.locations = new Map() // 地点信息
    this.timeline = [] // 时间线
  }

  // 初始化记忆库（基于准备工作）
  initializeFromPreparation(preparationData) {
    const { outline, characters, creativity, metadata } = preparationData

    // 解析并存储基础信息
    this.metadata = {
      title: metadata.title,
      genre: metadata.genre,
      category: metadata.category,
      totalChapters: metadata.chapterCount,
      wordsPerChapter: metadata.wordsPerChapter,
      creativity: creativity, // 用户的核心创意
      createdAt: new Date().toISOString()
    }

    // 解析角色信息
    this.parseCharacters(characters)
    
    // 解析世界观设定
    this.parseWorldSettings(outline)
    
    // 解析情节线
    this.parsePlotLines(outline)

    console.log('📚 记忆库初始化完成:', {
      角色数量: this.characters.size,
      世界观设定: Object.keys(this.worldSettings).length,
      情节线: Object.keys(this.plotLines).length
    })
  }

  // 解析角色信息
  parseCharacters(charactersData) {
    if (Array.isArray(charactersData)) {
      charactersData.forEach(char => {
        this.addCharacter({
          name: char.name || '未命名',
          role: char.role || '配角',
          description: char.description || '',
          status: 'alive',
          lastAppeared: 0,
          relationships: [],
          traits: this.extractTraits(char.description)
        })
      })
    }
  }

  // 解析世界观设定
  parseWorldSettings(outline) {
    this.worldSettings = {
      genre: this.metadata.genre,
      background: this.extractBackground(outline),
      powerSystem: this.extractPowerSystem(outline),
      organizations: this.extractOrganizations(outline),
      rules: this.extractRules(outline),
      creativity: this.metadata.creativity // 核心创意
    }
  }

  // 解析情节线
  parsePlotLines(outline) {
    this.plotLines = {
      mainPlot: {
        description: this.extractMainPlot(outline),
        currentStage: 'beginning',
        keyEvents: [],
        completed: [],
        upcoming: []
      },
      subPlots: [],
      creativityPlot: {
        description: this.metadata.creativity,
        implementation: 'pending',
        keyMoments: []
      }
    }
  }

  // 添加角色
  addCharacter(characterData) {
    const { name, role, description, status = 'alive', traits = [] } = characterData
    
    this.characters.set(name, {
      name,
      role,
      description,
      status,
      lastAppeared: 0,
      relationships: [],
      traits,
      development: [],
      createdAt: new Date().toISOString()
    })
  }

  // 更新角色信息
  updateCharacter(name, updates) {
    if (this.characters.has(name)) {
      const character = this.characters.get(name)
      Object.assign(character, updates)
      character.updatedAt = new Date().toISOString()
    }
  }

  // 记录角色出现
  recordCharacterAppearance(name, chapterNumber) {
    if (this.characters.has(name)) {
      const character = this.characters.get(name)
      character.lastAppeared = chapterNumber
    }
  }

  // 添加角色关系
  addRelationship(character1, character2, relationship) {
    this.relationships.set(`${character1}-${character2}`, {
      from: character1,
      to: character2,
      relationship,
      established: new Date().toISOString()
    })
  }

  // 添加伏笔
  addForeshadowing(chapterNumber, content, plannedResolution) {
    this.foreshadowing.push({
      id: Date.now(),
      chapter: chapterNumber,
      content,
      plannedResolution,
      resolved: false,
      createdAt: new Date().toISOString()
    })
  }

  // 解决伏笔
  resolveForeshadowing(id, resolutionChapter) {
    const foreshadow = this.foreshadowing.find(f => f.id === id)
    if (foreshadow) {
      foreshadow.resolved = true
      foreshadow.resolutionChapter = resolutionChapter
      foreshadow.resolvedAt = new Date().toISOString()
    }
  }

  // 添加章节摘要（增强版）
  addChapterSummary(chapterNumber, summary, fullContent = '') {
    this.chapterSummaries[chapterNumber - 1] = {
      chapter: chapterNumber,
      summary,
      fullContent, // 保存完整内容用于深度分析
      keyEvents: this.extractKeyEvents(summary),
      charactersAppeared: this.extractCharacters(summary),

      // 新增：详细的章节状态信息
      chapterEnding: this.extractChapterEnding(fullContent),
      emotionalTone: this.extractEmotionalTone(fullContent),
      sceneLocation: this.extractSceneLocation(fullContent),
      timeOfDay: this.extractTimeOfDay(fullContent),
      weatherMood: this.extractWeatherMood(fullContent),

      // 角色详细状态
      characterStates: this.extractDetailedCharacterStates(fullContent),
      characterRelationships: this.extractCharacterRelationships(fullContent),
      characterDialogueStyle: this.extractDialogueStyles(fullContent),

      // 情节线索
      plotThreads: this.extractPlotThreads(fullContent),
      foreshadowingElements: this.extractForeshadowingElements(fullContent),
      conflictStatus: this.extractConflictStatus(fullContent),

      createdAt: new Date().toISOString()
    }
  }

  // 获取相关记忆（智能检索和压缩）
  getRelevantMemory(chapterNumber, context = '') {
    // 智能压缩：根据章节数量动态调整记忆详细程度
    const compressionLevel = this.calculateCompressionLevel(chapterNumber)

    return {
      // 核心记忆：永远保持的关键信息
      core: this.getCoreMemory(),

      // 动态记忆：根据压缩级别调整
      dynamic: this.getDynamicMemory(chapterNumber, compressionLevel),

      // 上下文相关：基于当前章节内容
      contextual: this.getContextualMemory(context, compressionLevel),

      // 质量检查：用于一致性验证
      qualityCheck: this.getQualityCheckData(chapterNumber)
    }
  }

  // 计算压缩级别
  calculateCompressionLevel(chapterNumber) {
    if (chapterNumber <= 50) return 'detailed'      // 前50章：详细记忆
    if (chapterNumber <= 200) return 'moderate'     // 51-200章：中等压缩
    if (chapterNumber <= 500) return 'compressed'   // 201-500章：高压缩
    return 'minimal'                                // 500+章：最小记忆
  }

  // 获取核心记忆（永不压缩）
  getCoreMemory() {
    return {
      creativity: this.metadata.creativity,
      genre: this.metadata.genre,
      title: this.metadata.title,
      mainCharacter: this.getMainCharacter(),
      worldRules: this.worldSettings.rules || [],
      coreSettings: this.worldSettings.creativity
    }
  }

  // 获取动态记忆（智能压缩）
  getDynamicMemory(chapterNumber, compressionLevel) {
    const recentCount = {
      'detailed': 10,
      'moderate': 7,
      'compressed': 5,
      'minimal': 3
    }[compressionLevel]

    const maxCharacters = {
      'detailed': 20,
      'moderate': 15,
      'compressed': 10,
      'minimal': 5
    }[compressionLevel]

    return {
      recentChapters: this.getRecentChapters(chapterNumber, recentCount),
      activeCharacters: this.getActiveCharacters(maxCharacters),
      currentPlots: this.getCurrentPlots(compressionLevel),
      pendingForeshadowing: this.getPendingForeshadowing()
    }
  }

  // 获取上下文相关记忆
  getContextualMemory(context, compressionLevel) {
    const relevantCharacters = this.getRelevantCharacters(context)
    const relevantPlots = this.getRelevantPlots(context)

    // 根据压缩级别限制数量
    const maxItems = {
      'detailed': 10,
      'moderate': 7,
      'compressed': 5,
      'minimal': 3
    }[compressionLevel]

    return {
      characters: relevantCharacters.slice(0, maxItems),
      plots: relevantPlots.slice(0, maxItems),
      locations: this.getRelevantLocations(context).slice(0, maxItems)
    }
  }

  // 获取质量检查数据
  getQualityCheckData(chapterNumber) {
    return {
      characterStates: this.getCharacterStatesSnapshot(),
      plotConsistency: this.getPlotConsistencyData(),
      timelineEvents: this.getTimelineEvents(),
      previousInconsistencies: this.getKnownInconsistencies()
    }
  }

  // 辅助方法
  extractTraits(description) {
    // 简单的特征提取，实际可以更复杂
    const traits = []
    if (description.includes('聪明') || description.includes('智慧')) traits.push('聪明')
    if (description.includes('勇敢') || description.includes('无畏')) traits.push('勇敢')
    if (description.includes('冷酷') || description.includes('残忍')) traits.push('冷酷')
    return traits
  }

  extractBackground(outline) {
    // 从大纲中提取背景信息
    return outline.substring(0, 500) + '...'
  }

  extractPowerSystem(outline) {
    // 提取力量体系
    if (outline.includes('修炼') || outline.includes('境界')) {
      return ['炼气', '筑基', '金丹', '元婴']
    }
    return []
  }

  extractOrganizations(outline) {
    // 提取组织信息
    return []
  }

  extractRules(outline) {
    // 提取世界规则
    return ['实力为尊', '弱肉强食']
  }

  extractMainPlot(outline) {
    // 提取主线剧情
    return outline.substring(0, 200) + '...'
  }

  getMainCharacter() {
    // 获取主角信息
    for (const [name, character] of this.characters) {
      if (character.role === '主角') {
        return character
      }
    }
    return null
  }

  getRecentChapters(currentChapter, count) {
    const start = Math.max(0, currentChapter - count - 1)
    const end = currentChapter - 1
    return this.chapterSummaries.slice(start, end)
  }

  getRelevantCharacters(context) {
    // 基于上下文返回相关角色
    const relevant = []
    for (const [name, character] of this.characters) {
      if (context.includes(name) || character.role === '主角') {
        relevant.push(character)
      }
    }
    return relevant
  }

  getPendingForeshadowing() {
    return this.foreshadowing.filter(f => !f.resolved)
  }

  getCreativityElements() {
    return {
      coreCreativity: this.metadata.creativity,
      implementation: this.plotLines.creativityPlot.implementation,
      keyMoments: this.plotLines.creativityPlot.keyMoments
    }
  }

  extractKeyEvents(summary) {
    // 从摘要中提取关键事件
    return []
  }

  extractCharacters(summary) {
    // 从摘要中提取出现的角色
    const appeared = []
    for (const [name] of this.characters) {
      if (summary.includes(name)) {
        appeared.push(name)
      }
    }
    return appeared
  }

  // 导出记忆库数据
  export() {
    return {
      metadata: this.metadata,
      characters: Object.fromEntries(this.characters),
      worldSettings: this.worldSettings,
      plotLines: this.plotLines,
      foreshadowing: this.foreshadowing,
      chapterSummaries: this.chapterSummaries,
      relationships: Object.fromEntries(this.relationships),
      locations: Object.fromEntries(this.locations),
      timeline: this.timeline
    }
  }

  // 获取活跃角色（按重要性和最近出现排序）
  getActiveCharacters(maxCount) {
    const characters = Array.from(this.characters.values())
    return characters
      .filter(char => char.status === 'alive')
      .sort((a, b) => {
        // 按重要性和最近出现排序
        const importanceA = char => char.role === '主角' ? 100 : (char.role === '女主' ? 90 : char.lastAppeared)
        const importanceB = char => char.role === '主角' ? 100 : (char.role === '女主' ? 90 : char.lastAppeared)
        return importanceB(b) - importanceA(a)
      })
      .slice(0, maxCount)
  }

  // 获取当前情节线
  getCurrentPlots(compressionLevel) {
    const plots = []

    // 主线情节
    if (this.plotLines.mainPlot) {
      plots.push({
        type: 'main',
        description: this.plotLines.mainPlot.description,
        stage: this.plotLines.mainPlot.currentStage
      })
    }

    // 创意情节线
    if (this.plotLines.creativityPlot) {
      plots.push({
        type: 'creativity',
        description: this.plotLines.creativityPlot.description,
        implementation: this.plotLines.creativityPlot.implementation
      })
    }

    // 支线情节（根据压缩级别）
    const maxSubPlots = compressionLevel === 'detailed' ? 5 : (compressionLevel === 'moderate' ? 3 : 1)
    const subPlots = this.plotLines.subPlots?.slice(0, maxSubPlots) || []
    plots.push(...subPlots)

    return plots
  }

  // 获取相关情节
  getRelevantPlots(context) {
    const plots = []

    // 简单的关键词匹配
    if (context.includes('战斗') || context.includes('打斗')) {
      plots.push({ type: 'action', relevance: 'high' })
    }
    if (context.includes('感情') || context.includes('爱情')) {
      plots.push({ type: 'romance', relevance: 'high' })
    }

    return plots
  }

  // 获取相关地点
  getRelevantLocations(context) {
    const locations = []
    for (const [name, location] of this.locations) {
      if (context.includes(name)) {
        locations.push(location)
      }
    }
    return locations
  }

  // 获取角色状态快照
  getCharacterStatesSnapshot() {
    const snapshot = {}
    for (const [name, character] of this.characters) {
      snapshot[name] = {
        status: character.status,
        lastAppeared: character.lastAppeared,
        currentState: character.currentState || 'normal'
      }
    }
    return snapshot
  }

  // 获取情节一致性数据
  getPlotConsistencyData() {
    return {
      mainPlotStage: this.plotLines.mainPlot?.currentStage,
      creativityImplementation: this.plotLines.creativityPlot?.implementation,
      activeSubPlots: this.plotLines.subPlots?.filter(plot => plot.status === 'active') || []
    }
  }

  // 获取时间线事件
  getTimelineEvents() {
    return this.timeline.slice(-10) // 最近10个事件
  }

  // 获取已知的不一致问题
  getKnownInconsistencies() {
    return this.inconsistencies || []
  }

  // 记录不一致问题
  recordInconsistency(chapterNumber, type, description, severity = 'medium') {
    if (!this.inconsistencies) {
      this.inconsistencies = []
    }

    this.inconsistencies.push({
      id: Date.now(),
      chapter: chapterNumber,
      type,
      description,
      severity,
      resolved: false,
      createdAt: new Date().toISOString()
    })
  }

  // 解决不一致问题
  resolveInconsistency(id, resolution) {
    const inconsistency = this.inconsistencies?.find(inc => inc.id === id)
    if (inconsistency) {
      inconsistency.resolved = true
      inconsistency.resolution = resolution
      inconsistency.resolvedAt = new Date().toISOString()
    }
  }

  // 导入记忆库数据
  import(data) {
    this.metadata = data.metadata
    this.characters = new Map(Object.entries(data.characters || {}))
    this.worldSettings = data.worldSettings || {}
    this.plotLines = data.plotLines || {}
    this.foreshadowing = data.foreshadowing || []
    this.chapterSummaries = data.chapterSummaries || []
    this.relationships = new Map(Object.entries(data.relationships || {}))
    this.locations = new Map(Object.entries(data.locations || {}))
    this.timeline = data.timeline || []
    this.inconsistencies = data.inconsistencies || []
  }

  // 新增：详细状态提取方法

  // 提取章节结尾状态
  extractChapterEnding(content) {
    if (!content) return { lastSentences: '结尾信息缺失', endingMood: 'neutral', cliffhanger: false, nextChapterHint: '' }

    // 获取最后几句话
    const sentences = content.split(/[。！？]/).filter(s => s.trim().length > 0)
    const lastSentences = sentences.slice(-3).join('。')

    return {
      lastSentences: lastSentences,
      endingMood: this.analyzeEndingMood(lastSentences),
      cliffhanger: this.hasCliffhanger(lastSentences),
      nextChapterHint: this.extractNextChapterHint(lastSentences)
    }
  }

  // 分析结尾情绪
  analyzeEndingMood(text) {
    if (text.includes('悬念') || text.includes('突然') || text.includes('意外')) return 'suspenseful'
    if (text.includes('满意') || text.includes('成功') || text.includes('胜利')) return 'triumphant'
    if (text.includes('担心') || text.includes('危险') || text.includes('威胁')) return 'worried'
    if (text.includes('平静') || text.includes('安静') || text.includes('休息')) return 'peaceful'
    return 'neutral'
  }

  // 检查是否有悬念
  hasCliffhanger(text) {
    const cliffhangerWords = ['突然', '忽然', '这时', '就在这时', '不料', '没想到', '意外']
    return cliffhangerWords.some(word => text.includes(word))
  }

  // 提取下章提示
  extractNextChapterHint(text) {
    if (text.includes('明天') || text.includes('第二天')) return '时间推进'
    if (text.includes('前往') || text.includes('出发')) return '场景转换'
    if (text.includes('决定') || text.includes('打算')) return '行动计划'
    return ''
  }

  // 提取情感基调
  extractEmotionalTone(content) {
    if (!content) return 'neutral'

    const positiveWords = ['高兴', '兴奋', '满意', '开心', '愉快', '欣喜']
    const negativeWords = ['愤怒', '悲伤', '失望', '绝望', '痛苦', '恐惧']
    const tenseWords = ['紧张', '危险', '急迫', '焦虑', '担心']

    let positive = 0, negative = 0, tense = 0

    positiveWords.forEach(word => {
      if (content.includes(word)) positive++
    })
    negativeWords.forEach(word => {
      if (content.includes(word)) negative++
    })
    tenseWords.forEach(word => {
      if (content.includes(word)) tense++
    })

    if (tense > positive && tense > negative) return 'tense'
    if (positive > negative) return 'positive'
    if (negative > positive) return 'negative'
    return 'neutral'
  }

  // 提取场景位置
  extractSceneLocation(content) {
    if (!content) return '位置不明确'

    const locations = [
      '宗门', '山洞', '城市', '森林', '宫殿', '客栈', '家中', '房间',
      '大殿', '练功房', '藏书阁', '后山', '广场', '街道', '酒楼'
    ]

    for (const location of locations) {
      if (content.includes(location)) {
        return location
      }
    }
    return '位置不明确'
  }

  // 提取时间
  extractTimeOfDay(content) {
    if (!content) return '时间不明确'

    if (content.includes('清晨') || content.includes('早晨') || content.includes('黎明')) return '清晨'
    if (content.includes('上午') || content.includes('午前')) return '上午'
    if (content.includes('中午') || content.includes('正午')) return '中午'
    if (content.includes('下午') || content.includes('午后')) return '下午'
    if (content.includes('傍晚') || content.includes('黄昏')) return '傍晚'
    if (content.includes('夜晚') || content.includes('深夜') || content.includes('半夜')) return '夜晚'

    return '时间不明确'
  }

  // 提取天气氛围
  extractWeatherMood(content) {
    if (!content) return '天气不明确'

    if (content.includes('阳光') || content.includes('晴朗')) return '晴朗'
    if (content.includes('阴云') || content.includes('多云')) return '阴天'
    if (content.includes('下雨') || content.includes('雨水')) return '雨天'
    if (content.includes('雪花') || content.includes('下雪')) return '雪天'
    if (content.includes('雾气') || content.includes('迷雾')) return '雾天'

    return '天气不明确'
  }

  // 提取详细角色状态
  extractDetailedCharacterStates(content) {
    const states = new Map()

    for (const [name, character] of this.characters) {
      if (content.includes(name)) {
        states.set(name, {
          present: true,
          mood: this.extractCharacterMood(name, content),
          action: this.extractCharacterAction(name, content),
          location: this.extractCharacterLocation(name, content),
          injury: this.extractCharacterInjury(name, content),
          power: this.extractCharacterPower(name, content)
        })
      }
    }

    return Object.fromEntries(states)
  }

  // 提取角色情绪
  extractCharacterMood(name, content) {
    const nameIndex = content.indexOf(name)
    if (nameIndex === -1) return 'unknown'

    // 在角色名字附近查找情绪词
    const context = content.substring(Math.max(0, nameIndex - 50), nameIndex + 100)

    if (context.includes('愤怒') || context.includes('生气')) return 'angry'
    if (context.includes('高兴') || context.includes('开心')) return 'happy'
    if (context.includes('担心') || context.includes('焦虑')) return 'worried'
    if (context.includes('冷静') || context.includes('平静')) return 'calm'
    if (context.includes('兴奋') || context.includes('激动')) return 'excited'

    return 'neutral'
  }

  // 提取角色行动
  extractCharacterAction(name, content) {
    const actionPatterns = [
      `${name}说`, `${name}道`, `${name}走`, `${name}跑`, `${name}站`,
      `${name}坐`, `${name}看`, `${name}想`, `${name}做`, `${name}拿`
    ]

    for (const pattern of actionPatterns) {
      if (content.includes(pattern)) {
        return pattern.replace(name, '').trim()
      }
    }

    return 'unknown'
  }

  // 提取角色位置
  extractCharacterLocation(name, content) {
    // 简化实现，返回整体场景位置
    return this.extractSceneLocation(content)
  }

  // 提取角色伤势
  extractCharacterInjury(name, content) {
    const nameIndex = content.indexOf(name)
    if (nameIndex === -1) return 'none'

    const context = content.substring(Math.max(0, nameIndex - 50), nameIndex + 100)

    if (context.includes('受伤') || context.includes('伤势')) return 'injured'
    if (context.includes('重伤') || context.includes('严重')) return 'seriously_injured'
    if (context.includes('轻伤') || context.includes('小伤')) return 'lightly_injured'

    return 'none'
  }

  // 提取角色实力
  extractCharacterPower(name, content) {
    const nameIndex = content.indexOf(name)
    if (nameIndex === -1) return 'unknown'

    const context = content.substring(Math.max(0, nameIndex - 100), nameIndex + 100)

    // 修炼等级
    const levels = ['炼气', '筑基', '金丹', '元婴', '化神']
    for (const level of levels) {
      if (context.includes(level)) {
        return level
      }
    }

    return 'unknown'
  }

  // 提取角色关系
  extractCharacterRelationships(content) {
    const relationships = []
    const characters = Array.from(this.characters.keys())

    // 简单的关系检测
    for (let i = 0; i < characters.length; i++) {
      for (let j = i + 1; j < characters.length; j++) {
        const char1 = characters[i]
        const char2 = characters[j]

        if (content.includes(char1) && content.includes(char2)) {
          const relationship = this.analyzeRelationship(char1, char2, content)
          if (relationship) {
            relationships.push({
              character1: char1,
              character2: char2,
              relationship: relationship
            })
          }
        }
      }
    }

    return relationships
  }

  // 分析角色关系
  analyzeRelationship(char1, char2, content) {
    if (content.includes(`${char1}和${char2}`) || content.includes(`${char2}和${char1}`)) {
      if (content.includes('朋友') || content.includes('伙伴')) return 'friend'
      if (content.includes('敌人') || content.includes('对手')) return 'enemy'
      if (content.includes('师父') || content.includes('弟子')) return 'master_student'
      if (content.includes('恋人') || content.includes('喜欢')) return 'romantic'
      return 'acquaintance'
    }
    return null
  }

  // 提取对话风格
  extractDialogueStyles(content) {
    const styles = new Map()

    for (const [name] of this.characters) {
      if (content.includes(name)) {
        const dialogues = this.extractCharacterDialogues(name, content)
        if (dialogues.length > 0) {
          styles.set(name, this.analyzeDialogueStyle(dialogues))
        }
      }
    }

    return Object.fromEntries(styles)
  }

  // 提取角色对话
  extractCharacterDialogues(name, content) {
    const dialogues = []
    const pattern = new RegExp(`${name}[说道]："([^"]+)"`, 'g')
    let match

    while ((match = pattern.exec(content)) !== null) {
      dialogues.push(match[1])
    }

    return dialogues
  }

  // 分析对话风格
  analyzeDialogueStyle(dialogues) {
    const allText = dialogues.join('')

    if (allText.includes('哼') || allText.includes('切')) return 'arrogant'
    if (allText.includes('呵呵') || allText.includes('哈哈')) return 'cheerful'
    if (allText.includes('嗯') || allText.includes('好的')) return 'polite'
    if (allText.includes('什么') || allText.includes('为什么')) return 'curious'

    return 'normal'
  }

  // 提取情节线索
  extractPlotThreads(content) {
    const threads = []

    // 检测常见情节线索
    if (content.includes('任务') || content.includes('委托')) {
      threads.push({ type: 'quest', description: '任务情节' })
    }
    if (content.includes('宝物') || content.includes('秘籍')) {
      threads.push({ type: 'treasure', description: '宝物线索' })
    }
    if (content.includes('仇人') || content.includes('报仇')) {
      threads.push({ type: 'revenge', description: '复仇情节' })
    }
    if (content.includes('比赛') || content.includes('大会')) {
      threads.push({ type: 'competition', description: '竞赛情节' })
    }

    return threads
  }

  // 提取伏笔元素
  extractForeshadowingElements(content) {
    const elements = []

    // 检测可能的伏笔
    if (content.includes('神秘') || content.includes('奇怪')) {
      elements.push({ type: 'mystery', hint: '神秘元素' })
    }
    if (content.includes('预感') || content.includes('感觉')) {
      elements.push({ type: 'premonition', hint: '预感伏笔' })
    }
    if (content.includes('传说') || content.includes('传闻')) {
      elements.push({ type: 'legend', hint: '传说线索' })
    }

    return elements
  }

  // 提取冲突状态
  extractConflictStatus(content) {
    if (content.includes('战斗') || content.includes('打斗')) {
      return { type: 'combat', intensity: 'high', resolved: content.includes('胜利') || content.includes('结束') }
    }
    if (content.includes('争论') || content.includes('争执')) {
      return { type: 'argument', intensity: 'medium', resolved: content.includes('和解') || content.includes('妥协') }
    }
    if (content.includes('矛盾') || content.includes('分歧')) {
      return { type: 'disagreement', intensity: 'low', resolved: false }
    }

    return { type: 'none', intensity: 'none', resolved: true }
  }
}
