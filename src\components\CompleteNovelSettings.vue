<template>
  <div class="ink-settings" v-if="visible">
    <!-- 返回按钮 -->
    <div class="back-header">
      <button class="back-btn" @click="$emit('close')" title="返回">
        <svg viewBox="0 0 24 24" fill="none">
          <path d="M19 12H5M12 19l-7-7 7-7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </button>
    </div>

    <!-- 页面标题 -->
    <div class="page-header elegant-card fade-in-up">
      <div class="header-content">
        <h1 class="page-title gradient-text elegant">文房设置</h1>
        <p class="page-subtitle">配置您的创作环境</p>
      </div>
      <div class="header-decoration floating">
        <div class="decoration-icon">
          <svg viewBox="0 0 24 24" fill="none">
            <path d="M12 15l-2-5h4l-2 5zM12 6v3" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
      </div>
    </div>

    <!-- AI服务配置 -->
    <div class="settings-section elegant-card fade-in-up">
      <div class="section-header">
        <h2 class="section-title gradient-text purple">AI服务配置</h2>
        <p class="section-subtitle">配置AI服务以启用智能创作功能</p>
      </div>
      
      <div class="settings-form">
        <!-- AI服务商选择 -->
        <div class="form-item">
          <label class="form-label">AI服务商</label>
          <div class="select-wrapper">
            <select v-model="aiConfig.provider" @change="onProviderChange" class="form-select">
              <option value="">请选择AI服务提供商</option>
              <option value="gemini">Google Gemini</option>
              <option value="openai">OpenAI</option>
              <option value="claude">Anthropic Claude</option>
              <option value="custom">自定义服务</option>
            </select>
            <div class="select-arrow">
              <svg viewBox="0 0 24 24" fill="none">
                <path d="M6 9l6 6 6-6" stroke="currentColor" stroke-width="2"/>
              </svg>
            </div>
          </div>
        </div>

        <!-- API Key -->
        <div class="form-item" v-if="aiConfig.provider">
          <label class="form-label">API Key</label>
          <div class="api-key-group">
            <div class="api-key-input">
              <input 
                v-model="aiConfig.apiKey" 
                :type="showApiKey ? 'text' : 'password'"
                class="form-input"
                placeholder="请输入您的API Key"
              />
              <button 
                type="button" 
                class="toggle-btn"
                @click="showApiKey = !showApiKey"
                :title="showApiKey ? '隐藏' : '显示'"
              >
                <svg v-if="showApiKey" viewBox="0 0 24 24" fill="none">
                  <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" stroke="currentColor" stroke-width="2"/>
                  <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                </svg>
                <svg v-else viewBox="0 0 24 24" fill="none">
                  <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24" stroke="currentColor" stroke-width="2"/>
                  <line x1="1" y1="1" x2="23" y2="23" stroke="currentColor" stroke-width="2"/>
                </svg>
              </button>
            </div>
            <button type="button" class="help-btn" @click="openApiKeyHelp" title="获取帮助">
              <svg viewBox="0 0 24 24" fill="none">
                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3" stroke="currentColor" stroke-width="2"/>
                <path d="M12 17h.01" stroke="currentColor" stroke-width="2"/>
              </svg>
            </button>
          </div>
        </div>

        <!-- API地址 -->
        <div class="form-item" v-if="aiConfig.provider">
          <label class="form-label">API地址</label>
          <div class="api-url-group">
            <input 
              v-model="aiConfig.apiUrl" 
              type="text" 
              class="form-input"
              :placeholder="getApiUrlPlaceholder()"
            />
            <button type="button" class="use-default-btn" @click="useDefaultUrl">
              使用默认
            </button>
          </div>
          <div class="form-hint">
            <span class="hint-label">默认地址:</span>
            <code class="hint-code">{{ getDefaultApiUrl() }}</code>
          </div>
        </div>

        <!-- 模型选择 -->
        <div class="form-item" v-if="aiConfig.provider">
          <label class="form-label">模型选择</label>
          <div class="model-selection">
            <div class="select-wrapper">
              <select v-model="aiConfig.model" class="form-select">
                <option value="">请选择模型</option>
                <optgroup label="推荐模型">
                  <option 
                    v-for="model in getRecommendedModels()" 
                    :key="model.id" 
                    :value="model.id"
                  >
                    {{ model.name }}
                  </option>
                </optgroup>
              </select>
              <div class="select-arrow">
                <svg viewBox="0 0 24 24" fill="none">
                  <path d="M6 9l6 6 6-6" stroke="currentColor" stroke-width="2"/>
                </svg>
              </div>
            </div>
            <div class="model-actions">
              <button type="button" class="refresh-btn" @click="refreshModels" :disabled="refreshing || !aiConfig.provider">
                <svg viewBox="0 0 24 24" fill="none" :class="{ 'spinning': refreshing }">
                  <path d="M1 4v6h6M23 20v-6h-6" stroke="currentColor" stroke-width="2"/>
                  <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15" stroke="currentColor" stroke-width="2"/>
                </svg>
                {{ refreshing ? '刷新中...' : '刷新模型列表' }}
              </button>
              <button type="button" class="info-btn" @click="showModelInfo">
                <svg viewBox="0 0 24 24" fill="none">
                  <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                  <line x1="12" y1="16" x2="12" y2="12" stroke="currentColor" stroke-width="2"/>
                  <line x1="12" y1="8" x2="12.01" y2="8" stroke="currentColor" stroke-width="2"/>
                </svg>
                模型信息
              </button>
              <div v-if="getRecommendedModels().length > 0" class="model-count">
                共 {{ getRecommendedModels().length }} 个模型
              </div>
            </div>
          </div>
        </div>

        <!-- 连接测试和保存 -->
        <div class="form-actions" v-if="aiConfig.provider">
          <button 
            type="button" 
            class="btn btn-test" 
            @click="testConnection"
            :disabled="!isConfigValid || testing"
          >
            <svg viewBox="0 0 24 24" fill="none" :class="{ 'spinning': testing }">
              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" stroke="currentColor" stroke-width="2"/>
              <polyline points="22,4 12,14.01 9,11.01" stroke="currentColor" stroke-width="2"/>
            </svg>
            {{ testing ? '测试中...' : '测试连接' }}
          </button>
          <button 
            type="button" 
            class="btn btn-save" 
            @click="saveConfig"
            :disabled="!isConfigValid"
          >
            <svg viewBox="0 0 24 24" fill="none">
              <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z" stroke="currentColor" stroke-width="2"/>
              <polyline points="17,21 17,13 7,13 7,21" stroke="currentColor" stroke-width="2"/>
              <polyline points="7,3 7,8 15,8" stroke="currentColor" stroke-width="2"/>
            </svg>
            保存配置
          </button>
        </div>

        <!-- 高级设置 -->
        <div class="advanced-settings" v-if="aiConfig.provider">
          <div class="divider">
            <span>高级设置</span>
          </div>

          <!-- 请求超时 -->
          <div class="form-item">
            <label class="form-label">请求超时</label>
            <div class="number-input-group">
              <button type="button" class="number-btn" @click="adjustTimeout(-5)">−</button>
              <input 
                type="number" 
                v-model="advancedConfig.timeout"
                min="5"
                max="120"
                step="5"
                class="number-input"
              />
              <button type="button" class="number-btn" @click="adjustTimeout(5)">+</button>
              <span class="unit">秒</span>
            </div>
          </div>

          <!-- 最大重试次数 -->
          <div class="form-item">
            <label class="form-label">最大重试次数</label>
            <div class="number-input-group">
              <button type="button" class="number-btn" @click="adjustRetries(-1)">−</button>
              <input 
                type="number" 
                v-model="advancedConfig.maxRetries"
                min="0"
                max="5"
                class="number-input"
              />
              <button type="button" class="number-btn" @click="adjustRetries(1)">+</button>
            </div>
          </div>

          <!-- 温度参数 -->
          <div class="form-item">
            <label class="form-label">温度参数</label>
            <div class="slider-group">
              <button type="button" class="slider-btn" @click="adjustTemperature(-0.1)">−</button>
              <div class="slider-container">
                <input 
                  type="range" 
                  v-model="advancedConfig.temperature"
                  min="0"
                  max="2"
                  step="0.1"
                  class="slider"
                />
                <div class="slider-value">{{ advancedConfig.temperature }}</div>
              </div>
              <button type="button" class="slider-btn" @click="adjustTemperature(0.1)">+</button>
            </div>
            <div class="form-hint">控制AI回复的创造性，0为最保守，2为最创新</div>
          </div>

          <!-- 启用流式输出 -->
          <div class="form-item">
            <label class="form-label">启用流式输出</label>
            <div class="switch-group">
              <label class="switch">
                <input type="checkbox" v-model="advancedConfig.stream" />
                <span class="switch-slider"></span>
              </label>
              <span class="switch-text">实时显示AI生成内容</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑器设置 -->
    <div class="settings-section elegant-card fade-in-up">
      <div class="section-header">
        <h2 class="section-title gradient-text blue">编辑器设置</h2>
        <p class="section-subtitle">个性化您的写作环境</p>
      </div>
      
      <div class="settings-form">
        <!-- 字体大小 -->
        <div class="form-item">
          <label class="form-label">字体大小</label>
          <div class="slider-group">
            <button type="button" class="slider-btn" @click="adjustFontSize(-1)">−</button>
            <div class="slider-container">
              <input 
                type="range" 
                v-model="editorConfig.fontSize"
                min="12"
                max="24"
                class="slider"
              />
              <div class="slider-value">{{ editorConfig.fontSize }}px</div>
            </div>
            <button type="button" class="slider-btn" @click="adjustFontSize(1)">+</button>
          </div>
        </div>

        <!-- 行间距 -->
        <div class="form-item">
          <label class="form-label">行间距</label>
          <div class="slider-group">
            <button type="button" class="slider-btn" @click="adjustLineHeight(-0.1)">−</button>
            <div class="slider-container">
              <input 
                type="range" 
                v-model="editorConfig.lineHeight"
                min="1.2"
                max="2.5"
                step="0.1"
                class="slider"
              />
              <div class="slider-value">{{ editorConfig.lineHeight }}</div>
            </div>
            <button type="button" class="slider-btn" @click="adjustLineHeight(0.1)">+</button>
          </div>
        </div>

        <!-- 开关设置 -->
        <div class="form-item">
          <label class="form-label">自动保存</label>
          <div class="switch-group">
            <label class="switch">
              <input type="checkbox" v-model="editorConfig.autoSave" />
              <span class="switch-slider"></span>
            </label>
            <span class="switch-text">开启后将自动保存您的创作内容</span>
          </div>
        </div>

        <div class="form-item">
          <label class="form-label">夜间模式</label>
          <div class="switch-group">
            <label class="switch">
              <input type="checkbox" v-model="editorConfig.darkMode" />
              <span class="switch-slider"></span>
            </label>
            <span class="switch-text">护眼的深色主题</span>
          </div>
        </div>

        <div class="form-item">
          <label class="form-label">字数统计</label>
          <div class="switch-group">
            <label class="switch">
              <input type="checkbox" v-model="editorConfig.wordCount" />
              <span class="switch-slider"></span>
            </label>
            <span class="switch-text">实时显示字数统计</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 保存所有设置 -->
    <div class="settings-footer">
      <button type="button" class="btn btn-primary" @click="saveAllSettings">
        <svg viewBox="0 0 24 24" fill="none">
          <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z" stroke="currentColor" stroke-width="2"/>
          <polyline points="17,21 17,13 7,13 7,21" stroke="currentColor" stroke-width="2"/>
          <polyline points="7,3 7,8 15,8" stroke="currentColor" stroke-width="2"/>
        </svg>
        保存所有设置
      </button>
      <button type="button" class="btn btn-secondary" @click="resetAllSettings">
        重置默认
      </button>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed } from 'vue'

export default {
  name: 'CompleteNovelSettings',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  emits: ['close', 'settings-changed'],
  setup(props, { emit }) {
    const showApiKey = ref(false)
    const testing = ref(false)
    const refreshing = ref(false)
    
    // AI配置
    const aiConfig = reactive({
      provider: '',
      apiKey: '',
      apiUrl: '',
      model: ''
    })

    // 动态模型列表
    const dynamicModels = ref({})

    // 高级配置
    const advancedConfig = reactive({
      timeout: 30,
      maxRetries: 3,
      temperature: 0.7,
      stream: true
    })

    // 编辑器配置
    const editorConfig = reactive({
      fontSize: 16,
      lineHeight: 1.6,
      autoSave: true,
      darkMode: false,
      wordCount: true
    })

    // AI服务商配置
    const AI_PROVIDERS = {
      gemini: {
        name: 'Google Gemini',
        defaultUrl: 'https://generativelanguage.googleapis.com/v1beta',
        helpUrl: 'https://aistudio.google.com/app/apikey',
        models: [
          { id: 'gemini-2.5-flash', name: 'Gemini 2.5 Flash', description: '最新快速模型' },
          { id: 'gemini-pro', name: 'Gemini Pro', description: '多模态AI模型' }
        ]
      },
      openai: {
        name: 'OpenAI',
        defaultUrl: 'https://api.openai.com/v1',
        helpUrl: 'https://platform.openai.com/api-keys',
        models: [
          { id: 'gpt-4', name: 'GPT-4', description: '最强大的模型' },
          { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', description: '快速且经济' }
        ]
      },
      claude: {
        name: 'Anthropic Claude',
        defaultUrl: 'https://api.anthropic.com/v1',
        helpUrl: 'https://console.anthropic.com/',
        models: [
          { id: 'claude-3-opus', name: 'Claude 3 Opus', description: '最强推理能力' },
          { id: 'claude-3-sonnet', name: 'Claude 3 Sonnet', description: '平衡性能' }
        ]
      },
      custom: {
        name: '自定义',
        defaultUrl: '',
        helpUrl: 'https://github.com/topics/openai-api',
        models: [
          { id: 'custom-model', name: '自定义模型', description: '兼容OpenAI API' }
        ]
      }
    }

    // 计算属性
    const isConfigValid = computed(() => {
      return aiConfig.provider && aiConfig.apiKey && aiConfig.apiUrl
    })

    // 方法
    const getApiUrlPlaceholder = () => {
      const provider = AI_PROVIDERS[aiConfig.provider]
      return provider ? provider.defaultUrl || '输入自定义API地址' : '请先选择AI服务商'
    }

    const getDefaultApiUrl = () => {
      const provider = AI_PROVIDERS[aiConfig.provider]
      return provider ? provider.defaultUrl : ''
    }

    const getRecommendedModels = () => {
      const provider = AI_PROVIDERS[aiConfig.provider]

      // 如果有动态获取的模型，优先使用动态模型
      if (dynamicModels.value[aiConfig.provider]) {
        return dynamicModels.value[aiConfig.provider]
      }

      // 否则使用预定义模型
      return provider ? provider.models : []
    }

    const onProviderChange = () => {
      const provider = AI_PROVIDERS[aiConfig.provider]
      if (provider && provider.defaultUrl) {
        aiConfig.apiUrl = provider.defaultUrl
      }
      aiConfig.model = ''
    }

    const useDefaultUrl = () => {
      const provider = AI_PROVIDERS[aiConfig.provider]
      if (provider && provider.defaultUrl) {
        aiConfig.apiUrl = provider.defaultUrl
      }
    }

    const openApiKeyHelp = () => {
      const provider = AI_PROVIDERS[aiConfig.provider]
      if (provider && provider.helpUrl) {
        window.open(provider.helpUrl, '_blank')
      }
    }

    const refreshModels = async () => {
      if (!aiConfig.provider) {
        alert('请先选择AI服务商')
        return
      }

      refreshing.value = true
      try {
        let models = []

        // 如果有API Key，尝试从API获取模型列表
        if (aiConfig.apiKey) {
          try {
            models = await fetchRealModelList(aiConfig)
            alert(`从API获取到 ${models.length} 个可用模型`)
          } catch (apiError) {
            console.warn('从API获取模型失败，使用预定义列表:', apiError)
            // API获取失败时，使用预定义模型列表
            models = await fetchModelList(aiConfig.provider, aiConfig.apiUrl, aiConfig.apiKey)
            alert(`使用预定义模型列表，共 ${models.length} 个模型`)
          }
        } else {
          // 没有API Key时，直接使用预定义模型列表
          models = await fetchModelList(aiConfig.provider, aiConfig.apiUrl, aiConfig.apiKey)
          alert(`使用预定义模型列表，共 ${models.length} 个模型（未提供API Key）`)
        }

        // 更新动态模型列表
        dynamicModels.value[aiConfig.provider] = models

        // 如果当前选择的模型不在新列表中，自动选择推荐模型
        const modelIds = models.map(m => m.id)
        if (aiConfig.model && !modelIds.includes(aiConfig.model)) {
          const recommended = models.filter(model => model.recommended)
          if (recommended.length > 0) {
            aiConfig.model = recommended[0].id
            alert(`已自动选择推荐模型: ${aiConfig.model}`)
          } else {
            aiConfig.model = ''
            alert('当前选择的模型不可用，请重新选择')
          }
        }

      } catch (error) {
        console.error('刷新模型列表失败:', error)
        alert(`刷新模型列表失败: ${error.message}`)
      } finally {
        refreshing.value = false
      }
    }

    // 真实的模型列表获取函数
    const fetchRealModelList = async (config) => {
      try {
        if (config.provider === 'gemini') {
          // Google Gemini API 获取模型列表
          const url = `${config.apiUrl || 'https://generativelanguage.googleapis.com/v1beta'}/models?key=${config.apiKey}`

          const response = await fetch(url)
          if (!response.ok) {
            throw new Error(`HTTP ${response.status}`)
          }

          const data = await response.json()
          return data.models?.map(model => ({
            id: model.name.replace('models/', ''),
            name: model.displayName || model.name,
            description: model.description || '官方模型',
            recommended: model.name.includes('gemini-2.0-flash') || model.name.includes('gemini-1.5-pro')
          })) || []

        } else if (config.provider === 'openai') {
          // OpenAI API 获取模型列表
          const url = `${config.apiUrl || 'https://api.openai.com/v1'}/models`

          const response = await fetch(url, {
            headers: {
              'Authorization': `Bearer ${config.apiKey}`
            }
          })

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}`)
          }

          const data = await response.json()
          return data.data?.filter(model =>
            model.id.includes('gpt') || model.id.includes('text-davinci')
          ).map(model => ({
            id: model.id,
            name: model.id.toUpperCase(),
            description: '官方模型',
            recommended: model.id.includes('gpt-4') || model.id.includes('gpt-3.5-turbo')
          })) || []

        } else if (config.provider === 'claude') {
          // Claude API 不提供模型列表端点，返回预定义列表
          return [
            { id: 'claude-3-5-sonnet-20241022', name: 'Claude 3.5 Sonnet', description: '最新版本', recommended: true },
            { id: 'claude-3-opus-20240229', name: 'Claude 3 Opus', description: '最强推理能力', recommended: true },
            { id: 'claude-3-sonnet-20240229', name: 'Claude 3 Sonnet', description: '平衡性能', recommended: false },
            { id: 'claude-3-haiku-20240307', name: 'Claude 3 Haiku', description: '快速响应', recommended: false }
          ]

        } else {
          // 自定义API，尝试获取模型列表
          const url = `${config.apiUrl}/models`

          const response = await fetch(url, {
            headers: {
              'Authorization': `Bearer ${config.apiKey}`
            }
          })

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}`)
          }

          const data = await response.json()
          return data.data?.map(model => ({
            id: model.id,
            name: model.id,
            description: '自定义模型',
            recommended: false
          })) || []
        }

      } catch (error) {
        throw new Error(`获取模型列表失败: ${error.message}`)
      }
    }

    // 获取模型列表的函数（预定义列表）
    const fetchModelList = async (provider, apiUrl, apiKey) => {
      // 模拟网络请求
      await new Promise(resolve => setTimeout(resolve, 500))

      switch (provider) {
        case 'gemini':
          return [
            { id: 'gemini-2.0-flash-exp', name: 'Gemini 2.0 Flash', description: '最新快速模型', recommended: true },
            { id: 'gemini-1.5-pro', name: 'Gemini 1.5 Pro', description: '高性能模型', recommended: true },
            { id: 'gemini-1.5-flash', name: 'Gemini 1.5 Flash', description: '快速响应模型', recommended: false },
            { id: 'gemini-pro', name: 'Gemini Pro', description: '多模态AI模型', recommended: false }
          ]

        case 'openai':
          return [
            { id: 'gpt-4o', name: 'GPT-4o', description: '最新多模态模型', recommended: true },
            { id: 'gpt-4o-mini', name: 'GPT-4o Mini', description: '轻量级版本', recommended: true },
            { id: 'gpt-4-turbo', name: 'GPT-4 Turbo', description: '增强版GPT-4', recommended: false },
            { id: 'gpt-4', name: 'GPT-4', description: '最强大的模型', recommended: false },
            { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', description: '快速且经济', recommended: false }
          ]

        case 'claude':
          return [
            { id: 'claude-3-5-sonnet-20241022', name: 'Claude 3.5 Sonnet', description: '最新版本', recommended: true },
            { id: 'claude-3-opus-20240229', name: 'Claude 3 Opus', description: '最强推理能力', recommended: true },
            { id: 'claude-3-sonnet-20240229', name: 'Claude 3 Sonnet', description: '平衡性能', recommended: false },
            { id: 'claude-3-haiku-20240307', name: 'Claude 3 Haiku', description: '快速响应', recommended: false }
          ]

        case 'custom':
          return [
            { id: 'custom-model-1', name: '自定义模型 1', description: '兼容OpenAI API', recommended: true },
            { id: 'custom-model-2', name: '自定义模型 2', description: '本地部署模型', recommended: false },
            { id: 'custom-model-3', name: '自定义模型 3', description: '专用模型', recommended: false }
          ]

        default:
          throw new Error('不支持的服务商')
      }
    }

    const showModelInfo = () => {
      const model = getRecommendedModels().find(m => m.id === aiConfig.model)
      if (model) {
        alert(`模型信息：\n名称：${model.name}\n描述：${model.description}`)
      } else {
        alert('请先选择一个模型')
      }
    }

    const testConnection = async () => {
      if (!aiConfig.provider || !aiConfig.apiKey) {
        alert('请先选择AI服务商并填写API Key')
        return
      }

      testing.value = true
      try {
        // 真实的API测试
        const testResult = await performRealAPITest(aiConfig)

        if (testResult.success) {
          alert(`API连接测试成功！\n\n服务商: ${testResult.provider}\n模型: ${testResult.model}\n响应: ${testResult.response}\n延迟: ${testResult.latency}ms`)
        } else {
          throw new Error(testResult.error)
        }
      } catch (error) {
        console.error('API测试失败:', error)
        alert(`API连接测试失败：\n\n${error.message}\n\n可能原因：\n• API Key 无效或已过期\n• API 地址不正确\n• 网络连接问题\n• 模型名称不正确`)
      } finally {
        testing.value = false
      }
    }

    // 真实的API测试函数
    const performRealAPITest = async (config) => {
      const startTime = Date.now()

      try {
        let response

        if (config.provider === 'gemini') {
          // 测试Google Gemini API
          const url = `${config.apiUrl || 'https://generativelanguage.googleapis.com/v1beta'}/models/${config.model}:generateContent?key=${config.apiKey}`

          response = await fetch(url, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              contents: [{
                parts: [{
                  text: "请回复'测试成功'来确认API连接正常。"
                }]
              }],
              generationConfig: {
                temperature: 0.1,
                maxOutputTokens: 50
              }
            })
          })

          if (!response.ok) {
            const errorData = await response.json()
            throw new Error(errorData.error?.message || `HTTP ${response.status}`)
          }

          const data = await response.json()
          const generatedText = data.candidates?.[0]?.content?.parts?.[0]?.text || '无响应内容'

          return {
            success: true,
            provider: 'Google Gemini',
            model: config.model,
            response: generatedText.substring(0, 100),
            latency: Date.now() - startTime,
            usage: data.usageMetadata
          }

        } else if (config.provider === 'openai') {
          // 测试OpenAI API
          const url = `${config.apiUrl || 'https://api.openai.com/v1'}/chat/completions`

          response = await fetch(url, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${config.apiKey}`
            },
            body: JSON.stringify({
              model: config.model,
              messages: [{
                role: 'user',
                content: "请回复'测试成功'来确认API连接正常。"
              }],
              max_tokens: 50,
              temperature: 0.1
            })
          })

          if (!response.ok) {
            const errorData = await response.json()
            throw new Error(errorData.error?.message || `HTTP ${response.status}`)
          }

          const data = await response.json()
          const generatedText = data.choices?.[0]?.message?.content || '无响应内容'

          return {
            success: true,
            provider: 'OpenAI',
            model: config.model,
            response: generatedText.substring(0, 100),
            latency: Date.now() - startTime,
            usage: data.usage
          }

        } else if (config.provider === 'claude') {
          // 测试Claude API
          const url = `${config.apiUrl || 'https://api.anthropic.com'}/v1/messages`

          response = await fetch(url, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'x-api-key': config.apiKey,
              'anthropic-version': '2023-06-01'
            },
            body: JSON.stringify({
              model: config.model,
              max_tokens: 50,
              messages: [{
                role: 'user',
                content: "请回复'测试成功'来确认API连接正常。"
              }]
            })
          })

          if (!response.ok) {
            const errorData = await response.json()
            throw new Error(errorData.error?.message || `HTTP ${response.status}`)
          }

          const data = await response.json()
          const generatedText = data.content?.[0]?.text || '无响应内容'

          return {
            success: true,
            provider: 'Anthropic Claude',
            model: config.model,
            response: generatedText.substring(0, 100),
            latency: Date.now() - startTime,
            usage: data.usage
          }

        } else {
          // 自定义API测试
          const url = `${config.apiUrl}/chat/completions`

          response = await fetch(url, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${config.apiKey}`
            },
            body: JSON.stringify({
              model: config.model,
              messages: [{
                role: 'user',
                content: "请回复'测试成功'来确认API连接正常。"
              }],
              max_tokens: 50,
              temperature: 0.1
            })
          })

          if (!response.ok) {
            const errorData = await response.json()
            throw new Error(errorData.error?.message || `HTTP ${response.status}`)
          }

          const data = await response.json()
          const generatedText = data.choices?.[0]?.message?.content || '无响应内容'

          return {
            success: true,
            provider: '自定义API',
            model: config.model,
            response: generatedText.substring(0, 100),
            latency: Date.now() - startTime,
            usage: data.usage
          }
        }

      } catch (error) {
        return {
          success: false,
          error: error.message
        }
      }
    }

    const saveConfig = () => {
      // 保存完整的配置到localStorage
      const settings = {
        ai: { ...aiConfig },
        advanced: { ...advancedConfig },
        editor: { ...editorConfig }
      }

      localStorage.setItem('complete-novel-settings', JSON.stringify(settings))
      alert('配置已保存！')
    }

    // 数值调整方法
    const adjustTimeout = (delta) => {
      const newValue = advancedConfig.timeout + delta
      if (newValue >= 5 && newValue <= 120) {
        advancedConfig.timeout = newValue
      }
    }

    const adjustRetries = (delta) => {
      const newValue = advancedConfig.maxRetries + delta
      if (newValue >= 0 && newValue <= 5) {
        advancedConfig.maxRetries = newValue
      }
    }

    const adjustTemperature = (delta) => {
      const newValue = Math.round((advancedConfig.temperature + delta) * 10) / 10
      if (newValue >= 0 && newValue <= 2) {
        advancedConfig.temperature = newValue
      }
    }

    const adjustFontSize = (delta) => {
      const newValue = editorConfig.fontSize + delta
      if (newValue >= 12 && newValue <= 24) {
        editorConfig.fontSize = newValue
      }
    }

    const adjustLineHeight = (delta) => {
      const newValue = Math.round((editorConfig.lineHeight + delta) * 10) / 10
      if (newValue >= 1.2 && newValue <= 2.5) {
        editorConfig.lineHeight = newValue
      }
    }

    const saveAllSettings = () => {
      const allSettings = {
        ai: aiConfig,
        advanced: advancedConfig,
        editor: editorConfig
      }
      localStorage.setItem('complete-novel-settings', JSON.stringify(allSettings))
      emit('settings-changed', allSettings)
      alert('所有设置已保存')
    }

    const resetAllSettings = () => {
      if (confirm('确定要重置所有设置吗？')) {
        Object.assign(aiConfig, {
          provider: '',
          apiKey: '',
          apiUrl: '',
          model: ''
        })
        Object.assign(advancedConfig, {
          timeout: 30,
          maxRetries: 3,
          temperature: 0.7,
          stream: true
        })
        Object.assign(editorConfig, {
          fontSize: 16,
          lineHeight: 1.6,
          autoSave: true,
          darkMode: false,
          wordCount: true
        })
      }
    }

    // 加载设置
    const loadSettings = () => {
      const saved = localStorage.getItem('complete-novel-settings')
      if (saved) {
        try {
          const parsedSettings = JSON.parse(saved)
          if (parsedSettings.ai) Object.assign(aiConfig, parsedSettings.ai)
          if (parsedSettings.advanced) Object.assign(advancedConfig, parsedSettings.advanced)
          if (parsedSettings.editor) Object.assign(editorConfig, parsedSettings.editor)
        } catch (error) {
          console.error('加载设置失败:', error)
        }
      }
    }

    // 初始化
    loadSettings()

    return {
      showApiKey,
      testing,
      refreshing,
      aiConfig,
      advancedConfig,
      editorConfig,
      dynamicModels,
      isConfigValid,
      getApiUrlPlaceholder,
      getDefaultApiUrl,
      getRecommendedModels,
      onProviderChange,
      useDefaultUrl,
      openApiKeyHelp,
      refreshModels,
      showModelInfo,
      testConnection,
      saveConfig,
      adjustTimeout,
      adjustRetries,
      adjustTemperature,
      adjustFontSize,
      adjustLineHeight,
      saveAllSettings,
      resetAllSettings
    }
  }
}
</script>

<style scoped>
.ink-settings {
  height: 100%;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  overflow-y: auto;
  padding: 24px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.back-header {
  margin-bottom: 20px;
}

.back-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.back-btn:hover {
  background: rgba(74, 144, 226, 0.1);
  color: #4a90e2;
  transform: translateX(-2px);
}

.back-btn svg {
  width: 20px;
  height: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding: 32px 40px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.3);
  max-width: 900px;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 32px;
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: 36px;
  font-weight: 800;
  margin: 0 0 12px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.5px;
}

.page-subtitle {
  font-size: 18px;
  color: #64748b;
  margin: 0;
  font-weight: 400;
  opacity: 0.8;
}

.header-decoration {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.settings-section {
  margin-bottom: 32px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 36px 40px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.3);
  max-width: 900px;
  margin-left: auto;
  margin-right: auto;
  transition: all 0.3s ease;
}

.settings-section:hover {
  transform: translateY(-2px);
  box-shadow: 0 15px 50px rgba(0, 0, 0, 0.12);
}

.section-header {
  margin-bottom: 32px;
  text-align: center;
  position: relative;
}

.section-header::after {
  content: '';
  position: absolute;
  bottom: -16px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, transparent, #667eea, transparent);
  border-radius: 2px;
}

.section-title {
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 12px 0;
  letter-spacing: -0.3px;
}

.section-title.purple {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-title.blue {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-subtitle {
  font-size: 16px;
  color: #64748b;
  margin: 0;
  font-weight: 400;
  opacity: 0.8;
}

.settings-form {
  max-width: 700px;
  margin: 0 auto;
}

.form-item {
  margin-bottom: 28px;
  position: relative;
}

.form-label {
  display: block;
  margin-bottom: 10px;
  font-size: 15px;
  font-weight: 600;
  color: #1e293b;
  letter-spacing: -0.1px;
}

.select-wrapper {
  position: relative;
}

.form-select {
  width: 100%;
  padding: 14px 44px 14px 18px;
  border: 2px solid rgba(148, 163, 184, 0.2);
  border-radius: 14px;
  background: rgba(255, 255, 255, 0.9);
  font-size: 15px;
  appearance: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 500;
  color: #1e293b;
}

.form-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.12);
  background: rgba(255, 255, 255, 1);
  transform: translateY(-1px);
}

.form-select:hover {
  border-color: rgba(102, 126, 234, 0.3);
}

.select-arrow {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  color: #666;
}

.select-arrow svg {
  width: 16px;
  height: 16px;
}

.form-input {
  width: 100%;
  padding: 14px 18px;
  border: 2px solid rgba(148, 163, 184, 0.2);
  border-radius: 14px;
  background: rgba(255, 255, 255, 0.9);
  font-size: 15px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 500;
  color: #1e293b;
}

.form-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.12);
  background: rgba(255, 255, 255, 1);
  transform: translateY(-1px);
}

.form-input:hover {
  border-color: rgba(102, 126, 234, 0.3);
}

.form-input::placeholder {
  color: #94a3b8;
  font-weight: 400;
}

.api-key-group {
  display: flex;
  gap: 8px;
  align-items: center;
}

.api-key-input {
  flex: 1;
  display: flex;
  align-items: center;
  position: relative;
}

.api-key-input .form-input {
  padding-right: 48px;
}

.toggle-btn {
  position: absolute;
  right: 12px;
  background: none;
  border: none;
  cursor: pointer;
  color: #666;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.toggle-btn:hover {
  background: rgba(0, 0, 0, 0.05);
  color: #333;
}

.toggle-btn svg {
  width: 16px;
  height: 16px;
}

.help-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(102, 126, 234, 0.1);
  border: none;
  border-radius: 8px;
  cursor: pointer;
  color: #667eea;
  transition: all 0.2s ease;
}

.help-btn:hover {
  background: rgba(102, 126, 234, 0.2);
}

.help-btn svg {
  width: 16px;
  height: 16px;
}

.api-url-group {
  display: flex;
  gap: 8px;
  align-items: center;
}

.api-url-group .form-input {
  flex: 1;
}

.use-default-btn {
  padding: 12px 16px;
  background: rgba(102, 126, 234, 0.1);
  border: none;
  border-radius: 8px;
  color: #667eea;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.use-default-btn:hover {
  background: rgba(102, 126, 234, 0.2);
}

.form-hint {
  margin-top: 6px;
  font-size: 12px;
  color: #666;
  display: flex;
  align-items: center;
  gap: 8px;
}

.hint-label {
  font-weight: 500;
}

.hint-code {
  background: rgba(0, 0, 0, 0.05);
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 11px;
}

.model-selection {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.model-actions {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
}

.refresh-btn,
.info-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: rgba(102, 126, 234, 0.1);
  border: none;
  border-radius: 8px;
  color: #667eea;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.refresh-btn:hover,
.info-btn:hover {
  background: rgba(102, 126, 234, 0.2);
}

.refresh-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.refresh-btn svg,
.info-btn svg {
  width: 14px;
  height: 14px;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.model-count {
  font-size: 12px;
  color: #666;
  background: rgba(102, 126, 234, 0.1);
  padding: 4px 8px;
  border-radius: 12px;
  margin-left: auto;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-top: 24px;
}

.btn {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 14px 28px;
  border: none;
  border-radius: 14px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  letter-spacing: -0.1px;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn svg {
  width: 16px;
  height: 16px;
}

.btn-test {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  box-shadow: 0 6px 20px rgba(79, 172, 254, 0.25);
}

.btn-test:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(79, 172, 254, 0.35);
}

.btn-test:active {
  transform: translateY(-1px);
}

.btn-test:disabled {
  background: linear-gradient(135deg, #cbd5e1 0%, #94a3b8 100%);
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 2px 8px rgba(148, 163, 184, 0.2);
}

.btn-save {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.25);
}

.btn-save:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(16, 185, 129, 0.35);
}

.btn-save:active {
  transform: translateY(-1px);
}

.btn-save:disabled {
  background: linear-gradient(135deg, #cbd5e1 0%, #94a3b8 100%);
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 2px 8px rgba(148, 163, 184, 0.2);
}

.divider {
  text-align: center;
  margin: 30px 0 25px 0;
  position: relative;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, #ddd, transparent);
}

.divider span {
  background: white;
  padding: 0 20px;
  color: #666;
  font-size: 14px;
  font-weight: 500;
}

.advanced-settings {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.06) 0%, rgba(116, 75, 162, 0.04) 100%);
  border: 1px solid rgba(102, 126, 234, 0.1);
  border-radius: 20px;
  padding: 28px;
  margin-top: 28px;
  position: relative;
  overflow: hidden;
}

.advanced-settings::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 20px 20px 0 0;
}

.number-input-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.number-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: rgba(102, 126, 234, 0.1);
  border: none;
  border-radius: 6px;
  color: #667eea;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s ease;
}

.number-btn:hover {
  background: rgba(102, 126, 234, 0.2);
}

.number-input {
  width: 80px;
  padding: 8px 12px;
  border: 2px solid rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.8);
  text-align: center;
  font-size: 14px;
}

.number-input:focus {
  outline: none;
  border-color: #667eea;
}

.unit {
  font-size: 12px;
  color: #666;
  margin-left: 4px;
}

.slider-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.slider-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: rgba(102, 126, 234, 0.1);
  border: none;
  border-radius: 6px;
  color: #667eea;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s ease;
}

.slider-btn:hover {
  background: rgba(102, 126, 234, 0.2);
}

.slider-container {
  flex: 1;
  position: relative;
}

.slider {
  width: 100%;
  height: 8px;
  border-radius: 4px;
  background: linear-gradient(90deg, #e2e8f0, #cbd5e1);
  outline: none;
  -webkit-appearance: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.slider:hover {
  background: linear-gradient(90deg, #cbd5e1, #94a3b8);
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
}

.slider::-moz-range-thumb {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  cursor: pointer;
  border: none;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.slider::-moz-range-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
}

.slider-value {
  position: absolute;
  top: -36px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  color: white;
  padding: 6px 12px;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 600;
  white-space: nowrap;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.slider-value::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 4px solid transparent;
  border-top-color: #1e293b;
}

.switch-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.switch {
  position: relative;
  display: inline-block;
  width: 56px;
  height: 28px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.switch-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 28px;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.switch-slider:before {
  position: absolute;
  content: "";
  height: 22px;
  width: 22px;
  left: 3px;
  bottom: 3px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 50%;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.switch input:checked + .switch-slider {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: inset 0 2px 4px rgba(102, 126, 234, 0.2);
}

.switch input:checked + .switch-slider:before {
  transform: translateX(28px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.switch:hover .switch-slider:before {
  transform: scale(1.05);
}

.switch input:checked:hover + .switch-slider:before {
  transform: translateX(28px) scale(1.05);
}

.switch-text {
  font-size: 14px;
  color: #666;
}

.settings-footer {
  text-align: center;
  margin-top: 48px;
  padding: 32px 40px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.3);
  max-width: 900px;
  margin-left: auto;
  margin-right: auto;
  margin-top: 48px;
  position: relative;
}

.settings-footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #667eea, #764ba2, #4facfe, #00f2fe);
  background-size: 200% 100%;
  animation: shimmer 3s ease-in-out infinite;
  border-radius: 24px 24px 0 0;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.25);
  margin-right: 16px;
  font-size: 16px;
  padding: 16px 32px;
}

.btn-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.35);
}

.btn-primary:active {
  transform: translateY(-1px);
}

.btn-secondary {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  color: #64748b;
  border: 1px solid rgba(148, 163, 184, 0.2);
  font-size: 16px;
  padding: 16px 32px;
}

.btn-secondary:hover {
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
  color: #475569;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(148, 163, 184, 0.2);
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.elegant-card {
  position: relative;
  overflow: hidden;
}

.elegant-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #667eea, #764ba2, #4facfe, #00f2fe);
  background-size: 200% 100%;
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { background-position: 200% 0; }
  50% { background-position: -200% 0; }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ink-settings {
    padding: 16px;
  }

  .page-header,
  .settings-section,
  .settings-footer {
    padding: 24px 20px;
    margin-bottom: 20px;
  }

  .page-title {
    font-size: 28px;
  }

  .section-title {
    font-size: 24px;
  }

  .settings-form {
    max-width: 100%;
  }

  .form-actions {
    flex-direction: column;
    gap: 12px;
  }

  .btn {
    width: 100%;
    justify-content: center;
  }

  .model-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .refresh-btn,
  .info-btn {
    width: 100%;
    justify-content: center;
  }

  .api-key-group,
  .api-url-group {
    flex-direction: column;
    gap: 12px;
  }

  .help-btn,
  .use-default-btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .page-header {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }

  .header-decoration {
    order: -1;
  }

  .slider-group,
  .number-input-group {
    flex-direction: column;
    gap: 12px;
  }

  .slider-container {
    order: -1;
  }
}
</style>
