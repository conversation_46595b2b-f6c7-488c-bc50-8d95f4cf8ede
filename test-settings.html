<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设置面板测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .test-button {
            background: #4a90e2;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.2s ease;
        }
        
        .test-button:hover {
            background: #357abd;
            transform: translateY(-1px);
        }
        
        .settings-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .settings-panel {
            width: 90%;
            max-width: 900px;
            height: 80%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .settings-header {
            padding: 20px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .settings-title {
            font-size: 24px;
            font-weight: 600;
            margin: 0;
        }
        
        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            padding: 8px;
            border-radius: 8px;
            transition: background 0.2s ease;
        }
        
        .close-btn:hover {
            background: rgba(0, 0, 0, 0.1);
        }
        
        .settings-content {
            flex: 1;
            display: flex;
            overflow: hidden;
        }
        
        .settings-nav {
            width: 200px;
            background: rgba(0, 0, 0, 0.02);
            padding: 20px;
            border-right: 1px solid rgba(0, 0, 0, 0.1);
        }
        
        .nav-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            border-radius: 8px;
            cursor: pointer;
            margin-bottom: 8px;
            transition: all 0.2s ease;
        }
        
        .nav-item:hover {
            background: rgba(74, 144, 226, 0.1);
        }
        
        .nav-item.active {
            background: rgba(74, 144, 226, 0.2);
            color: #4a90e2;
        }
        
        .settings-main {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>设置面板测试</h1>
        <p>这是一个简单的测试页面，用于验证设置面板的基本功能。</p>
        
        <button class="test-button" onclick="showSettings()">打开设置面板</button>
        <button class="test-button" onclick="testComponents()">测试组件</button>
    </div>
    
    <!-- 设置面板 -->
    <div id="settingsOverlay" class="settings-overlay hidden">
        <div class="settings-panel">
            <div class="settings-header">
                <h1 class="settings-title">文房设置</h1>
                <button class="close-btn" onclick="hideSettings()">×</button>
            </div>
            
            <div class="settings-content">
                <div class="settings-nav">
                    <div class="nav-item active" onclick="switchSection('ai')">
                        🤖 AI配置
                    </div>
                    <div class="nav-item" onclick="switchSection('editor')">
                        📝 编辑器
                    </div>
                    <div class="nav-item" onclick="switchSection('theme')">
                        🎨 主题
                    </div>
                    <div class="nav-item" onclick="switchSection('export')">
                        📥 导出
                    </div>
                    <div class="nav-item" onclick="switchSection('about')">
                        ℹ️ 关于
                    </div>
                </div>
                
                <div class="settings-main">
                    <div id="ai-section">
                        <h2>AI配置</h2>
                        <p>配置AI服务提供商和相关参数。</p>
                        <div style="background: rgba(74, 144, 226, 0.1); padding: 16px; border-radius: 8px; margin: 16px 0;">
                            <h3>服务商选择</h3>
                            <label><input type="radio" name="provider" value="gemini" checked> Google Gemini</label><br>
                            <label><input type="radio" name="provider" value="openai"> OpenAI</label><br>
                            <label><input type="radio" name="provider" value="claude"> Claude</label>
                        </div>
                    </div>
                    
                    <div id="editor-section" class="hidden">
                        <h2>编辑器设置</h2>
                        <p>配置编辑器的外观和行为。</p>
                        <div style="background: rgba(52, 199, 89, 0.1); padding: 16px; border-radius: 8px; margin: 16px 0;">
                            <h3>字体设置</h3>
                            <label>字体大小: <input type="range" min="12" max="24" value="16"></label><br>
                            <label>行间距: <input type="range" min="1" max="3" step="0.1" value="1.5"></label>
                        </div>
                    </div>
                    
                    <div id="theme-section" class="hidden">
                        <h2>主题设置</h2>
                        <p>个性化您的界面主题。</p>
                        <div style="background: rgba(255, 149, 0, 0.1); padding: 16px; border-radius: 8px; margin: 16px 0;">
                            <h3>主题模式</h3>
                            <label><input type="radio" name="theme" value="light" checked> 日间模式</label><br>
                            <label><input type="radio" name="theme" value="dark"> 夜间模式</label><br>
                            <label><input type="radio" name="theme" value="auto"> 自动切换</label>
                        </div>
                    </div>
                    
                    <div id="export-section" class="hidden">
                        <h2>导出设置</h2>
                        <p>配置文档导出选项。</p>
                        <div style="background: rgba(255, 59, 48, 0.1); padding: 16px; border-radius: 8px; margin: 16px 0;">
                            <h3>导出格式</h3>
                            <label><input type="checkbox" checked> 包含章节号</label><br>
                            <label><input type="checkbox" checked> 包含目录</label><br>
                            <label><input type="checkbox"> 包含元数据</label>
                        </div>
                    </div>
                    
                    <div id="about-section" class="hidden">
                        <h2>关于应用</h2>
                        <p>应用信息与帮助。</p>
                        <div style="background: rgba(88, 86, 214, 0.1); padding: 16px; border-radius: 8px; margin: 16px 0;">
                            <h3>创作工坊</h3>
                            <p>版本: 1.0.0</p>
                            <p>专业的AI辅助创作平台</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function showSettings() {
            document.getElementById('settingsOverlay').classList.remove('hidden');
        }
        
        function hideSettings() {
            document.getElementById('settingsOverlay').classList.add('hidden');
        }
        
        function switchSection(section) {
            // 隐藏所有section
            const sections = ['ai', 'editor', 'theme', 'export', 'about'];
            sections.forEach(s => {
                document.getElementById(s + '-section').classList.add('hidden');
            });
            
            // 显示选中的section
            document.getElementById(section + '-section').classList.remove('hidden');
            
            // 更新导航状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');
        }
        
        function testComponents() {
            alert('组件测试功能：\n\n✅ 设置面板布局\n✅ 导航切换\n✅ 响应式设计\n✅ 毛玻璃效果\n\n所有基础功能正常！');
        }
        
        // 点击遮罩关闭
        document.getElementById('settingsOverlay').addEventListener('click', function(e) {
            if (e.target === this) {
                hideSettings();
            }
        });
        
        // ESC键关闭
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                hideSettings();
            }
        });
    </script>
</body>
</html>
