const { spawn } = require('child_process')
const http = require('http')
const path = require('path')

console.log('🚀 启动AI小说创作助手...')

// 检查Vite服务器是否已经运行
async function checkViteServer() {
  return new Promise((resolve) => {
    const req = http.get('http://localhost:3000', (res) => {
      resolve(true)
    })
    req.on('error', () => {
      resolve(false)
    })
    req.setTimeout(1000, () => {
      req.destroy()
      resolve(false)
    })
  })
}

// 等待Vite服务器启动
async function waitForViteServer(maxAttempts = 30) {
  for (let i = 0; i < maxAttempts; i++) {
    if (await checkViteServer()) {
      return true
    }
    console.log(`⏳ 等待Vite服务器启动... (${i + 1}/${maxAttempts})`)
    await new Promise(resolve => setTimeout(resolve, 1000))
  }
  return false
}

// 启动Vite开发服务器
function startViteServer() {
  return new Promise((resolve, reject) => {
    console.log('📦 启动Vite开发服务器...')
    
    const viteProcess = spawn('npm', ['run', 'dev:renderer'], {
      cwd: __dirname,
      stdio: 'pipe',
      shell: true
    })
    
    let serverStarted = false
    
    viteProcess.stdout.on('data', (data) => {
      const output = data.toString()
      console.log('Vite:', output.trim())
      
      // 检测服务器启动成功的标志
      if (output.includes('Local:') && output.includes('3000') && !serverStarted) {
        serverStarted = true
        console.log('✅ Vite服务器启动成功!')
        resolve(viteProcess)
      }
    })
    
    viteProcess.stderr.on('data', (data) => {
      console.error('Vite Error:', data.toString())
    })
    
    viteProcess.on('error', (error) => {
      console.error('启动Vite服务器失败:', error)
      reject(error)
    })
    
    // 超时处理
    setTimeout(() => {
      if (!serverStarted) {
        console.log('⚠️ Vite服务器启动超时，但继续尝试启动Electron...')
        resolve(viteProcess)
      }
    }, 15000)
  })
}

// 启动Electron应用
function startElectron() {
  console.log('🖥️ 启动Electron应用...')
  
  const electronProcess = spawn('npm', ['run', 'dev:main'], {
    cwd: __dirname,
    stdio: 'inherit',
    shell: true
  })
  
  electronProcess.on('error', (error) => {
    console.error('启动Electron失败:', error)
  })
  
  return electronProcess
}

// 主启动流程
async function main() {
  try {
    // 检查Vite服务器是否已经运行
    const isViteRunning = await checkViteServer()
    
    let viteProcess
    if (!isViteRunning) {
      // 启动Vite服务器
      viteProcess = await startViteServer()
      
      // 等待服务器完全就绪
      console.log('⏳ 等待Vite服务器完全就绪...')
      await waitForViteServer()
    } else {
      console.log('✅ Vite服务器已在运行')
    }
    
    // 启动Electron
    const electronProcess = startElectron()
    
    // 处理进程退出
    process.on('SIGINT', () => {
      console.log('\n🛑 正在关闭应用...')
      if (viteProcess) {
        viteProcess.kill()
      }
      electronProcess.kill()
      process.exit(0)
    })
    
    process.on('SIGTERM', () => {
      console.log('\n🛑 正在关闭应用...')
      if (viteProcess) {
        viteProcess.kill()
      }
      electronProcess.kill()
      process.exit(0)
    })
    
  } catch (error) {
    console.error('❌ 启动失败:', error)
    process.exit(1)
  }
}

// 启动应用
main()
