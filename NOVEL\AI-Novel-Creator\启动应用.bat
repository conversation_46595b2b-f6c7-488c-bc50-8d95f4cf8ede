@echo off
chcp 65001 >nul
title AI小说创作助手 - 启动器

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    AI小说创作助手                            ║
echo ║                   墨韵文轩 · 智能创作平台                    ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo [1/3] 正在启动Vite开发服务器...
start /B npm run dev:renderer

echo [2/3] 等待服务器启动完成...
timeout /t 6 /nobreak >nul

echo [3/3] 启动Electron桌面应用...
start /B npm run dev:main

echo.
echo ✅ 应用启动完成！
echo.
echo 📌 使用说明：
echo    • Electron桌面应用窗口应该会自动打开
echo    • 如果窗口显示空白，请在窗口中按 F12 打开开发者工具
echo    • 在Console中执行跳转代码（详见说明文档）
echo    • 或者直接在浏览器中访问: http://localhost:3000
echo.
echo 🌐 浏览器版本: http://localhost:3000
echo 🖥️  桌面版本: 查看Electron窗口
echo.
echo 按任意键退出启动器...
pause >nul
