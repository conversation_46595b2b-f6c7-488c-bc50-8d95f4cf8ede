// AI API 代理服务
const { ipcMain } = require('electron')
const https = require('https')
const http = require('http')

class AIProxy {
  constructor() {
    this.setupIPC()
  }

  // 网络连接诊断
  async diagnoseConnection(hostname) {
    const net = require('net')

    return new Promise((resolve) => {
      const socket = new net.Socket()
      const timeout = 5000

      socket.setTimeout(timeout)

      socket.on('connect', () => {
        socket.destroy()
        resolve({ success: true, message: `成功连接到 ${hostname}:443` })
      })

      socket.on('timeout', () => {
        socket.destroy()
        resolve({ success: false, message: `连接 ${hostname}:443 超时` })
      })

      socket.on('error', (error) => {
        socket.destroy()
        resolve({ success: false, message: `连接 ${hostname}:443 失败: ${error.message}` })
      })

      socket.connect(443, hostname)
    })
  }

  setupIPC() {
    // 处理AI API请求
    ipcMain.handle('ai-api-request', async (event, requestData) => {
      try {
        // 先进行网络诊断
        const urlObj = new URL(requestData.url)
        const diagnosis = await this.diagnoseConnection(urlObj.hostname)

        if (!diagnosis.success) {
          console.error('网络诊断失败:', diagnosis.message)
          throw new Error(`网络连接问题: ${diagnosis.message}`)
        }

        console.log('网络诊断成功:', diagnosis.message)
        return await this.makeAPIRequest(requestData)
      } catch (error) {
        console.error('AI API请求失败:', error)
        throw error
      }
    })

    // 处理网络诊断请求
    ipcMain.handle('network-diagnose', async (event, hostname) => {
      try {
        return await this.diagnoseConnection(hostname)
      } catch (error) {
        console.error('网络诊断失败:', error)
        return { success: false, message: error.message }
      }
    })
  }

  async makeAPIRequest(requestData, retryCount = 0) {
    const { url, method, headers, body } = requestData
    const maxRetries = 3
    const timeoutMs = 30000 // 30秒超时

    return new Promise((resolve, reject) => {
      const urlObj = new URL(url)
      const isHttps = urlObj.protocol === 'https:'
      const httpModule = isHttps ? https : http

      const options = {
        hostname: urlObj.hostname,
        port: urlObj.port || (isHttps ? 443 : 80),
        path: urlObj.pathname + urlObj.search,
        method: method || 'POST',
        timeout: timeoutMs,
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'AI-Novel-Creator/1.0.0',
          ...headers
        }
      }

      console.log(`尝试连接到 ${urlObj.hostname}:${options.port} (第${retryCount + 1}次)`)

      const req = httpModule.request(options, (res) => {
        let data = ''

        res.on('data', (chunk) => {
          data += chunk
        })

        res.on('end', () => {
          try {
            console.log(`请求成功，状态码: ${res.statusCode}`)
            const response = {
              status: res.statusCode,
              statusText: res.statusMessage,
              data: data ? JSON.parse(data) : {}
            }
            resolve(response)
          } catch (error) {
            console.error('解析响应失败:', error)
            reject(new Error(`解析响应失败: ${error.message}`))
          }
        })
      })

      req.on('error', async (error) => {
        console.error(`请求失败 (第${retryCount + 1}次):`, error.message)

        // 如果是超时或连接错误，且还有重试次数，则重试
        if (retryCount < maxRetries && (
          error.code === 'ETIMEDOUT' ||
          error.code === 'ECONNRESET' ||
          error.code === 'ENOTFOUND' ||
          error.code === 'ECONNREFUSED'
        )) {
          console.log(`等待 ${(retryCount + 1) * 2} 秒后重试...`)
          setTimeout(async () => {
            try {
              const result = await this.makeAPIRequest(requestData, retryCount + 1)
              resolve(result)
            } catch (retryError) {
              reject(retryError)
            }
          }, (retryCount + 1) * 2000) // 递增延迟
        } else {
          reject(new Error(`请求失败: ${error.message}`))
        }
      })

      req.on('timeout', () => {
        console.error('请求超时')
        req.destroy()
      })

      if (body) {
        req.write(JSON.stringify(body))
      }

      req.end()
    })
  }
}

module.exports = AIProxy
