// Electron兼容的入口文件
// 使用动态导入来解决模块解析问题

async function initApp() {
  try {
    // 动态导入Vue和其他依赖
    const { createApp } = await import('vue')
    const ElementPlus = await import('element-plus')
    const ElementPlusIcons = await import('@element-plus/icons-vue')
    
    // 导入样式
    await import('element-plus/dist/index.css')
    await import('./assets/styles/modern-theme.css')
    
    // 动态导入路由和App组件
    const { default: router } = await import('./router/index.js')
    const { default: App } = await import('./App.vue')
    
    // 创建Vue应用
    const app = createApp(App)
    
    // 注册所有图标
    for (const [key, component] of Object.entries(ElementPlusIcons)) {
      app.component(key, component)
    }
    
    // 使用插件
    app.use(router)
    app.use(ElementPlus.default || ElementPlus)
    
    // 挂载应用
    app.mount('#app')
    
    console.log('Vue应用初始化成功')
  } catch (error) {
    console.error('Vue应用初始化失败:', error)
    
    // 如果动态导入失败，尝试使用全局变量
    if (window.Vue) {
      console.log('尝试使用全局Vue变量')
      const app = window.Vue.createApp({
        template: `
          <div style="padding: 40px; text-align: center; font-family: Arial, sans-serif;">
            <h1 style="color: #e67e22;">AI小说创作助手</h1>
            <p style="color: #666; margin: 20px 0;">正在加载应用组件...</p>
            <div style="margin: 20px 0;">
              <div style="display: inline-block; width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #e67e22; border-radius: 50%; animation: spin 1s linear infinite;"></div>
            </div>
            <style>
              @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
              }
            </style>
          </div>
        `
      })
      app.mount('#app')
    } else {
      // 最后的备用方案
      document.getElementById('app').innerHTML = `
        <div style="padding: 40px; text-align: center; font-family: Arial, sans-serif;">
          <h1 style="color: #e74c3c;">加载失败</h1>
          <p style="color: #666;">Vue模块加载失败，请检查网络连接或刷新页面重试。</p>
          <button onclick="location.reload()" style="padding: 10px 20px; background: #e67e22; color: white; border: none; border-radius: 5px; cursor: pointer; margin-top: 20px;">
            刷新页面
          </button>
        </div>
      `
    }
  }
}

// 页面加载完成后初始化应用
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initApp)
} else {
  initApp()
}
