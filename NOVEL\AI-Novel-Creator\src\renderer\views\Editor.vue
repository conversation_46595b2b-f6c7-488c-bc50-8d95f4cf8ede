<template>
  <div class="editor">
    <div class="editor-container">
      <!-- 工具栏 -->
      <div class="toolbar">
        <div class="toolbar-left">
          <el-button-group>
            <el-button size="small" @click="saveContent">
              <el-icon><DocumentCopy /></el-icon>
              保存
            </el-button>
            <el-button size="small" @click="exportContent">
              <el-icon><Download /></el-icon>
              导出
            </el-button>
          </el-button-group>
          
          <el-divider direction="vertical" />
          
          <el-button-group>
            <el-button size="small" @click="undo">
              <el-icon><RefreshLeft /></el-icon>
              撤销
            </el-button>
            <el-button size="small" @click="redo">
              <el-icon><RefreshRight /></el-icon>
              重做
            </el-button>
          </el-button-group>
        </div>
        
        <div class="toolbar-right">
          <span class="word-count">字数: {{ wordCount }}</span>
          <el-divider direction="vertical" />
          <el-button size="small" @click="showAssistPanel = !showAssistPanel">
            <el-icon><ChatLineRound /></el-icon>
            AI助手
          </el-button>
        </div>
      </div>
      
      <!-- 编辑区域 -->
      <div class="editor-content">
        <!-- 主编辑器 -->
        <div class="main-editor" :class="{ 'with-assist': showAssistPanel }">
          <div class="chapter-selector">
            <el-select v-model="currentChapter" placeholder="选择章节" @change="loadChapter">
              <el-option 
                v-for="chapter in chapters" 
                :key="chapter.id" 
                :label="chapter.title" 
                :value="chapter.id"
              />
            </el-select>
            <el-button size="small" @click="addChapter">
              <el-icon><Plus /></el-icon>
              新建章节
            </el-button>
          </div>
          
          <div class="chapter-title">
            <el-input 
              v-model="chapterTitle" 
              placeholder="章节标题"
              @input="updateChapterTitle"
            />
          </div>
          
          <div class="text-editor">
            <textarea 
              ref="textEditor"
              v-model="content"
              @input="handleInput"
              @select="handleTextSelect"
              placeholder="在这里开始写作..."
              class="editor-textarea"
            />
          </div>
        </div>
        
        <!-- AI辅助面板 -->
        <div v-if="showAssistPanel" class="assist-panel">
          <div class="assist-header">
            <h4>AI写作助手</h4>
            <el-button size="small" text @click="showAssistPanel = false">
              <el-icon><Close /></el-icon>
            </el-button>
          </div>
          
          <el-tabs v-model="assistTab" type="border-card">
            <!-- 智能续写 -->
            <el-tab-pane label="智能续写" name="continue">
              <div class="assist-content">
                <p class="assist-tip">选中文本后点击续写，AI将根据上下文继续创作</p>
                <el-button 
                  type="primary" 
                  @click="continueWriting"
                  :disabled="!selectedText"
                  :loading="generating"
                >
                  续写文本
                </el-button>
                <div v-if="generatedText" class="generated-text">
                  <h5>AI生成内容：</h5>
                  <div class="text-preview">{{ generatedText }}</div>
                  <div class="text-actions">
                    <el-button size="small" @click="applyGenerated">应用</el-button>
                    <el-button size="small" @click="regenerateText">重新生成</el-button>
                  </div>
                </div>
              </div>
            </el-tab-pane>
            
            <!-- 文笔优化 -->
            <el-tab-pane label="文笔优化" name="optimize">
              <div class="assist-content">
                <p class="assist-tip">选中需要优化的文本</p>
                <el-button 
                  type="primary" 
                  @click="optimizeText"
                  :disabled="!selectedText"
                  :loading="generating"
                >
                  优化文笔
                </el-button>
                <div v-if="optimizedText" class="generated-text">
                  <h5>优化后内容：</h5>
                  <div class="text-preview">{{ optimizedText }}</div>
                  <div class="text-actions">
                    <el-button size="small" @click="applyOptimized">应用</el-button>
                    <el-button size="small" @click="reoptimizeText">重新优化</el-button>
                  </div>
                </div>
              </div>
            </el-tab-pane>
            
            <!-- 情节建议 -->
            <el-tab-pane label="情节建议" name="plot">
              <div class="assist-content">
                <p class="assist-tip">获取剧情发展建议</p>
                <el-button 
                  type="primary" 
                  @click="getPlotSuggestions"
                  :loading="generating"
                >
                  获取建议
                </el-button>
                <div v-if="plotSuggestions.length" class="suggestions">
                  <h5>剧情建议：</h5>
                  <div 
                    v-for="(suggestion, index) in plotSuggestions" 
                    :key="index"
                    class="suggestion-item"
                    @click="applySuggestion(suggestion)"
                  >
                    {{ suggestion }}
                  </div>
                </div>
              </div>
            </el-tab-pane>
            
            <!-- 人物对话 -->
            <el-tab-pane label="人物对话" name="dialogue">
              <div class="assist-content">
                <p class="assist-tip">选中对话内容进行优化</p>
                <el-button 
                  type="primary" 
                  @click="optimizeDialogue"
                  :disabled="!selectedText"
                  :loading="generating"
                >
                  优化对话
                </el-button>
                <div v-if="optimizedDialogue" class="generated-text">
                  <h5>优化后对话：</h5>
                  <div class="text-preview">{{ optimizedDialogue }}</div>
                  <div class="text-actions">
                    <el-button size="small" @click="applyDialogue">应用</el-button>
                    <el-button size="small" @click="reoptimizeDialogue">重新优化</el-button>
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { useAIStore } from '@/store/ai'
import { useProjectStore } from '@/store/project'
import { useAppStore } from '@/store/app'
import { textUtils, commonUtils } from '@/utils'

export default {
  name: 'Editor',
  setup() {
    const aiStore = useAIStore()
    const projectStore = useProjectStore()
    const appStore = useAppStore()
    const textEditor = ref(null)
    const content = ref('')
    const chapterTitle = ref('')
    const currentChapter = ref(null)
    const showAssistPanel = ref(false)
    const assistTab = ref('continue')
    const generating = ref(false)
    
    // 选中的文本
    const selectedText = ref('')
    const selectionStart = ref(0)
    const selectionEnd = ref(0)
    
    // AI生成的内容
    const generatedText = ref('')
    const optimizedText = ref('')
    const optimizedDialogue = ref('')
    const plotSuggestions = ref([])
    
    // 章节数据
    const chapters = computed(() => projectStore.chapters)
    
    // 当前项目
    const currentProject = computed(() => projectStore.currentProject)
    
    const wordCount = computed(() => {
      return textUtils.getWordCount(content.value)
    })
    
    // 自动保存
    const debouncedSave = commonUtils.debounce(async () => {
      if (currentChapter.value && content.value !== chapters.value.find(c => c.id === currentChapter.value)?.content) {
        await saveContent()
      }
    }, 2000)
    
    // 检查AI服务状态
    const checkAIStatus = () => {
      if (!aiStore.isReady) {
        ElMessage({
          message: '请先在设置中配置Google Gemini API Key',
          type: 'warning'
        })
        return false
      }
      return true
    }
    
    // 处理文本选择
    const handleTextSelect = () => {
      if (textEditor.value) {
        const textarea = textEditor.value
        const start = textarea.selectionStart
        const end = textarea.selectionEnd
        
        if (start !== end) {
          selectedText.value = content.value.substring(start, end)
          selectionStart.value = start
          selectionEnd.value = end
        } else {
          selectedText.value = ''
        }
      }
    }
    
    // 处理输入
    const handleInput = () => {
      // 自动保存
      debouncedSave()
    }
    
    // 加载章节
    const loadChapter = async (chapterId) => {
      if (!chapterId) return
      
      const chapter = chapters.value.find(c => c.id === chapterId)
      if (chapter) {
        content.value = chapter.content || ''
        chapterTitle.value = chapter.title || ''
        projectStore.setCurrentChapter(chapterId)
        
        // 清除AI生成内容
        generatedText.value = ''
        optimizedText.value = ''
        optimizedDialogue.value = ''
        plotSuggestions.value = []
      }
    }
    
    // 更新章节标题
    const updateChapterTitle = async () => {
      if (currentChapter.value && chapterTitle.value) {
        await projectStore.updateChapter(currentChapter.value, {
          title: chapterTitle.value
        })
      }
    }
    
    // 添加章节
    const addChapter = async () => {
      if (!currentProject.value) {
        ElMessage({
          message: '请先选择一个项目',
          type: 'warning'
        })
        return
      }
      
      const result = await projectStore.createChapter({
        title: `第${chapters.value.length + 1}章：新章节`,
        content: ''
      })
      
      if (result.success) {
        currentChapter.value = result.data.id
        await loadChapter(result.data.id)
        
        ElMessage({
          message: '章节创建成功',
          type: 'success'
        })
      }
    }
    
    // 保存内容
    const saveContent = async () => {
      if (!currentChapter.value) {
        ElMessage({
          message: '请先选择章节',
          type: 'warning'
        })
        return
      }
      
      try {
        const result = await projectStore.updateChapter(currentChapter.value, {
          content: content.value,
          title: chapterTitle.value
        })
        
        if (result.success) {
          ElMessage({
            message: '保存成功',
            type: 'success'
          })
        }
      } catch (error) {
        ElMessage({
          message: '保存失败',
          type: 'error'
        })
      }
    }
    
    // 导出内容
    const exportContent = async () => {
      if (!currentProject.value) {
        ElMessage({
          message: '请先选择项目',
          type: 'warning'
        })
        return
      }
      
      const result = await projectStore.exportProject('txt')
      if (result.success) {
        // 触发下载
        const blob = new Blob([result.data], { type: 'text/plain;charset=utf-8' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `${currentProject.value.title}.txt`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)
        
        ElMessage({
          message: '导出成功',
          type: 'success'
        })
      }
    }
    
    // 撤销重做
    const undo = () => {
      document.execCommand('undo')
    }
    
    const redo = () => {
      document.execCommand('redo')
    }
    
    // AI功能
    const continueWriting = async () => {
      if (!selectedText.value) {
        ElMessage({
          message: '请先选择文本',
          type: 'warning'
        })
        return
      }
      
      if (!checkAIStatus()) return
      
      try {
        // 获取上下文
        const context = content.value.substring(Math.max(0, selectionStart.value - 500), selectionStart.value)
        
        const result = await aiStore.continueWriting(selectedText.value, context)
        
        if (result.success) {
          generatedText.value = result.data
          
          ElMessage({
            message: '续写完成',
            type: 'success'
          })
        } else {
          ElMessage({
            message: result.message || '续写失败',
            type: 'error'
          })
        }
      } catch (error) {
        console.error('续写失败:', error)
        ElMessage({
          message: '续写失败',
          type: 'error'
        })
      }
    }
    
    const optimizeText = async () => {
      if (!selectedText.value) {
        ElMessage({
          message: '请先选择文本',
          type: 'warning'
        })
        return
      }
      
      if (!checkAIStatus()) return
      
      try {
        const result = await aiStore.optimizeText(selectedText.value, '流畅自然')
        
        if (result.success) {
          optimizedText.value = result.data
          
          ElMessage({
            message: '文笔优化完成',
            type: 'success'
          })
        } else {
          ElMessage({
            message: result.message || '优化失败',
            type: 'error'
          })
        }
      } catch (error) {
        console.error('优化失败:', error)
        ElMessage({
          message: '优化失败',
          type: 'error'
        })
      }
    }
    
    const getPlotSuggestions = async () => {
      if (!checkAIStatus()) return
      
      try {
        // 获取当前情节上下文
        const currentPlot = content.value.substring(Math.max(0, content.value.length - 1000))
        
        const novelContext = {
          genre: currentProject.value?.genre || '',
          style: currentProject.value?.style || '',
          characters: projectStore.characters.map(c => `${c.name}: ${c.description}`).join('; ')
        }
        
        const result = await aiStore.getPlotSuggestions(currentPlot, novelContext)
        
        if (result.success) {
          plotSuggestions.value = result.data
          
          ElMessage({
            message: '获取建议成功',
            type: 'success'
          })
        } else {
          ElMessage({
            message: result.message || '获取建议失败',
            type: 'error'
          })
        }
      } catch (error) {
        console.error('获取建议失败:', error)
        ElMessage({
          message: '获取建议失败',
          type: 'error'
        })
      }
    }
    
    const optimizeDialogue = async () => {
      if (!selectedText.value) {
        ElMessage({
          message: '请先选择对话内容',
          type: 'warning'
        })
        return
      }
      
      if (!checkAIStatus()) return
      
      try {
        // 获取人物信息
        const characterInfo = projectStore.characters.map(c => 
          `${c.name}: ${c.personality || c.description}`
        ).join('\n')
        
        const result = await aiStore.optimizeDialogue(selectedText.value, characterInfo)
        
        if (result.success) {
          optimizedDialogue.value = result.data
          
          ElMessage({
            message: '对话优化完成',
            type: 'success'
          })
        } else {
          ElMessage({
            message: result.message || '对话优化失败',
            type: 'error'
          })
        }
      } catch (error) {
        console.error('对话优化失败:', error)
        ElMessage({
          message: '对话优化失败',
          type: 'error'
        })
      }
    }
    
    // 应用生成的内容
    const applyGenerated = () => {
      if (generatedText.value && textEditor.value) {
        const newContent = content.value.substring(0, selectionEnd.value) + 
                          generatedText.value + 
                          content.value.substring(selectionEnd.value)
        content.value = newContent
        generatedText.value = ''
        
        // 触发自动保存
        debouncedSave()
      }
    }
    
    const applyOptimized = () => {
      if (optimizedText.value && textEditor.value) {
        const newContent = content.value.substring(0, selectionStart.value) + 
                          optimizedText.value + 
                          content.value.substring(selectionEnd.value)
        content.value = newContent
        optimizedText.value = ''
        
        // 清除选择
        selectedText.value = ''
        
        // 触发自动保存
        debouncedSave()
      }
    }
    
    const applyDialogue = () => {
      if (optimizedDialogue.value && textEditor.value) {
        const newContent = content.value.substring(0, selectionStart.value) + 
                          optimizedDialogue.value + 
                          content.value.substring(selectionEnd.value)
        content.value = newContent
        optimizedDialogue.value = ''
        
        // 清除选择
        selectedText.value = ''
        
        // 触发自动保存
        debouncedSave()
      }
    }
    
    const applySuggestion = (suggestion) => {
      // 将建议插入到光标位置
      if (textEditor.value) {
        const textarea = textEditor.value
        const cursorPos = textarea.selectionStart
        const suggestionText = `\n\n[情节建议：${suggestion.description}]\n\n`
        
        const newContent = content.value.substring(0, cursorPos) + 
                          suggestionText + 
                          content.value.substring(cursorPos)
        content.value = newContent
        
        // 移动光标到插入内容后
        nextTick(() => {
          textarea.selectionStart = textarea.selectionEnd = cursorPos + suggestionText.length
          textarea.focus()
        })
        
        ElMessage({
          message: `应用建议：${suggestion.title}`,
          type: 'info'
        })
        
        // 触发自动保存
        debouncedSave()
      }
    }
    
    const regenerateText = () => {
      continueWriting()
    }
    
    const reoptimizeText = () => {
      optimizeText()
    }
    
    const reoptimizeDialogue = () => {
      optimizeDialogue()
    }
    
    // 初始化
    onMounted(async () => {
      console.log('编辑器初始化...')
      console.log('当前项目:', projectStore.currentProject)
      
      // 如果有当前项目，加载章节
      if (projectStore.currentProject) {
        console.log('加载项目章节:', projectStore.currentProject.id)
        await projectStore.loadProjectChapters(projectStore.currentProject.id)
        
        console.log('章节列表:', projectStore.chapters)
        
        if (chapters.value.length > 0) {
          currentChapter.value = chapters.value[0].id
          await loadChapter(currentChapter.value)
          console.log('加载第一个章节:', chapters.value[0].title)
        }
      } else {
        console.log('没有当前项目，尝试加载项目列表...')
        // 如果没有当前项目，尝试设置最近的项目
        if (projectStore.projects.length > 0) {
          const latestProject = projectStore.projects[0]
          console.log('设置最新项目为当前项目:', latestProject.title)
          await projectStore.setCurrentProject(latestProject.id)
        } else {
          console.log('没有任何项目')
          ElMessage({
            message: '请先创建一个项目或从全自动创作开始',
            type: 'info'
          })
        }
      }
    })
    
    // 监听项目变化
    watch(() => projectStore.currentProject, async (newProject) => {
      if (newProject) {
        await projectStore.loadProjectChapters(newProject.id)
        if (chapters.value.length > 0) {
          currentChapter.value = chapters.value[0].id
          await loadChapter(currentChapter.value)
        }
      }
    })
    
    // 监听章节列表变化
    watch(chapters, (newChapters) => {
      if (newChapters.length > 0 && !currentChapter.value) {
        currentChapter.value = newChapters[0].id
        loadChapter(currentChapter.value)
      }
    })
    
    return {
      textEditor,
      content,
      chapterTitle,
      currentChapter,
      chapters,
      showAssistPanel,
      assistTab,
      generating,
      selectedText,
      generatedText,
      optimizedText,
      optimizedDialogue,
      plotSuggestions,
      wordCount,
      handleTextSelect,
      handleInput,
      loadChapter,
      updateChapterTitle,
      addChapter,
      saveContent,
      exportContent,
      undo,
      redo,
      continueWriting,
      optimizeText,
      getPlotSuggestions,
      optimizeDialogue,
      applyGenerated,
      applyOptimized,
      applyDialogue,
      applySuggestion,
      regenerateText,
      reoptimizeText,
      reoptimizeDialogue
    }
  }
}
</script>

<style scoped>
.editor {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.editor-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  background: #ffffff;
  border-bottom: 1px solid #e6e6e6;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 10px;
}

.word-count {
  font-size: 14px;
  color: #666;
}

.editor-content {
  flex: 1;
  display: flex;
  min-height: 0;
}

.main-editor {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 20px;
}

.main-editor.with-assist {
  flex: 1;
  border-right: 1px solid #e6e6e6;
}

.chapter-selector {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
}

.chapter-title {
  margin-bottom: 20px;
}

.text-editor {
  flex: 1;
  min-height: 0;
}

.editor-textarea {
  width: 100%;
  height: 100%;
  min-height: 500px;
  padding: 20px;
  border: 1px solid #e6e6e6;
  border-radius: 4px;
  font-size: 16px;
  line-height: 1.8;
  font-family: 'Microsoft YaHei', sans-serif;
  resize: none;
  outline: none;
}

.editor-textarea:focus {
  border-color: #409eff;
}

.assist-panel {
  width: 400px;
  background: #fafafa;
  border-left: 1px solid #e6e6e6;
  display: flex;
  flex-direction: column;
}

.assist-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: #ffffff;
  border-bottom: 1px solid #e6e6e6;
}

.assist-header h4 {
  margin: 0;
  color: #2c3e50;
}

.assist-content {
  padding: 20px;
  flex: 1;
}

.assist-tip {
  margin: 0 0 15px 0;
  color: #666;
  font-size: 14px;
}

.generated-text {
  margin-top: 20px;
  padding: 15px;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #e6e6e6;
}

.generated-text h5 {
  margin: 0 0 10px 0;
  color: #2c3e50;
}

.text-preview {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  line-height: 1.6;
  margin-bottom: 15px;
}

.text-actions {
  display: flex;
  gap: 10px;
}

.suggestions {
  margin-top: 20px;
}

.suggestions h5 {
  margin: 0 0 10px 0;
  color: #2c3e50;
}

.suggestion-item {
  padding: 10px;
  background: #ffffff;
  border-radius: 4px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.suggestion-item:hover {
  background: #e6f7ff;
}
</style>