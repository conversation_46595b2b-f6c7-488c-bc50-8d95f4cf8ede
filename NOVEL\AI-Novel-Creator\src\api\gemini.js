import { GoogleGenerativeAI } from '@google/generative-ai'

class GeminiService {
  constructor() {
    this.genAI = null
    this.model = null
    this.isInitialized = false
  }

  // 初始化Gemini API
  async initialize(apiKey) {
    try {
      if (!apiKey) {
        throw new Error('API Key is required')
      }

      if (!apiKey.startsWith('AIza')) {
        throw new Error('Invalid API Key format. Gemini API Key should start with "AIza"')
      }

      this.genAI = new GoogleGenerativeAI(apiKey)
      this.model = this.genAI.getGenerativeModel({ model: 'gemini-1.5-flash' })
      this.isInitialized = true
      
      return { success: true, message: 'Gemini API initialized successfully' }
    } catch (error) {
      console.error('Failed to initialize Gemini API:', error)
      this.isInitialized = false
      return { success: false, message: error.message }
    }
  }

  // 检查是否已初始化
  checkInitialization() {
    if (!this.isInitialized || !this.model) {
      throw new Error('Gemini API not initialized. Please set API Key first.')
    }
  }

  // 生成文本
  async generateText(prompt, options = {}) {
    this.checkInitialization()

    try {
      const result = await this.model.generateContent(prompt)
      const response = await result.response
      const text = response.text()
      
      return {
        success: true,
        text: text,
        usage: response.usage || null
      }
    } catch (error) {
      console.error('Failed to generate text:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // 生成小说大纲
  async generateNovelOutline(config) {
    const prompt = `请为以下小说配置生成详细的创作大纲：

小说配置：
- 题材：${config.genre}
- 类型：${config.category}
- 子类型：${config.subGenre}
- 小说名称：${config.title}
- 总章节数：${config.totalChapters}
- 单章字数：${config.wordsPerChapter}
- 创作风格：${config.style}
- 故事背景：${config.background || '无特殊设定'}
- 主角设定：${config.protagonist || '无特殊设定'}

请按以下格式输出：
1. 故事简介（200字以内）
2. 主要人物（3-5个主要角色及其特点）
3. 世界观设定（背景世界的基本设定）
4. 核心冲突（主要矛盾和冲突点）
5. 章节规划（前10章的标题和内容概要）

请确保内容符合${config.genre}${config.subGenre}的特点，风格为${config.style}。`

    return await this.generateText(prompt)
  }

  // 生成章节内容
  async generateChapterContent(chapterInfo, novelContext) {
    const prompt = `请根据以下信息创作一个完整的小说章节：

章节信息：
- 章节标题：${chapterInfo.title}
- 章节概要：${chapterInfo.summary}
- 目标字数：${chapterInfo.wordCount}

小说背景：
- 题材：${novelContext.genre}
- 风格：${novelContext.style}
- 主要人物：${novelContext.characters}
- 故事背景：${novelContext.background}

前情提要：
${novelContext.previousChapter || '这是第一章'}

创作要求：
1. 字数控制在${chapterInfo.wordCount}字左右
2. 保持${novelContext.style}的写作风格
3. 情节紧凑，对话生动
4. 符合${novelContext.genre}类型特点
5. 为下一章留下合适的过渡

请直接输出章节内容，不需要其他说明。`

    return await this.generateText(prompt)
  }

  // 文本续写
  async continueWriting(selectedText, context) {
    const prompt = `请根据以下上下文继续写作：

上下文：
${context}

选中的文本：
${selectedText}

请从选中文本的末尾开始续写，保持文风一致，内容自然流畅。续写300-500字。`

    return await this.generateText(prompt)
  }

  // 文笔优化
  async optimizeText(text, style = '流畅自然') {
    const prompt = `请优化以下文本，要求：
1. 保持原意不变
2. 提升文笔质量
3. 使语言更加${style}
4. 修正语法错误
5. 增强表达效果

原文：
${text}

请直接输出优化后的文本：`

    return await this.generateText(prompt)
  }

  // 情节建议
  async getPlotSuggestions(currentPlot, novelContext) {
    const prompt = `基于以下小说情节，请提供5个可能的剧情发展建议：

当前情节：
${currentPlot}

小说背景：
- 题材：${novelContext.genre}
- 风格：${novelContext.style}
- 主要人物：${novelContext.characters}

请为每个建议提供：
1. 简短的发展方向（20字以内）
2. 具体的情节描述（50字以内）

格式示例：
1. 主角获得新能力 - 在危机中觉醒隐藏的力量，改变实力格局
2. 引入新角色 - 神秘人物登场，带来重要信息或新的冲突
...`

    return await this.generateText(prompt)
  }

  // 对话优化
  async optimizeDialogue(dialogue, characterInfo) {
    const prompt = `请优化以下对话，要求：
1. 保持人物性格特点
2. 让对话更加生动自然
3. 增强情感表达
4. 丰富对话标签和动作描写

人物信息：
${characterInfo}

原对话：
${dialogue}

请输出优化后的对话内容：`

    return await this.generateText(prompt)
  }

  // 场景描写
  async generateSceneDescription(sceneInfo, style = '细腻生动') {
    const prompt = `请根据以下场景信息创作场景描写：

场景信息：
${sceneInfo}

要求：
1. 描写${style}
2. 注重感官体验（视觉、听觉、触觉等）
3. 营造相应的氛围
4. 字数控制在200-400字

请直接输出场景描写：`

    return await this.generateText(prompt)
  }

  // 人物设定生成
  async generateCharacterProfile(characterName, role, novelContext) {
    const prompt = `请为以下角色创建详细的人物设定：

角色信息：
- 姓名：${characterName}
- 角色定位：${role}

小说背景：
- 题材：${novelContext.genre}
- 世界观：${novelContext.worldSetting}

请包含以下内容：
1. 基本信息（年龄、外貌、身份）
2. 性格特点（3-5个关键特质）
3. 背景故事（成长经历、重要事件）
4. 能力特长（技能、天赋等）
5. 人际关系（与主要角色的关系）
6. 核心动机（行为驱动力）
7. 语言特色（说话方式、口头禅等）

请确保人物设定符合${novelContext.genre}的特点。`

    return await this.generateText(prompt)
  }

  // 测试连接
  async testConnection() {
    try {
      this.checkInitialization()
      
      const result = await this.generateText('请回复"连接测试成功"')
      return result.success
    } catch (error) {
      console.error('Connection test failed:', error)
      return false
    }
  }
}

// 创建单例实例
const geminiService = new GeminiService()

export default geminiService