<template>
  <button
    class="modern-button"
    :class="[
      `btn-${variant}`,
      `btn-${size}`,
      {
        'btn-loading': loading,
        'btn-disabled': disabled,
        'btn-icon-only': iconOnly,
        'btn-gradient': gradient,
        'btn-glass': glass,
        'btn-neumorphism': neumorphism
      }
    ]"
    :disabled="disabled || loading"
    @click="handleClick"
  >
    <!-- 加载状态 -->
    <div v-if="loading" class="btn-loading-spinner">
      <div class="spinner-dot" v-for="i in 3" :key="i" :style="{ animationDelay: (i * 0.1) + 's' }"></div>
    </div>
    
    <!-- 图标 -->
    <div v-if="icon && !loading" class="btn-icon" :class="{ 'floating': iconFloat }">
      <el-icon>
        <component :is="icon" />
      </el-icon>
    </div>
    
    <!-- 文字内容 -->
    <span v-if="!iconOnly && !loading" class="btn-text" :class="{ 'gradient-text': textGradient }">
      <slot>{{ text }}</slot>
    </span>
    
    <!-- 右侧图标 -->
    <div v-if="iconRight && !loading" class="btn-icon-right">
      <el-icon>
        <component :is="iconRight" />
      </el-icon>
    </div>
    
    <!-- 涟漪效果 -->
    <div class="btn-ripple" ref="rippleRef"></div>
    
    <!-- 悬浮装饰 -->
    <div v-if="decorative" class="btn-decoration"></div>
  </button>
</template>

<script>
import { ref } from 'vue'

export default {
  name: 'ModernButton',
  props: {
    variant: {
      type: String,
      default: 'primary',
      validator: (value) => ['primary', 'secondary', 'success', 'warning', 'danger', 'info', 'text'].includes(value)
    },
    size: {
      type: String,
      default: 'medium',
      validator: (value) => ['small', 'medium', 'large'].includes(value)
    },
    text: {
      type: String,
      default: ''
    },
    icon: {
      type: String,
      default: ''
    },
    iconRight: {
      type: String,
      default: ''
    },
    iconOnly: {
      type: Boolean,
      default: false
    },
    iconFloat: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    gradient: {
      type: Boolean,
      default: false
    },
    glass: {
      type: Boolean,
      default: false
    },
    neumorphism: {
      type: Boolean,
      default: false
    },
    textGradient: {
      type: Boolean,
      default: false
    },
    decorative: {
      type: Boolean,
      default: false
    },
    ripple: {
      type: Boolean,
      default: true
    }
  },
  emits: ['click'],
  setup(props, { emit }) {
    const rippleRef = ref(null)

    const handleClick = (event) => {
      if (props.disabled || props.loading) return

      // 涟漪效果
      if (props.ripple && rippleRef.value) {
        const rect = event.currentTarget.getBoundingClientRect()
        const x = event.clientX - rect.left
        const y = event.clientY - rect.top
        
        const ripple = document.createElement('div')
        ripple.className = 'ripple-effect'
        ripple.style.left = x + 'px'
        ripple.style.top = y + 'px'
        
        rippleRef.value.appendChild(ripple)
        
        setTimeout(() => {
          ripple.remove()
        }, 600)
      }

      emit('click', event)
    }

    return {
      rippleRef,
      handleClick
    }
  }
}
</script>

<style scoped>
/* 引入水墨书香主题 */
@import '@/assets/styles/modern-theme.css';

.modern-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  border: none;
  border-radius: var(--radius-lg);
  font-family: var(--font-elegant);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-paper);
  overflow: hidden;
  user-select: none;
  vertical-align: middle;
}

/* 尺寸变体 */
.btn-small {
  padding: var(--spacing-xs) var(--spacing-md);
  font-size: 13px;
  min-height: 32px;
}

.btn-medium {
  padding: var(--spacing-sm) var(--spacing-lg);
  font-size: 14px;
  min-height: 40px;
}

.btn-large {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: 16px;
  min-height: 48px;
}

.btn-icon-only.btn-small {
  width: 32px;
  padding: 0;
}

.btn-icon-only.btn-medium {
  width: 40px;
  padding: 0;
}

.btn-icon-only.btn-large {
  width: 48px;
  padding: 0;
}

/* 颜色变体 */
.btn-primary {
  background: var(--gradient-gold);
  color: var(--ink-jiao);
  border: 2px solid var(--huang-jin);
}

.btn-primary:hover:not(.btn-disabled) {
  background: var(--huang-jin-light);
  transform: translateY(-2px);
  box-shadow: var(--shadow-paper-lg);
}

.btn-secondary {
  background: var(--bg-primary);
  color: var(--text-primary);
  border: 2px solid var(--border-medium);
}

.btn-secondary:hover:not(.btn-disabled) {
  border-color: var(--huang-jin);
  color: var(--huang-jin);
  transform: translateY(-1px);
}

.btn-success {
  background: var(--gradient-jade);
  color: var(--paper-xuan);
  border: 2px solid var(--song-lv);
}

.btn-success:hover:not(.btn-disabled) {
  background: var(--song-lv-light);
  transform: translateY(-2px);
  box-shadow: var(--shadow-paper-lg);
}

.btn-warning {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  color: var(--ink-jiao);
  border: 2px solid #ff9a9e;
}

.btn-danger {
  background: linear-gradient(135deg, var(--zhu-sha) 0%, #e74c3c 100%);
  color: var(--paper-xuan);
  border: 2px solid var(--zhu-sha);
}

.btn-info {
  background: var(--gradient-ocean);
  color: var(--paper-xuan);
  border: 2px solid var(--dan-qing);
}

.btn-text {
  background: transparent;
  color: var(--text-primary);
  border: 2px solid transparent;
}

.btn-text:hover:not(.btn-disabled) {
  background: var(--bg-elevated);
  color: var(--huang-jin);
}

/* 特殊效果 */
.btn-gradient {
  background: var(--gradient-gold);
  border: none;
  color: var(--ink-jiao);
}

.btn-glass {
  background: var(--glass-medium);
  backdrop-filter: var(--backdrop-blur);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: var(--text-primary);
}

.btn-neumorphism {
  background: var(--bg-primary);
  border: none;
  box-shadow: var(--neumorphism-light);
  color: var(--text-primary);
}

.btn-neumorphism:hover:not(.btn-disabled) {
  transform: translateY(-1px);
}

.btn-neumorphism:active {
  box-shadow: var(--neumorphism-inset);
  transform: translateY(0);
}

/* 状态 */
.btn-loading {
  pointer-events: none;
}

.btn-disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

/* 加载动画 */
.btn-loading-spinner {
  display: flex;
  gap: 2px;
}

.spinner-dot {
  width: 4px;
  height: 4px;
  background: currentColor;
  border-radius: 50%;
  animation: spinner-bounce 1.4s ease-in-out infinite both;
}

@keyframes spinner-bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* 图标 */
.btn-icon,
.btn-icon-right {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  transition: all var(--duration-fast) var(--ease-paper);
}

.btn-small .btn-icon,
.btn-small .btn-icon-right {
  font-size: 14px;
}

.btn-large .btn-icon,
.btn-large .btn-icon-right {
  font-size: 18px;
}

/* 涟漪效果 */
.btn-ripple {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
  border-radius: inherit;
}

.ripple-effect {
  position: absolute;
  width: 20px;
  height: 20px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%) scale(0);
  animation: ripple-animation 0.6s ease-out;
}

@keyframes ripple-animation {
  to {
    transform: translate(-50%, -50%) scale(4);
    opacity: 0;
  }
}

/* 装饰效果 */
.btn-decoration {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--duration-slow) var(--ease-paper);
}

.modern-button:hover .btn-decoration {
  left: 100%;
}

/* 文字渐变 */
.btn-text.gradient-text {
  background: var(--gradient-gold);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .btn-large {
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: 15px;
    min-height: 44px;
  }
  
  .btn-medium {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: 13px;
    min-height: 36px;
  }
}
</style>
