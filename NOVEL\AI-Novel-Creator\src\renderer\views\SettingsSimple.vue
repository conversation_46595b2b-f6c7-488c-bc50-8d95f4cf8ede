<template>
  <div class="ink-settings">
    <!-- 页面标题 -->
    <div class="page-header elegant-card fade-in-up">
      <div class="header-content">
        <h1 class="page-title gradient-text elegant">文房设置</h1>
        <p class="page-subtitle">配置您的创作环境</p>
      </div>
      <div class="header-decoration floating">
        <div class="decoration-icon">
          <el-icon><Setting /></el-icon>
        </div>
      </div>
    </div>

    <!-- 设置内容 -->
    <div class="settings-content">
      <!-- AI配置 -->
      <div class="settings-section elegant-card fade-in-up">
        <div class="section-header">
          <h2 class="section-title gradient-text purple">AI服务配置</h2>
          <p class="section-subtitle">配置AI服务以启用智能创作功能</p>
        </div>
        
        <div class="settings-form">
          <!-- 使用新的AI配置面板 -->
          <AIConfigPanel
            v-model="aiSettings"
            @save="saveAiSettings"
            @test="testConnection"
          />

          <!-- 高级设置 -->
          <el-divider content-position="left">高级设置</el-divider>

          <el-form :model="advancedSettings" label-width="120px">
            <el-form-item label="请求超时">
              <el-input-number
                v-model="advancedSettings.timeout"
                :min="5"
                :max="120"
                :step="5"
                style="width: 200px"
              />
              <span class="setting-hint">秒</span>
            </el-form-item>

            <el-form-item label="最大重试次数">
              <el-input-number
                v-model="advancedSettings.maxRetries"
                :min="0"
                :max="5"
                style="width: 200px"
              />
            </el-form-item>

            <el-form-item label="温度参数">
              <el-slider
                v-model="advancedSettings.temperature"
                :min="0"
                :max="2"
                :step="0.1"
                show-input
                style="width: 300px"
              />
              <div class="setting-hint">控制AI回复的创造性，0为最保守，2为最创新</div>
            </el-form-item>

            <el-form-item label="启用流式输出">
              <el-switch v-model="advancedSettings.stream" />
              <span class="setting-hint">实时显示AI生成内容</span>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <!-- 编辑器设置 -->
      <div class="settings-section">
        <div class="section-header">
          <h2 class="section-title">编辑器设置</h2>
          <p class="section-subtitle">个性化您的写作环境</p>
        </div>
        
        <div class="settings-form">
          <el-form :model="editorSettings" label-width="120px">
            <el-form-item label="字体大小">
              <el-slider 
                v-model="editorSettings.fontSize"
                :min="12"
                :max="24"
                show-input
                style="width: 300px"
              />
            </el-form-item>
            
            <el-form-item label="行间距">
              <el-slider 
                v-model="editorSettings.lineHeight"
                :min="1.2"
                :max="2.5"
                :step="0.1"
                show-input
                style="width: 300px"
              />
            </el-form-item>
            
            <el-form-item label="自动保存">
              <el-switch v-model="editorSettings.autoSave" />
              <span class="setting-hint">开启后将自动保存您的创作内容</span>
            </el-form-item>
            
            <el-form-item label="夜间模式">
              <el-switch v-model="editorSettings.darkMode" />
              <span class="setting-hint">护眼的深色主题</span>
            </el-form-item>
            
            <el-form-item label="字数统计">
              <el-switch v-model="editorSettings.wordCount" />
              <span class="setting-hint">实时显示字数统计</span>
            </el-form-item>
            
            <el-form-item>
              <button class="settings-btn secondary" @click="saveEditorSettings">
                <el-icon><DocumentCopy /></el-icon>
                保存设置
              </button>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <!-- 导出设置 -->
      <div class="settings-section">
        <div class="section-header">
          <h2 class="section-title">导出设置</h2>
          <p class="section-subtitle">配置文档导出选项</p>
        </div>
        
        <div class="settings-form">
          <el-form :model="exportSettings" label-width="120px">
            <el-form-item label="默认格式">
              <el-select v-model="exportSettings.defaultFormat" style="width: 200px">
                <el-option label="TXT文本" value="txt" />
                <el-option label="Word文档" value="docx" />
                <el-option label="PDF文档" value="pdf" />
                <el-option label="Markdown" value="md" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="包含章节号">
              <el-switch v-model="exportSettings.includeChapterNumbers" />
            </el-form-item>
            
            <el-form-item label="包含目录">
              <el-switch v-model="exportSettings.includeTableOfContents" />
            </el-form-item>
            
            <el-form-item>
              <button class="settings-btn secondary" @click="saveExportSettings">
                <el-icon><DocumentCopy /></el-icon>
                保存设置
              </button>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <!-- 关于信息 -->
      <div class="settings-section">
        <div class="section-header">
          <h2 class="section-title">关于应用</h2>
          <p class="section-subtitle">应用信息和版本详情</p>
        </div>
        
        <div class="about-content">
          <div class="about-item">
            <span class="about-label">应用名称</span>
            <span class="about-value">墨韵文轩 AI创作助手</span>
          </div>
          
          <div class="about-item">
            <span class="about-label">版本号</span>
            <span class="about-value">v1.0.0</span>
          </div>
          
          <div class="about-item">
            <span class="about-label">开发者</span>
            <span class="about-value">AI创作团队</span>
          </div>
          
          <div class="about-item">
            <span class="about-label">技术栈</span>
            <span class="about-value">Vue 3 + Electron + Element Plus</span>
          </div>
          
          <div class="about-actions">
            <button class="settings-btn secondary" @click="checkUpdate">
              <el-icon><Refresh /></el-icon>
              检查更新
            </button>
            
            <button class="settings-btn secondary" @click="showLicense">
              <el-icon><Document /></el-icon>
              许可证
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import AIConfigPanel from '@/components/AIConfigPanel.vue'

export default {
  name: 'SettingsSimple',
  components: {
    AIConfigPanel
  },
  setup() {
    // AI设置
    const aiSettings = ref({
      provider: '',
      apiKey: '',
      apiUrl: '',
      model: ''
    })

    // 高级设置
    const advancedSettings = ref({
      timeout: 30,
      maxRetries: 3,
      temperature: 0.7,
      stream: true
    })

    // 编辑器设置
    const editorSettings = ref({
      fontSize: 16,
      lineHeight: 1.6,
      autoSave: true,
      darkMode: false,
      wordCount: true
    })

    // 导出设置
    const exportSettings = ref({
      defaultFormat: 'txt',
      includeChapterNumbers: true,
      includeTableOfContents: true
    })

    // 测试AI连接
    const testConnection = (config) => {
      console.log('测试AI连接:', config)
      ElMessage({
        message: '正在测试AI服务连接...',
        type: 'info'
      })

      // 这里可以添加实际的API测试逻辑
      // 例如发送一个简单的请求来验证配置
    }

    // 保存AI设置
    const saveAiSettings = (config) => {
      console.log('保存AI配置:', config)

      // 保存到本地存储
      try {
        localStorage.setItem('aiSettings', JSON.stringify(config))
        localStorage.setItem('advancedSettings', JSON.stringify(advancedSettings.value))

        ElMessage({
          message: 'AI配置已保存到本地',
          type: 'success'
        })
      } catch (error) {
        ElMessage({
          message: '保存配置失败',
          type: 'error'
        })
      }
    }

    // 加载保存的设置
    const loadSettings = () => {
      try {
        const savedAiSettings = localStorage.getItem('aiSettings')
        const savedAdvancedSettings = localStorage.getItem('advancedSettings')

        if (savedAiSettings) {
          aiSettings.value = { ...aiSettings.value, ...JSON.parse(savedAiSettings) }
        } else {
          // 如果没有保存的设置，设置默认值
          aiSettings.value = {
            provider: 'gemini',
            apiKey: '',
            apiUrl: 'https://generativelanguage.googleapis.com/v1beta',
            model: 'gemini-2.0-flash-exp'
          }
        }

        if (savedAdvancedSettings) {
          advancedSettings.value = { ...advancedSettings.value, ...JSON.parse(savedAdvancedSettings) }
        }
      } catch (error) {
        console.warn('加载设置失败:', error)
        // 设置默认值
        aiSettings.value = {
          provider: 'gemini',
          apiKey: '',
          apiUrl: 'https://generativelanguage.googleapis.com/v1beta',
          model: 'gemini-2.0-flash-exp'
        }
      }
    }

    // 组件挂载时加载设置
    loadSettings()

    // 保存编辑器设置
    const saveEditorSettings = () => {
      ElMessage({
        message: '编辑器设置已保存',
        type: 'success'
      })
    }

    // 保存导出设置
    const saveExportSettings = () => {
      ElMessage({
        message: '导出设置已保存',
        type: 'success'
      })
    }

    // 检查更新
    const checkUpdate = () => {
      ElMessage({
        message: '当前已是最新版本',
        type: 'info'
      })
    }

    // 显示许可证
    const showLicense = () => {
      ElMessage({
        message: '许可证信息功能开发中...',
        type: 'info'
      })
    }

    return {
      aiSettings,
      advancedSettings,
      editorSettings,
      exportSettings,
      testConnection,
      saveAiSettings,
      saveEditorSettings,
      saveExportSettings,
      checkUpdate,
      showLicense
    }
  }
}
</script>

<style scoped>
/* 引入水墨书香主题 */
@import '@/assets/styles/modern-theme.css';

.ink-settings {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0;
}

/* 页面标题 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-3xl);
  padding: var(--spacing-xl);
  background: var(--gradient-paper);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-paper-md);
  position: relative;
  overflow: hidden;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--gradient-gold);
  opacity: 0.8;
}

.header-content {
  flex: 1;
}

.page-title {
  font-family: var(--font-calligraphy);
  font-size: 32px;
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-sm) 0;
  letter-spacing: 0.03em;
}

.page-subtitle {
  font-family: var(--font-elegant);
  font-size: 14px;
  color: var(--text-muted);
  margin: 0;
}

.header-decoration {
  width: 64px;
  height: 64px;
  background: var(--gradient-gold);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-paper-md);
}

.decoration-icon {
  font-size: 24px;
  color: var(--ink-jiao);
}

/* 设置内容 */
.settings-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3xl);
}

.settings-section {
  background: var(--bg-primary);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-paper-md);
}

.section-header {
  padding: var(--spacing-xl);
  background: var(--gradient-paper);
  border-bottom: 1px solid var(--border-light);
}

.section-title {
  font-family: var(--font-calligraphy);
  font-size: 20px;
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-sm) 0;
}

.section-subtitle {
  font-family: var(--font-elegant);
  font-size: 13px;
  color: var(--text-muted);
  margin: 0;
}

.settings-form {
  padding: var(--spacing-xl);
}

.form-actions {
  display: flex;
  gap: var(--spacing-md);
}

.settings-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  border: 2px solid;
  border-radius: var(--radius-lg);
  font-family: var(--font-elegant);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-paper);
}

.settings-btn.primary {
  background: var(--gradient-gold);
  border-color: var(--huang-jin);
  color: var(--ink-jiao);
}

.settings-btn.primary:hover {
  background: #f1c40f;
  transform: translateY(-2px);
  box-shadow: var(--shadow-paper-lg);
}

.settings-btn.secondary {
  background: var(--bg-primary);
  border-color: var(--border-medium);
  color: var(--text-muted);
}

.settings-btn.secondary:hover {
  border-color: var(--border-accent);
  color: var(--text-primary);
  transform: translateY(-1px);
}

.setting-hint {
  margin-left: var(--spacing-md);
  font-family: var(--font-elegant);
  font-size: 12px;
  color: var(--text-muted);
}

/* 关于信息 */
.about-content {
  padding: var(--spacing-xl);
}

.about-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) 0;
  border-bottom: 1px solid var(--border-light);
}

.about-item:last-of-type {
  border-bottom: none;
}

.about-label {
  font-family: var(--font-elegant);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
}

.about-value {
  font-family: var(--font-elegant);
  color: var(--text-primary);
}

.about-actions {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-xl);
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--border-light);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: var(--spacing-lg);
    text-align: center;
  }
  
  .form-actions,
  .about-actions {
    flex-direction: column;
  }
  
  .settings-btn {
    width: 100%;
    justify-content: center;
  }
  
  .about-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }
}
</style>
