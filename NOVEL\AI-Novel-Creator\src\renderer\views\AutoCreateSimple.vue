<template>
  <div class="ink-auto-create">
    <!-- 页面标题 -->
    <div class="page-header elegant-card fade-in-up">
      <div class="header-content">
        <h1 class="page-title gradient-text purple">妙笔生花</h1>
        <p class="page-subtitle">AI全自动创作，让灵感如泉涌</p>
      </div>
      <div class="header-decoration floating">
        <div class="decoration-icon pulsing">
          <el-icon><StarFilled /></el-icon>
        </div>
      </div>
    </div>

    <!-- AI状态检查 -->
    <div class="ai-status-card elegant-card fade-in-up">
      <div class="status-indicator" :class="aiStatusClass">
        <div class="status-icon floating">
          <el-icon v-if="isConfigured"><CircleCheck /></el-icon>
          <el-icon v-else><WarningFilled /></el-icon>
        </div>
        <div class="status-info">
          <span class="status-title gradient-text" :class="{ 'jade': isConfigured, 'warm': !isConfigured }">{{ aiStatusText }}</span>
          <span class="status-desc">{{ aiStatusDesc }}</span>
        </div>
      </div>

      <div v-if="!isConfigured" class="config-notice">
        <p>请先前往文房设置配置AI服务</p>
        <div class="config-actions">
          <button class="config-btn elegant-btn" @click="$router.push('/settings')">
            前往配置
          </button>
          <button class="refresh-btn elegant-btn" @click="refreshAIConfig">
            刷新状态
          </button>
        </div>
      </div>

      <div v-if="isConfigured" class="test-section">
        <div class="test-actions">
          <button class="test-btn elegant-btn" @click="testAPIOutput" :disabled="isTesting">
            {{ isTesting ? '测试中...' : '测试API输出' }}
          </button>
          <span class="test-desc">测试AI输出完整性</span>
        </div>
      </div>
    </div>

    <!-- 创作表单 -->
    <div v-if="isConfigured" class="create-form-section">
      <div class="form-card glass-card fade-in-up">
        <h2 class="form-title gradient-text">创作设定</h2>
        
        <el-form :model="createForm" label-width="100px" class="create-form">
          <el-form-item label="项目名称">
            <el-input 
              v-model="createForm.title" 
              placeholder="请输入小说名称"
              class="elegant-input"
            />
          </el-form-item>
          
          <el-form-item label="题材类型">
            <el-select 
              v-model="createForm.genre" 
              placeholder="请选择题材"
              style="width: 100%"
              class="elegant-select"
            >
              <el-option label="玄幻" value="玄幻" />
              <el-option label="都市" value="都市" />
              <el-option label="科幻" value="科幻" />
              <el-option label="历史" value="历史" />
              <el-option label="军事" value="军事" />
              <el-option label="游戏" value="游戏" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="读者群体">
            <el-select 
              v-model="createForm.category" 
              placeholder="请选择类型"
              style="width: 100%"
              class="elegant-select"
            >
              <el-option label="男频" value="男频" />
              <el-option label="女频" value="女频" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="创作要求">
            <el-input
              v-model="createForm.requirements"
              type="textarea"
              :rows="4"
              placeholder="请描述您的创作要求，如：主角设定、故事背景、情节要求等..."
              class="elegant-textarea"
            />
          </el-form-item>

          <el-form-item label="创意灵感">
            <el-input
              v-model="createForm.creativity"
              type="textarea"
              :rows="3"
              placeholder="请输入您的奇思妙想和创意，如：我觉醒反派系统当打脸主角就会变强、我能看到别人的好感度、我有读心术等..."
              class="elegant-textarea creativity-input"
            />
            <div class="creativity-hint">
              <el-icon><Sunny /></el-icon>
              <span>这里是您发挥创意的地方！任何脑洞都可以，AI会基于您的创意设计整个故事</span>
            </div>
          </el-form-item>

          <el-form-item label="总章节数">
            <el-input-number
              v-model="createForm.chapterCount"
              :min="10"
              :max="2000"
              :step="10"
              style="width: 200px"
            />
            <span class="form-hint">{{ getChapterCountTip(createForm.chapterCount) }}</span>
          </el-form-item>

          <el-form-item label="单章字数">
            <el-input-number
              v-model="createForm.wordsPerChapter"
              :min="1000"
              :max="8000"
              :step="500"
              style="width: 200px"
            />
            <span class="form-hint">{{ getWordCountTip(createForm.wordsPerChapter) }}</span>
          </el-form-item>
        </el-form>
        
        <div class="form-actions">
          <button
            class="create-btn primary neumorphism-btn"
            @click="startAutoCreate"
            :disabled="!canCreate"
          >
            <el-icon class="floating"><StarFilled /></el-icon>
            开始创作
          </button>

          <button class="create-btn secondary neumorphism-btn" @click="resetForm">
            <el-icon><RefreshLeft /></el-icon>
            重置表单
          </button>

          <!-- 测试按钮 -->
          <button class="create-btn test neumorphism-btn" @click="showTestResult">
            <el-icon><Tools /></el-icon>
            测试显示
          </button>
        </div>
      </div>
    </div>

    <!-- 创作进度 -->
    <div v-if="isCreating" class="progress-section fade-in-up">
      <div class="progress-card elegant-card">
        <h3 class="progress-title gradient-text purple">{{ currentStep }}</h3>
        <div class="progress-content">
          <el-progress
            :percentage="createProgress"
            :stroke-width="12"
            :color="progressColor"
            :show-text="false"
          />
          <div class="progress-info">
            <span class="progress-text">{{ progressText }}</span>
            <span class="progress-percent">{{ createProgress }}%</span>
          </div>
        </div>

        <!-- 创作步骤 -->
        <div class="creation-steps">
          <div
            v-for="(step, index) in creationSteps"
            :key="index"
            class="step-item"
            :class="{
              'active': index === currentStepIndex,
              'completed': index < currentStepIndex,
              'pending': index > currentStepIndex
            }"
          >
            <div class="step-icon">
              <el-icon v-if="index < currentStepIndex"><Check /></el-icon>
              <el-icon v-else-if="index === currentStepIndex"><Loading /></el-icon>
              <span v-else>{{ index + 1 }}</span>
            </div>
            <span class="step-text">{{ step.name }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 创作结果 -->
    <div v-if="creationResult" class="result-section fade-in-up">
      <div class="result-card elegant-card">
        <div class="result-header">
          <h3 class="result-title gradient-text jade">创作完成！</h3>
          <div class="result-actions">
            <el-button type="primary" @click="saveProject">
              <el-icon><DocumentAdd /></el-icon>
              保存项目
            </el-button>
            <el-button @click="exportResult">
              <el-icon><Download /></el-icon>
              导出文档
            </el-button>
            <el-button @click="continueEdit">
              <el-icon><EditPen /></el-icon>
              继续编辑
            </el-button>
          </div>
        </div>

        <div class="result-content">
          <el-tabs v-model="activeResultTab" class="result-tabs">
            <el-tab-pane label="故事大纲" name="outline">
              <div class="content-panel">
                <pre class="content-text">{{ creationResult.outline }}</pre>
              </div>
            </el-tab-pane>

            <el-tab-pane label="角色设定" name="characters">
              <div class="content-panel">
                <div v-for="(character, index) in creationResult.characters" :key="index" class="character-card">
                  <h4>{{ character.name }}</h4>
                  <pre class="content-text">{{ character.description }}</pre>
                </div>
              </div>
            </el-tab-pane>

            <el-tab-pane :label="creationResult.isLayered ? '分卷规划' : '章节内容'" name="chapters">
              <div class="content-panel">
                <!-- 分层规划模式 -->
                <div v-if="creationResult.isLayered" class="layered-info">
                  <div class="volume-summary">
                    <h4>📚 分卷规划概览</h4>
                    <div class="summary-stats">
                      <div class="stat-item">
                        <span class="stat-label">总章节数：</span>
                        <span class="stat-value">{{ creationResult.volumeInfo.totalChapters }}章</span>
                      </div>
                      <div class="stat-item">
                        <span class="stat-label">预计分卷：</span>
                        <span class="stat-value">{{ creationResult.volumeInfo.estimatedVolumes }}卷</span>
                      </div>
                      <div class="stat-item">
                        <span class="stat-label">预计总字数：</span>
                        <span class="stat-value">{{ Math.round(creationResult.volumeInfo.totalChapters * creationResult.metadata.wordsPerChapter / 10000) }}万字</span>
                      </div>
                    </div>
                    <div class="volume-message">
                      <el-alert
                        :title="creationResult.volumeInfo.message"
                        type="info"
                        :closable="false"
                        show-icon
                      />
                    </div>
                    <div class="volume-actions">
                      <el-button type="primary" @click="generateVolumeChapters(1)">
                        <el-icon><DocumentAdd /></el-icon>
                        生成第1卷详细章节
                      </el-button>
                      <el-button @click="showVolumeDialog = true">
                        <el-icon><Setting /></el-icon>
                        选择其他卷
                      </el-button>
                      <el-button type="success" @click="showChapterGenerator = true">
                        <el-icon><EditPen /></el-icon>
                        开始逐章创作
                      </el-button>
                    </div>
                  </div>
                </div>

                <!-- 传统模式 -->
                <div v-else>
                  <div v-for="(chapter, index) in creationResult.chapters" :key="index" class="chapter-card">
                    <h4>第{{ index + 1 }}章 {{ chapter.title }}</h4>
                    <pre class="content-text">{{ chapter.content }}</pre>
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>

    <!-- 功能介绍 -->
    <div class="features-section">
      <h2 class="features-title">功能特色</h2>
      <div class="features-grid">
        <div class="feature-card">
          <div class="feature-icon">
            <el-icon><StarFilled /></el-icon>
          </div>
          <h3>智能构思</h3>
          <p>AI自动生成完整的故事大纲和人物设定</p>
        </div>
        
        <div class="feature-card">
          <div class="feature-icon">
            <el-icon><EditPen /></el-icon>
          </div>
          <h3>自动写作</h3>
          <p>根据设定自动生成高质量的章节内容</p>
        </div>
        
        <div class="feature-card">
          <div class="feature-icon">
            <el-icon><Document /></el-icon>
          </div>
          <h3>一键导出</h3>
          <p>支持多种格式导出，方便后续编辑</p>
        </div>
      </div>
    </div>

    <!-- 逐章生成对话框 -->
    <el-dialog
      v-model="showChapterGenerator"
      title="逐章创作"
      width="80%"
      :close-on-click-modal="false"
    >
      <div class="chapter-generator">
        <div class="generator-header">
          <h3>📝 智能逐章创作</h3>
          <p>基于记忆系统，确保前后一致的高质量章节生成</p>
        </div>

        <div class="chapter-input">
          <el-form :model="chapterForm" label-width="100px">
            <el-form-item label="章节号">
              <el-input-number
                v-model="chapterForm.chapterNumber"
                :min="1"
                :max="creationResult?.volumeInfo?.totalChapters || 1000"
                style="width: 150px"
              />
            </el-form-item>

            <el-form-item label="章节标题">
              <el-input
                v-model="chapterForm.chapterTitle"
                placeholder="请输入章节标题，如：初入宗门"
                style="width: 300px"
              />
            </el-form-item>
          </el-form>
        </div>

        <div class="generator-actions">
          <el-button
            type="primary"
            @click="generateSingleChapter"
            :loading="isGeneratingChapter"
            :disabled="!chapterForm.chapterTitle"
          >
            <el-icon><Star /></el-icon>
            生成章节内容
          </el-button>

          <el-button @click="showMemoryViewer = true">
            <el-icon><View /></el-icon>
            查看记忆库
          </el-button>
        </div>

        <!-- 章节生成结果 -->
        <div v-if="currentChapterContent" class="chapter-result">
          <div class="result-header">
            <h4>第{{ chapterForm.chapterNumber }}章：{{ chapterForm.chapterTitle }}</h4>
            <div class="result-stats">
              <span>字数：{{ currentChapterContent.length }}字</span>
              <span>生成时间：{{ chapterGeneratedAt }}</span>
            </div>
          </div>

          <div class="chapter-preview">
            <h5>章节预览</h5>
            <div class="preview-content">
              <pre>{{ currentChapterContent.substring(0, 500) }}{{ currentChapterContent.length > 500 ? '...' : '' }}</pre>
            </div>
          </div>

          <div class="chapter-actions">
            <el-button type="primary" size="large" @click="startDeepReview">
              <el-icon><DocumentChecked /></el-icon>
              开始深度审核 (30分钟)
            </el-button>

            <el-button @click="regenerateChapter">
              <el-icon><Refresh /></el-icon>
              重新生成
            </el-button>

            <el-button @click="quickApprove">
              <el-icon><Check /></el-icon>
              快速通过
            </el-button>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 深度审核对话框 -->
    <el-dialog
      v-model="showDeepReview"
      title="深度章节审核"
      width="95%"
      :close-on-click-modal="false"
      class="deep-review-dialog"
    >
      <ChapterReviewPanel
        v-if="showDeepReview && currentChapterContent"
        :chapter-number="chapterForm.chapterNumber"
        :chapter-content="currentChapterContent"
        :memory-data="memoryData"
        :core-creativity="createForm.creativity"
        :consistency-issues="currentConsistencyIssues"
        @approve="handleChapterApprove"
        @reject="handleChapterReject"
        @revise="handleChapterRevise"
        @update-content="handleContentUpdate"
      />
    </el-dialog>

    <!-- 记忆库查看器 -->
    <el-dialog
      v-model="showMemoryViewer"
      title="记忆库"
      width="70%"
    >
      <div class="memory-viewer">
        <el-tabs v-model="activeMemoryTab">
          <el-tab-pane label="角色信息" name="characters">
            <div v-if="memoryData?.characters" class="memory-section">
              <div v-for="(character, name) in memoryData.characters" :key="name" class="character-item">
                <h4>{{ character.name }}（{{ character.role }}）</h4>
                <p><strong>状态：</strong>{{ character.status }}</p>
                <p><strong>最后出现：</strong>第{{ character.lastAppeared }}章</p>
                <p><strong>特征：</strong>{{ character.traits?.join('、') || '无' }}</p>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="世界观设定" name="world">
            <div v-if="memoryData?.worldSettings" class="memory-section">
              <p><strong>题材：</strong>{{ memoryData.worldSettings.genre }}</p>
              <p><strong>核心创意：</strong>{{ memoryData.worldSettings.creativity }}</p>
              <p><strong>世界规则：</strong>{{ memoryData.worldSettings.rules?.join('、') || '无' }}</p>
            </div>
          </el-tab-pane>

          <el-tab-pane label="伏笔记录" name="foreshadowing">
            <div v-if="memoryData?.foreshadowing" class="memory-section">
              <div v-for="foreshadow in memoryData.foreshadowing" :key="foreshadow.id" class="foreshadow-item">
                <h4>第{{ foreshadow.chapter }}章</h4>
                <p><strong>内容：</strong>{{ foreshadow.content }}</p>
                <p><strong>状态：</strong>{{ foreshadow.resolved ? '已解决' : '未解决' }}</p>
                <p><strong>计划解决：</strong>{{ foreshadow.plannedResolution }}</p>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Sunny, // 创意图标
  DocumentAdd, // 文档添加
  Setting, // 设置
  EditPen, // 编辑笔
  Star, // 魔法图标替代
  View, // 查看
  Check, // 确认
  Refresh, // 刷新
  Edit // 编辑
} from '@element-plus/icons-vue'
import { aiCreationService } from '@/services/aiCreationService.js'
import ChapterReviewPanel from '@/components/ChapterReviewPanel.vue'

export default {
  name: 'AutoCreateSimple',
  components: {
    Sunny,
    DocumentAdd,
    Setting,
    EditPen,
    Star,
    View,
    Check,
    Refresh,
    Edit,
    ChapterReviewPanel
  },
  setup() {
    const router = useRouter()
    
    // AI配置状态
    const isConfigured = ref(false)
    const isCreating = ref(false)
    const isTesting = ref(false)
    const createProgress = ref(0)
    const currentStepIndex = ref(0)
    const currentStep = ref('')

    // 创作结果
    const creationResult = ref(null)
    const activeResultTab = ref('outline')

    // 分卷相关
    const showVolumeDialog = ref(false)
    const selectedVolume = ref(1)
    const volumeChapters = ref(null)

    // 逐章生成相关
    const showChapterGenerator = ref(false)
    const isGeneratingChapter = ref(false)
    const chapterForm = ref({
      chapterNumber: 1,
      chapterTitle: ''
    })
    const currentChapterContent = ref('')
    const chapterGeneratedAt = ref('')
    const currentConsistencyIssues = ref([]) // 当前章节的一致性问题

    // 记忆库相关
    const showMemoryViewer = ref(false)
    const activeMemoryTab = ref('characters')
    const memoryData = ref(null)

    // 深度审核相关
    const showDeepReview = ref(false)
    const reviewHistory = ref([]) // 审核历史记录

    // 创作步骤
    const creationSteps = ref([
      { name: '分析需求', key: 'analyze' },
      { name: '总体规划', key: 'outline' },
      { name: '创建角色', key: 'characters' },
      { name: '分卷规划', key: 'volumes' },
      { name: '章节概要', key: 'chapters' },
      { name: '完善内容', key: 'polish' }
    ])

    // 进度颜色
    const progressColor = computed(() => {
      if (createProgress.value < 30) return '#409eff'
      if (createProgress.value < 70) return '#e6a23c'
      return '#67c23a'
    })

    // 检查AI配置
    const checkAIConfig = () => {
      isConfigured.value = aiCreationService.isConfigured()
      console.log('AI config status:', isConfigured.value)
    }

    // 刷新AI配置状态
    const refreshAIConfig = () => {
      checkAIConfig()
      if (isConfigured.value) {
        ElMessage.success('AI服务已配置，可以开始创作！')
      } else {
        ElMessage.warning('AI服务尚未配置，请前往设置页面配置')
      }
    }

    // 测试API输出
    const testAPIOutput = async () => {
      if (isTesting.value) return

      isTesting.value = true
      try {
        ElMessage.info('开始测试API输出完整性...')
        const result = await aiCreationService.testAPIOutput()

        // 显示测试结果
        ElMessageBox.alert(
          `测试完成！
输出长度: ${result.length} 字符
包含第10章: ${result.includes('第10章') ? '✅' : '❌'}
包含结局: ${result.includes('故事结局') || result.includes('高潮') ? '✅' : '❌'}

请查看浏览器控制台获取详细信息。`,
          'API测试结果',
          { type: 'info' }
        )
      } catch (error) {
        ElMessage.error(`测试失败: ${error.message}`)
      } finally {
        isTesting.value = false
      }
    }
    
    // 创作表单
    const createForm = ref({
      title: '',
      genre: '',
      category: '',
      requirements: '',
      creativity: '', // 用户创意输入
      chapterCount: 100,
      wordsPerChapter: 3000
    })

    // AI状态
    const aiStatusClass = computed(() => {
      return isConfigured.value ? 'configured' : 'not-configured'
    })
    
    const aiStatusText = computed(() => {
      return isConfigured.value ? 'AI笔墨就绪' : '待配置笔墨'
    })
    
    const aiStatusDesc = computed(() => {
      return isConfigured.value ? '可以开始自动创作' : '需要配置API Key'
    })

    // 是否可以创作
    const canCreate = computed(() => {
      return createForm.value.title && 
             createForm.value.genre && 
             createForm.value.category &&
             createForm.value.requirements
    })

    // 进度文本
    const progressText = computed(() => {
      if (createProgress.value < 20) return '正在分析创作要求...'
      if (createProgress.value < 40) return '正在生成故事大纲...'
      if (createProgress.value < 60) return '正在创建人物设定...'
      if (createProgress.value < 80) return '正在撰写章节内容...'
      if (createProgress.value < 100) return '正在完善细节...'
      return '创作完成！'
    })

    // 开始自动创作
    const startAutoCreate = async () => {
      if (!canCreate.value) {
        ElMessage.warning('请填写完整的创作信息')
        return
      }

      if (!isConfigured.value) {
        ElMessage.warning('请先配置AI服务')
        return
      }

      try {
        isCreating.value = true
        createProgress.value = 0
        currentStepIndex.value = 0
        creationResult.value = null

        // 步骤1: 分析需求
        currentStep.value = creationSteps.value[0].name
        createProgress.value = 10
        await new Promise(resolve => setTimeout(resolve, 1000))

        // 统一使用分层规划流程，不再区分章节数量
        const creationData = await createLayeredOutline()

        await new Promise(resolve => setTimeout(resolve, 1000))
        createProgress.value = 100

        // 保存结果（使用createLayeredOutline返回的数据）
        creationResult.value = creationData

        console.log('Creation result:', creationResult.value)
        ElMessage.success('创作完成！')

      } catch (error) {
        console.error('创作失败:', error)
        ElMessage.error(`创作失败: ${error.message}`)
      } finally {
        isCreating.value = false
      }
    }

    // 统一的分层规划流程（适用于所有章节数量）
    const createLayeredOutline = async () => {
      // 步骤2: 总体规划
      currentStepIndex.value = 1
      currentStep.value = creationSteps.value[1].name
      createProgress.value = 20

      const outline = await aiCreationService.generateOutline(createForm.value, (partialContent) => {
        console.log('Layered outline generation progress:', partialContent.length, 'characters')
      })
      createProgress.value = 40

      // 步骤3: 创建角色设定
      currentStepIndex.value = 2
      currentStep.value = creationSteps.value[2].name
      createProgress.value = 50

      const characters = await generateCharacters()
      createProgress.value = 70

      // 步骤4: 分卷规划提示
      currentStepIndex.value = 3
      currentStep.value = creationSteps.value[3].name
      createProgress.value = 80

      // 步骤5: 章节概要提示
      currentStepIndex.value = 4
      currentStep.value = creationSteps.value[4].name
      createProgress.value = 90

      // 步骤6: 完善内容
      currentStepIndex.value = 5
      currentStep.value = creationSteps.value[5].name
      createProgress.value = 95

      await new Promise(resolve => setTimeout(resolve, 1000))
      createProgress.value = 100

      // 构建结果数据
      const resultData = {
        outline,
        characters,
        chapters: [], // 大型网文不预生成章节内容
        isLayered: true, // 标记为分层规划
        volumeInfo: {
          totalChapters: createForm.value.chapterCount,
          estimatedVolumes: Math.ceil(createForm.value.chapterCount / 100),
          message: '已完成总体规划，可按需生成具体卷的详细章节概要'
        },
        metadata: {
          title: createForm.value.title,
          genre: createForm.value.genre,
          category: createForm.value.category,
          chapterCount: createForm.value.chapterCount,
          wordsPerChapter: createForm.value.wordsPerChapter,
          createdAt: new Date().toISOString()
        }
      }

      console.log('Creation result:', resultData)
      ElMessage.success('分层规划完成！')

      // 返回结果数据
      return resultData
    }

    // 小型小说的完整生成流程已删除，统一使用分层规划

    // 生成角色设定
    const generateCharacters = async () => {
      const characters = []
      const characterRoles = ['主角', '女主角', '反派', '配角']

      for (const role of characterRoles) {
        try {
          const character = await aiCreationService.generateCharacter({
            name: `${role}（待命名）`,
            role,
            genre: createForm.value.genre,
            background: createForm.value.requirements
          }, (partialContent) => {
            // 实时更新角色设定
            console.log(`${role} character generation progress:`, partialContent.length, 'characters')
          })

          characters.push({
            name: role,
            description: character
          })
        } catch (error) {
          console.error(`生成${role}失败:`, error)
        }
      }

      return characters
    }

    // 生成章节内容
    const generateChapters = async (outline) => {
      const chapters = []
      const chapterCount = Math.min(createForm.value.chapterCount, 3) // 限制演示章节数

      for (let i = 0; i < chapterCount; i++) {
        try {
          const chapterTitle = `第${i + 1}章`
          const chapterOutline = `根据总体大纲创作第${i + 1}章内容`

          const content = await aiCreationService.generateChapter({
            chapterTitle,
            outline: chapterOutline,
            previousContent: i > 0 ? chapters[i - 1].content : '',
            requirements: createForm.value.requirements
          }, (partialContent) => {
            // 实时更新章节内容
            console.log(`Chapter ${i + 1} generation progress:`, partialContent.length, 'characters')
          })

          chapters.push({
            title: chapterTitle,
            content
          })

          // 更新进度
          createProgress.value = 65 + (i + 1) / chapterCount * 20

        } catch (error) {
          console.error(`生成第${i + 1}章失败:`, error)
        }
      }

      return chapters
    }

    // 保存项目
    const saveProject = () => {
      if (!creationResult.value) return

      // 保存到本地存储
      const projects = JSON.parse(localStorage.getItem('novelProjects') || '[]')
      const newProject = {
        id: Date.now(),
        ...creationResult.value.metadata,
        outline: creationResult.value.outline,
        characters: creationResult.value.characters,
        chapters: creationResult.value.chapters,
        updatedAt: new Date().toISOString()
      }

      projects.push(newProject)
      localStorage.setItem('novelProjects', JSON.stringify(projects))

      ElMessage.success('项目已保存')
    }

    // 导出结果
    const exportResult = () => {
      if (!creationResult.value) return

      const content = `# ${creationResult.value.metadata.title}

## 故事大纲
${creationResult.value.outline}

## 角色设定
${creationResult.value.characters.map(char => `### ${char.name}\n${char.description}`).join('\n\n')}

## 章节内容
${creationResult.value.chapters.map(chapter => `### ${chapter.title}\n${chapter.content}`).join('\n\n')}
`

      const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `${creationResult.value.metadata.title}.txt`
      a.click()
      URL.revokeObjectURL(url)

      ElMessage.success('导出成功')
    }

    // 继续编辑
    const continueEdit = () => {
      router.push('/editor')
    }

    // 测试显示结果
    const showTestResult = () => {
      creationResult.value = {
        outline: `# 《都市修仙传》故事大纲

## 背景设定
现代都市中隐藏着修仙世界，主角林浩是一名普通大学生，在一次意外中获得古代修仙者的传承，从此踏上修仙之路。

## 主线剧情
1. 第一章：意外获得传承，觉醒修仙能力
2. 第二章：初入修仙世界，遇到第一个挑战
3. 第三章：实力提升，面对更大的危机

## 核心冲突
- 现代生活与修仙世界的冲突
- 个人成长与责任担当的矛盾
- 正邪势力的对抗

## 故事主题
成长、责任、正义、友情`,

        characters: [
          {
            name: '主角',
            description: `**林浩**
- 年龄：20岁
- 身份：大学生
- 性格：善良正直，有责任感，面对困难不轻易放弃
- 外貌：身高1.78米，相貌清秀，眼神坚定
- 背景：普通家庭出身，成绩优秀，意外获得修仙传承
- 能力：初级修仙术法，感知力强
- 成长轨迹：从普通学生到修仙者的转变`
          },
          {
            name: '女主角',
            description: `**苏雨萱**
- 年龄：19岁
- 身份：同校学妹，修仙世家后人
- 性格：聪明机智，外表温柔内心坚强
- 外貌：身高1.65米，容貌清丽，气质出众
- 背景：修仙世家隐藏身份，暗中观察林浩
- 能力：家传修仙功法，实力深不可测
- 关系：林浩的引路人和伙伴`
          },
          {
            name: '反派',
            description: `**黑袍修士**
- 年龄：不详
- 身份：邪修组织头目
- 性格：阴险狡诈，为达目的不择手段
- 外貌：身材高大，常穿黑袍，面容阴沉
- 背景：曾经的正道修士，因走火入魔堕入邪道
- 能力：邪修功法，实力强大
- 目标：夺取林浩的传承，称霸修仙界`
          },
          {
            name: '配角',
            description: `**张伟**
- 年龄：20岁
- 身份：林浩的室友兼好友
- 性格：幽默开朗，义气深重
- 外貌：身材微胖，笑容憨厚
- 背景：普通人，不知修仙世界存在
- 作用：提供日常生活的真实感，是林浩的精神支柱
- 发展：后期可能也会接触到修仙世界`
          }
        ],

        chapters: [
          {
            title: '第一章',
            content: `第一章 意外传承

林浩走在回宿舍的路上，夜色已深，校园里只有零星的路灯还在坚守着自己的职责。作为一名大三学生，他已经习惯了这样的夜归生活——图书馆、自习室、实验室，这就是他的日常轨迹。

"唉，明天还有高数考试。"林浩叹了口气，加快了脚步。

就在这时，一道奇异的光芒从天而降，直直地砸向了他。林浩下意识地想要躲避，但那光芒的速度实在太快，瞬间就将他包围。

一阵天旋地转后，林浩发现自己站在一个古朴的石室中。石室四壁雕刻着复杂的符文，散发着淡淡的光芒。正中央有一个石台，上面放着一本古老的书籍。

"这是什么地方？"林浩环顾四周，心中充满了疑惑。

突然，一个苍老的声音在石室中响起："有缘人，你终于来了。"

林浩吓了一跳，四处张望却没有看到任何人影。"谁？谁在说话？"

"我是这传承空间的器灵，已经等待了三千年。"声音继续说道，"你能进入这里，说明你与我主人有缘。这本《太玄真经》，便是我主人留下的修仙功法。"

"修仙？"林浩瞪大了眼睛，"你是说像小说里那样的修仙？"

"正是。这个世界远比你想象的复杂，修仙者隐藏在普通人中间，维护着世界的平衡。而你，将成为其中的一员。"

林浩半信半疑地走向石台，伸手触摸那本古书。瞬间，无数的信息涌入他的脑海——修仙的基础知识、功法口诀、术法运用...

"记住，修仙之路充满危险，但也充满机遇。善用这份传承，不要辜负了我主人的期望。"器灵的声音渐渐变弱，"传承已经完成，你可以离开了。"

当林浩再次睁开眼睛时，发现自己还站在校园的小路上，仿佛刚才的一切都是幻觉。但是，他能清晰地感受到体内多了一股暖流，那些修仙知识也深深印在了脑海中。

"这...这是真的？"林浩喃喃自语，按照脑海中的口诀，尝试运转体内的真气。

一股微弱但真实的能量在经脉中流淌，林浩的眼中闪过一丝震惊和兴奋。

"我真的获得了修仙传承！"

从这一刻起，林浩的人生彻底改变了。他不再是那个普通的大学生，而是踏上了修仙之路的修行者。

但他还不知道，在不远处的阴影中，一双眼睛正在默默观察着他...`
          },
          {
            title: '第二章',
            content: `第二章 初入修仙界

第二天一早，林浩醒来时发现昨晚的经历并非梦境。体内的真气依然存在，那些修仙知识也清晰地印在脑海中。

"伟子，你醒了吗？"林浩轻声叫着室友张伟。

"醒了醒了，你今天怎么起这么早？"张伟揉着眼睛坐起来，"不是说要复习高数吗？"

林浩苦笑一声，高数考试在获得修仙传承面前显得微不足道。但他不能告诉张伟这些，只能说："睡不着，想早点去图书馆。"

来到图书馆，林浩找了个安静的角落，开始研究脑海中的修仙知识。《太玄真经》分为九层，他现在只是刚刚入门，连第一层都没有完全掌握。

"真气运转要按照特定的路线，不能有丝毫差错..."林浩默默背诵着口诀，小心翼翼地引导体内的真气。

就在这时，一个清脆的声音在他身后响起："同学，你在修炼吗？"

林浩吓了一跳，猛地回头，看到一个容貌清丽的女孩正站在他身后，眼中带着好奇的光芒。

"你...你是谁？"林浩紧张地问道，心中暗想：难道她也是修仙者？

女孩微微一笑："我叫苏雨萱，是中文系的学妹。刚才看到你在这里打坐，很像传说中的修炼姿势，所以好奇地问一下。"

"修炼？"林浩干笑两声，"我只是在冥想，放松一下而已。"

苏雨萱点点头，但眼中的光芒并没有消失："原来如此。不过，如果你真的对修炼感兴趣的话，我倒是知道一些相关的知识。"

林浩心中一动，难道这个女孩真的知道修仙的事情？

"你知道修炼？"他试探性地问道。

苏雨萱环顾四周，确认没有其他人注意后，压低声音说："我知道你昨晚获得了传承。"

林浩瞪大了眼睛，心中掀起惊涛骇浪。这个女孩竟然知道昨晚的事情！

"你...你是什么人？"林浩警惕地问道。

"别紧张，我不是敌人。"苏雨萱轻声说道，"我来自一个修仙世家，一直在暗中观察着这个城市的修仙者动向。昨晚那道传承之光，我们早就感知到了。"

"修仙世家？"林浩感觉自己的世界观再次被颠覆。

"是的，修仙者并不像你想象的那么稀少。只是我们都隐藏在普通人中间，维护着两个世界的平衡。"苏雨萱解释道，"而你获得的那份传承，来历非常不简单。"

"什么意思？"

苏雨萱的表情变得严肃起来："那是三千年前一位大能留下的传承，名为《太玄真经》。传说中，这部功法可以让人修炼到极高的境界。但也正因为如此，它引起了很多人的觊觎。"

林浩感到一阵寒意："你是说，会有人来抢夺这份传承？"

"很有可能。"苏雨萱点头，"所以，你需要尽快提升实力，同时要学会隐藏自己的修为。"

就在这时，图书馆里突然刮起了一阵阴风，温度骤然下降。苏雨萱脸色一变："不好，邪修来了！"

林浩还没反应过来，就看到一个身穿黑袍的人影出现在图书馆门口，阴冷的目光直直地盯着他。

"找到你了，传承者。"黑袍人的声音如同来自地狱，"乖乖交出《太玄真经》，我可以给你一个痛快的死法。"

苏雨萱立刻站到林浩面前，手中出现一把闪闪发光的长剑："想要伤害他，先过我这一关！"

林浩这才意识到，自己已经卷入了一个危险的世界...`
          },
          {
            title: '第三章',
            content: `第三章 实力觉醒

图书馆里的其他学生似乎都没有注意到这边的异常，他们依然在安静地看书学习，仿佛黑袍人和苏雨萱手中的长剑都不存在。

"这是怎么回事？"林浩惊讶地问道。

"幻阵。"苏雨萱简短地回答，"在普通人眼中，我们只是在正常交谈。"

黑袍人发出阴森的笑声："苏家的小丫头，你以为凭你的实力能保护得了他吗？"

"试试就知道了！"苏雨萱娇喝一声，长剑化作一道银光直刺黑袍人。

黑袍人不慌不忙，手中出现一把黑色的镰刀，轻松挡住了苏雨萱的攻击。两人瞬间战在一起，剑光刀影在空中交错，发出阵阵金属碰撞声。

林浩看得目瞪口呆，这就是修仙者的战斗吗？速度快得他几乎看不清楚。

"小子，别发呆了！"苏雨萱在战斗中大喊，"快运转你的真气，尝试使用术法！"

"术法？我不会啊！"林浩慌张地回答。

"按照传承中的记忆，相信你的本能！"

林浩闭上眼睛，努力回想脑海中的修仙知识。突然，一段关于基础术法的记忆浮现出来——御风术，可以操控周围的气流。

他按照记忆中的方法，将真气引导到双手，然后向前推出。一股无形的风压向黑袍人袭去。

黑袍人正在和苏雨萱激战，没想到林浩会突然出手，被这股风压击中，身形一个踉跄。

"不错嘛，刚获得传承就能使用术法。"黑袍人稳住身形，眼中闪过一丝贪婪，"看来这份传承比我想象的还要珍贵。"

苏雨萱趁机发动攻击，长剑上闪烁着淡蓝色的光芒，威力比刚才强了数倍。

"冰霜剑气！"

剑气如同冰龙一般咆哮着冲向黑袍人，所过之处空气都结成了冰晶。

黑袍人脸色一变，连忙举起镰刀抵挡。但这一击的威力超出了他的预料，整个人被击飞出去，撞在图书馆的墙上。

"可恶！"黑袍人从地上爬起来，身上的黑袍已经破损，"苏家的冰霜剑诀果然名不虚传。不过，今天我是不会空手而归的！"

他突然从怀中掏出一个黑色的珠子，用力捏碎。瞬间，一股浓郁的黑雾从珠子中涌出，整个图书馆都被黑雾笼罩。

"不好，是魔气珠！"苏雨萱脸色大变，"林浩，快跟我走！"

她拉着林浩向图书馆外跑去，但黑雾中传来黑袍人阴森的笑声："想跑？没那么容易！"

无数黑色的触手从雾气中伸出，向两人抓去。苏雨萱挥剑斩断几根触手，但数量实在太多，很快就被包围了。

就在这危急时刻，林浩体内的真气突然暴涨，一股金色的光芒从他身上散发出来。那些黑色触手一碰到金光就立刻消散，仿佛遇到了天敌。

"这是...太玄真气？"黑袍人的声音中带着震惊，"怎么可能，你才刚刚获得传承！"

林浩自己也很惊讶，他能感受到体内有一股强大的力量在涌动，仿佛要冲破身体的束缚。

"我感觉...我感觉我能做到更多！"林浩双手结印，按照脑海中突然浮现的记忆，大喝一声："太玄净世咒！"

一道更加耀眼的金光从他身上爆发，瞬间驱散了所有的黑雾。黑袍人发出一声惨叫，身形在金光中变得虚幻起来。

"不可能...这种程度的太玄真气...你到底是什么人？"黑袍人惊恐地看着林浩，"我记住你了，小子。下次见面，我一定要得到你的传承！"

说完，他的身影彻底消散在空气中。

图书馆恢复了平静，其他学生依然在安静地看书，仿佛刚才的战斗从未发生过。

苏雨萱看着林浩，眼中满是震惊："你...你的天赋比我想象的还要强。刚才那个术法，就连我也做不到。"

林浩感到一阵虚脱，刚才爆发的力量让他消耗很大："我也不知道怎么回事，就是感觉体内有股力量要爆发出来。"

"看来这份传承选择你是有原因的。"苏雨萱若有所思地说道，"不过，今天的事情只是开始。那个黑袍人不会善罢甘休的，你需要尽快提升实力。"

林浩点点头，他已经意识到自己踏入了一个全新的世界，一个充满危险但也充满可能的世界。

而这，只是他修仙之路的开始...`
          }
        ],

        metadata: {
          title: createForm.value.title || '测试小说',
          genre: createForm.value.genre || '都市',
          category: createForm.value.category || '男频',
          createdAt: new Date().toISOString()
        }
      }

      console.log('Test result set:', creationResult.value)
      ElMessage.success('测试数据已加载，请查看创作结果')
    }

    // 重置表单
    const resetForm = () => {
      createForm.value = {
        title: '',
        genre: '',
        category: '',
        requirements: '',
        creativity: '',
        chapterCount: 100,
        wordsPerChapter: 3000
      }
      
      ElMessage({
        message: '表单已重置',
        type: 'info'
      })
    }

    // 生成指定卷的详细章节概要
    const generateVolumeChapters = async (volumeNumber) => {
      try {
        const chaptersPerVolume = 100
        const startChapter = (volumeNumber - 1) * chaptersPerVolume + 1
        const endChapter = Math.min(volumeNumber * chaptersPerVolume, creationResult.value.volumeInfo.totalChapters)

        ElMessage.info(`正在生成第${volumeNumber}卷（第${startChapter}-${endChapter}章）的详细概要...`)

        const chapters = await aiCreationService.generateVolumeChapters(
          createForm.value,
          volumeNumber,
          startChapter,
          endChapter,
          (partialContent) => {
            console.log(`Volume ${volumeNumber} chapters generation progress:`, partialContent.length, 'characters')
          }
        )

        volumeChapters.value = {
          volumeNumber,
          startChapter,
          endChapter,
          content: chapters
        }

        ElMessage.success(`第${volumeNumber}卷章节概要生成完成！`)

        // 切换到章节概要标签页
        activeResultTab.value = 'chapters'

      } catch (error) {
        console.error('生成卷章节失败:', error)
        ElMessage.error(`生成第${volumeNumber}卷章节失败: ${error.message}`)
      }
    }

    // 生成单章内容
    const generateSingleChapter = async () => {
      if (!chapterForm.value.chapterTitle) {
        ElMessage.warning('请输入章节标题')
        return
      }

      // 检查是否已完成创作准备工作
      if (!creationResult.value) {
        ElMessage.error('请先完成创作准备工作（生成大纲和角色设定）')
        return
      }

      // 检查记忆系统是否已初始化
      if (!aiCreationService.memoryDatabase) {
        ElMessage.warning('记忆系统未初始化，正在初始化...')

        // 尝试重新初始化记忆系统
        try {
          aiCreationService.initializeProject(createForm.value)
          if (aiCreationService.memoryDatabase) {
            aiCreationService.memoryDatabase.initializeFromPreparation({
              outline: creationResult.value.outline,
              characters: creationResult.value.characters,
              creativity: createForm.value.creativity,
              metadata: creationResult.value.metadata
            })
          }
        } catch (error) {
          console.error('记忆系统初始化失败:', error)
          ElMessage.error('记忆系统初始化失败，请重新生成大纲')
          return
        }
      }

      try {
        isGeneratingChapter.value = true
        currentChapterContent.value = ''

        ElMessage.info(`正在生成第${chapterForm.value.chapterNumber}章...`)

        const result = await aiCreationService.generateChapterWithMemory(
          chapterForm.value.chapterNumber,
          chapterForm.value.chapterTitle,
          (partialContent) => {
            // 处理流式输出，可能是字符串或对象
            if (typeof partialContent === 'string') {
              currentChapterContent.value = partialContent
            } else if (partialContent.content) {
              currentChapterContent.value = partialContent.content
            }
          }
        )

        // 处理返回结果
        if (typeof result === 'string') {
          currentChapterContent.value = result
          currentConsistencyIssues.value = []
        } else {
          currentChapterContent.value = result.content
          currentConsistencyIssues.value = result.consistencyIssues || []
        }

        chapterGeneratedAt.value = new Date().toLocaleString()

        const issueCount = currentConsistencyIssues.value.length
        if (issueCount > 0) {
          ElMessage.warning(`第${chapterForm.value.chapterNumber}章生成完成！发现${issueCount}个一致性问题，建议进行深度审核`)
        } else {
          ElMessage.success(`第${chapterForm.value.chapterNumber}章生成完成！未发现一致性问题`)
        }

      } catch (error) {
        console.error('章节生成失败:', error)
        ElMessage.error(`章节生成失败: ${error.message}`)
      } finally {
        isGeneratingChapter.value = false
      }
    }

    // 确认章节
    const confirmChapter = () => {
      ElMessage.success('章节已确认，可以继续生成下一章')

      // 自动设置下一章
      chapterForm.value.chapterNumber += 1
      chapterForm.value.chapterTitle = ''
      currentChapterContent.value = ''
    }

    // 重新生成章节
    const regenerateChapter = () => {
      ElMessageBox.confirm('确定要重新生成这一章吗？', '确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        generateSingleChapter()
      })
    }

    // 编辑章节
    const editChapter = () => {
      ElMessage.info('编辑功能开发中...')
    }

    // 开始深度审核
    const startDeepReview = () => {
      if (!currentChapterContent.value) {
        ElMessage.warning('没有章节内容可供审核')
        return
      }

      // 获取最新的记忆库数据
      if (aiCreationService.memoryDatabase) {
        memoryData.value = aiCreationService.memoryDatabase.export()
      }

      showDeepReview.value = true
      ElMessage.info('开始深度审核，建议投入30分钟仔细检查')
    }

    // 快速通过（跳过深度审核）
    const quickApprove = () => {
      ElMessageBox.confirm('确定要跳过深度审核直接通过吗？', '快速通过', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        confirmChapter()
        ElMessage.success('章节已快速通过')
      })
    }

    // 处理章节审核通过
    const handleChapterApprove = (reviewData) => {
      console.log('章节审核通过:', reviewData)

      // 记录审核历史
      reviewHistory.value.push({
        chapterNumber: chapterForm.value.chapterNumber,
        chapterTitle: chapterForm.value.chapterTitle,
        result: 'approved',
        ratings: reviewData.ratings,
        notes: reviewData.notes,
        reviewTime: reviewData.reviewTime,
        timestamp: new Date().toISOString()
      })

      showDeepReview.value = false
      confirmChapter()

      ElMessage.success(`第${chapterForm.value.chapterNumber}章审核通过！质量评分：${Math.round((reviewData.ratings.plot + reviewData.ratings.character + reviewData.ratings.writing + reviewData.ratings.creativity + reviewData.ratings.overall) / 5 * 10)}/100`)
    }

    // 处理章节审核拒绝
    const handleChapterReject = (reviewData) => {
      console.log('章节审核拒绝:', reviewData)

      // 记录审核历史
      reviewHistory.value.push({
        chapterNumber: chapterForm.value.chapterNumber,
        chapterTitle: chapterForm.value.chapterTitle,
        result: 'rejected',
        reason: reviewData.reason,
        ratings: reviewData.ratings,
        timestamp: new Date().toISOString()
      })

      showDeepReview.value = false

      ElMessageBox.confirm('章节已被拒绝，是否重新生成？', '章节拒绝', {
        confirmButtonText: '重新生成',
        cancelButtonText: '稍后处理',
        type: 'warning'
      }).then(() => {
        regenerateChapter()
      })
    }

    // 处理章节修改要求
    const handleChapterRevise = (reviewData) => {
      console.log('要求章节修改:', reviewData)

      ElMessage.info('修改建议已记录，请手动编辑或重新生成')
      showDeepReview.value = false
    }

    // 处理内容更新
    const handleContentUpdate = (newContent) => {
      currentChapterContent.value = newContent
      ElMessage.success('章节内容已更新')
    }

    // 查看记忆库
    const viewMemoryDatabase = () => {
      if (aiCreationService.memoryDatabase) {
        memoryData.value = aiCreationService.memoryDatabase.export()
        showMemoryViewer.value = true
      } else {
        ElMessage.warning('记忆库未初始化，请先生成大纲')
      }
    }

    // 获取字数建议提示
    const getWordCountTip = (wordCount) => {
      if (!wordCount) return '请设置章节字数'

      if (wordCount < 2000) {
        return '偏短，适合快节奏阅读'
      } else if (wordCount <= 3000) {
        return '标准长度，平衡节奏'
      } else if (wordCount <= 5000) {
        return '较长，内容丰富'
      } else if (wordCount <= 6000) {
        return '很长，详细描述'
      } else {
        return '超长，需要更多时间生成'
      }
    }

    // 获取章节数量建议提示
    const getChapterCountTip = (chapterCount) => {
      if (!chapterCount) return '请设置总章节数'

      const totalWords = chapterCount * (createForm.value.wordsPerChapter || 3000)
      const totalWordsText = `预计总字数：${Math.round(totalWords / 10000)}万字`

      if (chapterCount <= 50) {
        return `短篇小说，${totalWordsText}`
      } else if (chapterCount <= 200) {
        return `中篇小说，${totalWordsText}`
      } else if (chapterCount <= 500) {
        return `长篇小说，${totalWordsText}`
      } else if (chapterCount <= 1000) {
        return `超长篇网文，${totalWordsText}`
      } else {
        return `史诗级巨著，${totalWordsText}`
      }
    }

    // 组件挂载时检查AI配置
    onMounted(() => {
      checkAIConfig()
    })

    return {
      isConfigured,
      isCreating,
      isTesting,
      createProgress,
      currentStepIndex,
      currentStep,
      creationSteps,
      progressColor,
      creationResult,
      activeResultTab,
      createForm,
      aiStatusClass,
      aiStatusText,
      aiStatusDesc,
      canCreate,
      progressText,
      startAutoCreate,
      resetForm,
      saveProject,
      exportResult,
      continueEdit,
      checkAIConfig,
      refreshAIConfig,
      showTestResult,
      testAPIOutput,
      getWordCountTip,
      getChapterCountTip,
      showVolumeDialog,
      selectedVolume,
      volumeChapters,
      generateVolumeChapters,
      showChapterGenerator,
      isGeneratingChapter,
      chapterForm,
      currentChapterContent,
      chapterGeneratedAt,
      currentConsistencyIssues,
      showMemoryViewer,
      activeMemoryTab,
      memoryData,
      generateSingleChapter,
      confirmChapter,
      regenerateChapter,
      editChapter,
      viewMemoryDatabase,
      showDeepReview,
      reviewHistory,
      startDeepReview,
      quickApprove,
      handleChapterApprove,
      handleChapterReject,
      handleChapterRevise,
      handleContentUpdate
    }
  }
}
</script>

<style scoped>
/* 引入水墨书香主题 */
@import '@/assets/styles/modern-theme.css';

.ink-auto-create {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0;
}

/* 页面标题 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-3xl);
  padding: var(--spacing-xl);
  background: var(--gradient-paper);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-paper-md);
  position: relative;
  overflow: hidden;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--gradient-gold);
  opacity: 0.8;
}

.header-content {
  flex: 1;
}

.page-title {
  font-family: var(--font-calligraphy);
  font-size: 36px;
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-sm) 0;
  letter-spacing: 0.05em;
}

.page-subtitle {
  font-family: var(--font-elegant);
  font-size: 16px;
  color: var(--text-muted);
  margin: 0;
}

.header-decoration {
  width: 80px;
  height: 80px;
  background: var(--gradient-gold);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-paper-md);
}

.decoration-icon {
  font-size: 32px;
  color: var(--ink-jiao);
}

/* AI状态卡片 */
.ai-status-card {
  background: var(--bg-primary);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-3xl);
  box-shadow: var(--shadow-paper-md);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.status-indicator.configured {
  color: var(--song-lv);
}

.status-indicator.not-configured {
  color: var(--huang-jin);
}

.status-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.status-indicator.configured .status-icon {
  background: var(--song-lv);
  color: var(--paper-xuan);
}

.status-indicator.not-configured .status-icon {
  background: var(--huang-jin);
  color: var(--ink-jiao);
}

.status-info {
  display: flex;
  flex-direction: column;
}

.status-title {
  font-family: var(--font-elegant);
  font-size: 16px;
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-xs);
}

.status-desc {
  font-family: var(--font-elegant);
  font-size: 13px;
  opacity: 0.8;
}

.config-notice {
  background: rgba(212, 175, 55, 0.05);
  border: 1px solid rgba(212, 175, 55, 0.2);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  text-align: center;
}

.config-notice p {
  font-family: var(--font-elegant);
  color: var(--text-muted);
  margin: 0 0 var(--spacing-md) 0;
}

.config-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  margin-top: var(--spacing-md);
}

.refresh-btn {
  background: #409eff;
  color: white;
  border: 2px solid #409eff;
  border-radius: var(--radius-lg);
  padding: var(--spacing-sm) var(--spacing-lg);
  font-family: var(--font-elegant);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-paper);
}

.refresh-btn:hover {
  background: #337ecc;
  border-color: #337ecc;
  transform: translateY(-1px);
}

.config-btn {
  background: var(--huang-jin);
  color: var(--ink-jiao);
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--spacing-sm) var(--spacing-lg);
  font-family: var(--font-elegant);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-paper);
}

.config-btn:hover {
  background: #f1c40f;
  transform: translateY(-1px);
}

/* 创作表单 */
.create-form-section {
  margin-bottom: var(--spacing-3xl);
}

.form-card {
  background: var(--bg-primary);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-xl);
  padding: var(--spacing-3xl);
  box-shadow: var(--shadow-paper-md);
}

.form-title {
  font-family: var(--font-calligraphy);
  font-size: 24px;
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xl) 0;
  text-align: center;
}

.create-form {
  margin-bottom: var(--spacing-xl);
}

.form-hint {
  margin-left: var(--spacing-md);
  font-family: var(--font-elegant);
  font-size: 12px;
  color: var(--text-muted);
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: var(--spacing-lg);
}

.create-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg) var(--spacing-xl);
  border: 2px solid;
  border-radius: var(--radius-lg);
  font-family: var(--font-elegant);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-paper);
}

.create-btn.primary {
  background: var(--gradient-gold);
  border-color: var(--huang-jin);
  color: var(--ink-jiao);
}

.create-btn.primary:hover:not(:disabled) {
  background: #f1c40f;
  transform: translateY(-2px);
  box-shadow: var(--shadow-paper-lg);
}

.create-btn.secondary {
  background: var(--bg-primary);
  border-color: var(--border-medium);
  color: var(--text-muted);
}

.create-btn.secondary:hover {
  border-color: var(--border-accent);
  color: var(--text-primary);
}

.create-btn.test {
  background: #67c23a;
  color: white;
  border-color: #67c23a;
}

.create-btn.test:hover {
  background: #5daf34;
  border-color: #5daf34;
}

.create-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

/* 创作进度 */
.progress-section {
  margin-bottom: var(--spacing-3xl);
}

.progress-card {
  background: var(--bg-primary);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  text-align: center;
  box-shadow: var(--shadow-paper-md);
}

.progress-title {
  font-family: var(--font-calligraphy);
  font-size: 20px;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-lg) 0;
}

.progress-content {
  max-width: 400px;
  margin: 0 auto;
}

.progress-text {
  font-family: var(--font-elegant);
  color: var(--text-muted);
  margin: var(--spacing-md) 0 0 0;
}

/* 功能介绍 */
.features-section {
  background: var(--gradient-paper);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-xl);
  padding: var(--spacing-3xl);
  box-shadow: var(--shadow-paper-md);
}

.features-title {
  font-family: var(--font-calligraphy);
  font-size: 24px;
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  text-align: center;
  margin: 0 0 var(--spacing-xl) 0;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-xl);
}

.feature-card {
  text-align: center;
  padding: var(--spacing-lg);
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  transition: all var(--duration-normal) var(--ease-paper);
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-paper-md);
}

.feature-icon {
  width: 48px;
  height: 48px;
  background: var(--huang-jin);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--spacing-md);
  font-size: 20px;
  color: var(--ink-jiao);
}

.feature-card h3 {
  font-family: var(--font-calligraphy);
  font-size: 16px;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-sm) 0;
}

.feature-card p {
  font-family: var(--font-elegant);
  font-size: 13px;
  color: var(--text-muted);
  margin: 0;
  line-height: 1.5;
}

/* 创作步骤样式 */
.creation-steps {
  display: flex;
  justify-content: space-between;
  margin-top: var(--spacing-xl);
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--border-light);
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
  flex: 1;
  position: relative;
}

.step-item:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 20px;
  right: -50%;
  width: 100%;
  height: 2px;
  background: var(--border-light);
  z-index: 1;
}

.step-item.completed:not(:last-child)::after {
  background: #67c23a;
}

.step-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  position: relative;
  z-index: 2;
  transition: all var(--duration-normal);
}

.step-item.pending .step-icon {
  background: var(--bg-secondary);
  color: var(--text-muted);
  border: 2px solid var(--border-light);
}

.step-item.active .step-icon {
  background: #409eff;
  color: white;
  border: 2px solid #409eff;
  animation: pulse 2s infinite;
}

.step-item.completed .step-icon {
  background: #67c23a;
  color: white;
  border: 2px solid #67c23a;
}

.step-text {
  font-size: 12px;
  color: var(--text-muted);
  text-align: center;
}

.step-item.active .step-text {
  color: #409eff;
  font-weight: 500;
}

.step-item.completed .step-text {
  color: #67c23a;
  font-weight: 500;
}

/* 结果展示样式 */
.result-section {
  margin-top: var(--spacing-3xl);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing-lg);
  border-bottom: 1px solid var(--border-light);
}

.result-title {
  font-size: 24px;
  margin: 0;
}

.result-actions {
  display: flex;
  gap: var(--spacing-md);
}

.result-content {
  margin-top: var(--spacing-lg);
}

.content-panel {
  padding: var(--spacing-lg);
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  margin-top: var(--spacing-md);
}

.content-text {
  white-space: pre-wrap;
  line-height: 1.8;
  color: var(--text-primary);
  font-family: var(--font-content);
  margin: 0;
}

.character-card,
.chapter-card {
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-lg);
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
}

.character-card h4,
.chapter-card h4 {
  color: var(--text-primary);
  margin: 0 0 var(--spacing-md) 0;
  font-size: 16px;
  font-weight: 600;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--spacing-md);
}

.progress-percent {
  font-weight: bold;
  color: #409eff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: var(--spacing-lg);
    text-align: center;
  }

  .form-actions {
    flex-direction: column;
    align-items: center;
  }

  .create-btn {
    width: 100%;
    max-width: 200px;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .creation-steps {
    flex-wrap: wrap;
    gap: var(--spacing-md);
  }

  .step-item:not(:last-child)::after {
    display: none;
  }

  .result-header {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }

  .result-actions {
    justify-content: center;
  }
}

/* 创意输入样式 */
.creativity-input {
  border: 2px dashed var(--primary-color);
  border-radius: var(--border-radius-lg);
  background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);
}

.creativity-hint {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-sm);
  padding: var(--spacing-sm);
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border-radius: var(--border-radius-md);
  font-size: 0.9em;
  color: #856404;
}

.creativity-hint .el-icon {
  color: #f39c12;
  font-size: 1.2em;
}

/* 分卷规划样式 */
.layered-info {
  padding: var(--spacing-lg);
}

.volume-summary {
  background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  border: 1px solid var(--border-color);
}

.volume-summary h4 {
  color: var(--primary-color);
  margin-bottom: var(--spacing-lg);
  font-size: 1.2em;
  font-weight: 600;
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.stat-item {
  background: white;
  padding: var(--spacing-md);
  border-radius: var(--border-radius-md);
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-label {
  color: var(--text-secondary);
  font-size: 0.9em;
}

.stat-value {
  color: var(--primary-color);
  font-weight: 600;
  font-size: 1.1em;
}

.volume-message {
  margin-bottom: var(--spacing-lg);
}

.volume-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
}

/* 逐章生成样式 */
.chapter-generator {
  padding: var(--spacing-lg);
}

.generator-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.generator-header h3 {
  color: var(--primary-color);
  margin-bottom: var(--spacing-sm);
}

.chapter-input {
  background: #f8f9fa;
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-lg);
  margin-bottom: var(--spacing-lg);
}

.generator-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  margin-bottom: var(--spacing-lg);
}

.chapter-result {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
}

.result-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: var(--spacing-lg);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.result-stats {
  display: flex;
  gap: var(--spacing-md);
  font-size: 0.9em;
}

.chapter-content {
  max-height: 400px;
  overflow-y: auto;
  padding: var(--spacing-lg);
  background: white;
}

.chapter-content pre {
  white-space: pre-wrap;
  font-family: 'Microsoft YaHei', sans-serif;
  line-height: 1.8;
  margin: 0;
}

.chapter-actions {
  padding: var(--spacing-lg);
  background: #f8f9fa;
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
}

/* 记忆库样式 */
.memory-viewer {
  padding: var(--spacing-lg);
}

.memory-section {
  max-height: 400px;
  overflow-y: auto;
}

.character-item, .foreshadow-item {
  background: white;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.character-item h4, .foreshadow-item h4 {
  color: var(--primary-color);
  margin-bottom: var(--spacing-sm);
}

@media (max-width: 768px) {
  .summary-stats {
    grid-template-columns: 1fr;
  }

  .volume-actions {
    flex-direction: column;
  }

  .generator-actions {
    flex-direction: column;
  }

  .chapter-actions {
    flex-direction: column;
  }

  .result-header {
    flex-direction: column;
    gap: var(--spacing-sm);
    text-align: center;
  }
}

/* 深度审核样式 */
.deep-review-dialog {
  --el-dialog-padding-primary: 0;
}

.chapter-preview {
  margin: var(--spacing-lg) 0;
  padding: var(--spacing-md);
  background: #f8f9fa;
  border-radius: var(--border-radius-md);
  border-left: 4px solid var(--primary-color);
}

.chapter-preview h5 {
  margin-bottom: var(--spacing-sm);
  color: var(--primary-color);
}

.preview-content {
  max-height: 200px;
  overflow-y: auto;
}

.preview-content pre {
  white-space: pre-wrap;
  font-family: 'Microsoft YaHei', sans-serif;
  line-height: 1.6;
  margin: 0;
  color: #333;
}
</style>
