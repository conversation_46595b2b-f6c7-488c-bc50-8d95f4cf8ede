<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>侧边栏测试</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: #f5f5f5;
        }
        .test-info {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
        }
        .test-info h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .test-info ul {
            margin: 0;
            padding-left: 20px;
        }
        .test-info li {
            margin: 5px 0;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="test-info">
        <h3>侧边栏功能测试</h3>
        <ul>
            <li>✅ 深色主题背景</li>
            <li>✅ 用户头像和信息</li>
            <li>✅ 展开/收起功能</li>
            <li>✅ 菜单项切换</li>
            <li>✅ SVG图标集成</li>
            <li>✅ 悬停效果</li>
            <li>✅ 响应式设计</li>
        </ul>
    </div>
    
    <div id="app"></div>
    <script type="module" src="/src/main.js"></script>
</body>
</html>
