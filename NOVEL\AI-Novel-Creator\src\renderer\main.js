// 检测是否在Electron环境中
const isElectron = typeof window !== 'undefined' && window.process && window.process.type

// 动态导入以解决Electron中的模块解析问题
async function initApp() {
  try {
    console.log('Starting Vue app initialization...')

    // 动态导入所有依赖
    const { createApp } = await import('vue')
    const ElementPlus = await import('element-plus')
    const ElementPlusIconsVue = await import('@element-plus/icons-vue')

    // 导入样式
    await import('element-plus/dist/index.css')
    await import('./assets/styles/modern-theme.css')

    // 导入路由和App组件
    const { default: router } = await import('./router/index.js')
    const { default: App } = await import('./App.vue')

    console.log('All modules imported successfully, creating Vue app...')

    // 创建Vue应用
    const app = createApp(App)

    // 注册所有图标
    for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
      app.component(key, component)
    }

    // 使用插件
    app.use(router)
    app.use(ElementPlus.default || ElementPlus)

    // 挂载应用
    app.mount('#app')

    // 等待DOM挂载完成后再处理路由
    await new Promise(resolve => setTimeout(resolve, 100))

    // 确保路由正确初始化
    await router.isReady()

    // 强制导航到dashboard
    console.log('App mounted, current route path:', router.currentRoute.value.path)

    // 使用nextTick确保在下一个tick中导航
    await new Promise(resolve => {
      router.push('/dashboard').then(() => {
        console.log('Navigation completed, current route:', router.currentRoute.value.path)
        resolve()
      })
    })

    console.log('Vue app initialization successful! Final route:', router.currentRoute.value.path)

  } catch (error) {
    console.error('Vue app initialization failed:', error)

    // 显示错误信息
    document.getElementById('app').innerHTML = `
      <div style="padding: 40px; text-align: center; font-family: 'Microsoft YaHei', Arial, sans-serif; background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); min-height: 100vh;">
        <div style="max-width: 600px; margin: 0 auto; background: white; padding: 40px; border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
          <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; color: white; font-size: 32px; font-weight: bold;">墨</div>
          <h1 style="color: #e74c3c; margin-bottom: 20px;">应用初始化失败</h1>
          <p style="color: #7f8c8d; margin-bottom: 20px; line-height: 1.6;">
            Vue模块加载遇到问题。请尝试在浏览器中访问应用。
          </p>
          <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: left;">
            <h3 style="color: #2c3e50; margin-bottom: 10px;">错误信息：</h3>
            <code style="color: #e74c3c; font-size: 14px;">${error.message}</code>
          </div>
          <div style="margin-top: 30px;">
            <button onclick="window.location.reload()" style="background: linear-gradient(45deg, #667eea, #764ba2); color: white; border: none; padding: 15px 30px; border-radius: 8px; cursor: pointer; font-size: 16px; margin-right: 10px;">
              重新加载
            </button>
            <button onclick="window.open('http://localhost:3000')" style="background: linear-gradient(45deg, #4facfe, #00f2fe); color: white; border: none; padding: 15px 30px; border-radius: 8px; cursor: pointer; font-size: 16px;">
              在浏览器中打开
            </button>
          </div>
        </div>
      </div>
    `
  }
}

// 页面加载完成后初始化应用
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initApp)
} else {
  initApp()
}