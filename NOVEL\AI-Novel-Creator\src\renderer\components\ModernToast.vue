<template>
  <Transition name="toast" appear>
    <div 
      v-if="visible"
      class="modern-toast glass-card"
      :class="[`toast-${type}`, { 'toast-closable': closable }]"
    >
      <div class="toast-icon floating">
        <el-icon>
          <component :is="iconComponent" />
        </el-icon>
      </div>
      
      <div class="toast-content">
        <h4 v-if="title" class="toast-title gradient-text">{{ title }}</h4>
        <p class="toast-message">{{ message }}</p>
      </div>
      
      <button 
        v-if="closable"
        class="toast-close neumorphism-btn"
        @click="close"
      >
        <el-icon><Close /></el-icon>
      </button>
      
      <div 
        v-if="duration > 0"
        class="toast-progress"
        :style="{ animationDuration: duration + 'ms' }"
      ></div>
    </div>
  </Transition>
</template>

<script>
import { ref, computed, onMounted } from 'vue'

export default {
  name: 'ModernToast',
  props: {
    type: {
      type: String,
      default: 'info',
      validator: (value) => ['success', 'warning', 'error', 'info'].includes(value)
    },
    title: {
      type: String,
      default: ''
    },
    message: {
      type: String,
      required: true
    },
    duration: {
      type: Number,
      default: 3000
    },
    closable: {
      type: Boolean,
      default: true
    }
  },
  emits: ['close'],
  setup(props, { emit }) {
    const visible = ref(false)

    const iconComponent = computed(() => {
      const icons = {
        success: 'CircleCheck',
        warning: 'WarningFilled',
        error: 'CircleClose',
        info: 'InfoFilled'
      }
      return icons[props.type]
    })

    const close = () => {
      visible.value = false
      setTimeout(() => {
        emit('close')
      }, 300)
    }

    onMounted(() => {
      visible.value = true
      
      if (props.duration > 0) {
        setTimeout(() => {
          close()
        }, props.duration)
      }
    })

    return {
      visible,
      iconComponent,
      close
    }
  }
}
</script>

<style scoped>
/* 引入水墨书香主题 */
@import '@/assets/styles/modern-theme.css';

.modern-toast {
  position: relative;
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  min-width: 320px;
  max-width: 480px;
  background: var(--glass-heavy);
  backdrop-filter: var(--backdrop-blur);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-paper-lg);
  overflow: hidden;
}

.toast-icon {
  flex-shrink: 0;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: white;
}

.toast-success .toast-icon {
  background: var(--gradient-jade);
}

.toast-warning .toast-icon {
  background: var(--gradient-gold);
}

.toast-error .toast-icon {
  background: linear-gradient(135deg, var(--zhu-sha) 0%, #e74c3c 100%);
}

.toast-info .toast-icon {
  background: var(--gradient-ocean);
}

.toast-content {
  flex: 1;
  min-width: 0;
}

.toast-title {
  font-family: var(--font-calligraphy);
  font-size: 16px;
  font-weight: var(--font-weight-semibold);
  margin: 0 0 var(--spacing-xs) 0;
  line-height: 1.2;
}

.toast-message {
  font-family: var(--font-elegant);
  font-size: 14px;
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.4;
}

.toast-close {
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: var(--text-muted);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-paper);
}

.toast-close:hover {
  color: var(--text-primary);
  transform: scale(1.1);
}

.toast-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background: var(--gradient-gold);
  border-radius: 0 0 var(--radius-xl) var(--radius-xl);
  animation: toast-progress linear forwards;
}

@keyframes toast-progress {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}

/* 过渡动画 */
.toast-enter-active,
.toast-leave-active {
  transition: all var(--duration-normal) var(--ease-elegant);
}

.toast-enter-from {
  opacity: 0;
  transform: translateX(100%) scale(0.8);
}

.toast-leave-to {
  opacity: 0;
  transform: translateX(100%) scale(0.8);
}

/* 不同类型的边框装饰 */
.toast-success::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--gradient-jade);
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

.toast-warning::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--gradient-gold);
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

.toast-error::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(135deg, var(--zhu-sha) 0%, #e74c3c 100%);
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

.toast-info::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--gradient-ocean);
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .modern-toast {
    min-width: 280px;
    max-width: calc(100vw - 32px);
    padding: var(--spacing-md);
  }
  
  .toast-title {
    font-size: 14px;
  }
  
  .toast-message {
    font-size: 13px;
  }
}
</style>
