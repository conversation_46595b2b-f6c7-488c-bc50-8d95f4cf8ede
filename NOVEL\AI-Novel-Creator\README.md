# AI网文小说创作助手

基于Google Gemini AI的智能网文小说创作工具，支持全自动创作和智能辅助写作。

## 功能特色

### 🤖 全自动创作
- 输入题材、类型、子类型等参数
- 自动生成完整小说大纲
- 智能创作章节内容
- 支持多种网文类型

### ✍️ 智能写作助手
- 选中文本智能续写
- 文笔优化建议
- 情节发展建议
- 人物对话优化
- 场景描写增强

### 📊 项目管理
- 多项目管理
- 章节管理
- 字数统计
- 创作进度追踪

### 🎨 用户界面
- 现代化设计
- 响应式布局
- 深色/浅色主题
- 自定义字体设置

## 技术栈

- **前端**: Vue.js 3 + Element Plus
- **桌面端**: Electron
- **状态管理**: Pinia
- **AI模型**: Google Gemini API
- **数据库**: SQLite
- **构建工具**: Vite

## 快速开始

### 环境要求
- Node.js 18+
- npm 或 yarn
- Google Gemini API Key

### 安装依赖
```bash
npm install
```

### 配置环境变量
```bash
cp .env.example .env
```

编辑 `.env` 文件，设置你的 Google Gemini API Key：
```
GEMINI_API_KEY=your_api_key_here
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 打包桌面应用
```bash
npm run build:electron
```

## 使用指南

### 1. 配置设置
- 在设置页面输入 Google Gemini API Key
- 配置自动保存、主题等个人偏好

### 2. 创建项目
- 在项目管理页面创建新项目
- 选择题材、类型、子类型等参数

### 3. 全自动创作
- 配置创作参数（总章数、单章字数等）
- 生成故事大纲
- 开始自动创作

### 4. 智能编辑
- 使用编辑器进行写作
- 利用AI助手优化文本
- 获取情节建议

## 支持的网文类型

### 男频小说
- 玄幻：修仙、异世大陆、东方玄幻
- 都市：都市生活、都市重生、都市异能
- 科幻：星际战争、时空穿梭、机甲
- 历史：架空历史、穿越重生
- 军事：抗战烽火、现代战争

### 女频小说
- 现代言情：甜宠、虐恋、豪门
- 古代言情：宫廷、江湖、种田
- 玄幻言情：修仙、异世、魔法
- 校园青春：青春校园、都市青春

## 开发计划

- [x] 基础项目架构
- [x] 用户界面设计
- [x] 项目管理功能
- [ ] Google Gemini API集成
- [ ] 全自动创作功能
- [ ] 智能辅助功能
- [ ] 数据库集成
- [ ] 跨平台打包

## 贡献指南

欢迎提交 Issue 和 Pull Request！

## 许可证

MIT License

## 联系我们

如有问题或建议，请联系技术支持。