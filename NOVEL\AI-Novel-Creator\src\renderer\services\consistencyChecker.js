// 智能一致性检查器
export class ConsistencyChecker {
  constructor(memoryDatabase) {
    this.memoryDatabase = memoryDatabase
    this.issues = []
  }

  // 执行完整的一致性检查
  async performFullCheck(chapterNumber, chapterContent) {
    this.issues = []
    
    console.log(`🔍 开始第${chapterNumber}章一致性检查`)
    
    // 角色一致性检查
    await this.checkCharacterConsistency(chapterNumber, chapterContent)
    
    // 情节一致性检查
    await this.checkPlotConsistency(chapterNumber, chapterContent)
    
    // 世界观一致性检查
    await this.checkWorldConsistency(chapterNumber, chapterContent)
    
    // 时间线一致性检查
    await this.checkTimelineConsistency(chapterNumber, chapterContent)

    // 章节连贯性检查（重点）
    await this.checkChapterContinuity(chapterNumber, chapterContent)

    console.log(`✅ 一致性检查完成，发现${this.issues.length}个问题`)
    return this.issues
  }

  // 角色一致性检查
  async checkCharacterConsistency(chapterNumber, chapterContent) {
    const characters = this.memoryDatabase.characters
    
    for (const [name, character] of characters) {
      if (chapterContent.includes(name)) {
        // 检查角色状态一致性
        await this.checkCharacterStatus(name, character, chapterNumber, chapterContent)
        
        // 检查角色性格一致性
        await this.checkCharacterPersonality(name, character, chapterNumber, chapterContent)
        
        // 检查角色能力一致性
        await this.checkCharacterAbilities(name, character, chapterNumber, chapterContent)
      }
    }
  }

  // 检查角色状态一致性
  async checkCharacterStatus(name, character, chapterNumber, chapterContent) {
    // 检查生死状态
    if (character.status === 'dead') {
      if (this.isCharacterActive(name, chapterContent)) {
        this.addIssue({
          type: 'character_status',
          severity: 'high',
          title: `角色${name}状态矛盾`,
          description: `${name}在记忆中已死亡，但在第${chapterNumber}章中仍然活跃`,
          suggestion: `检查${name}的死亡时间，或修改当前章节内容`,
          chapterNumber,
          characterName: name
        })
      }
    }

    // 检查年龄一致性
    if (character.age) {
      const ageInContent = this.extractAge(name, chapterContent)
      if (ageInContent && Math.abs(ageInContent - character.age) > 2) {
        this.addIssue({
          type: 'character_age',
          severity: 'medium',
          title: `${name}年龄不一致`,
          description: `记忆中${name}是${character.age}岁，但本章提到${ageInContent}岁`,
          suggestion: `统一${name}的年龄设定`,
          chapterNumber,
          characterName: name
        })
      }
    }
  }

  // 检查角色性格一致性
  async checkCharacterPersonality(name, character, chapterNumber, chapterContent) {
    if (!character.traits || character.traits.length === 0) return

    // 检查性格特征是否在行为中体现
    const characterBehavior = this.extractCharacterBehavior(name, chapterContent)
    
    if (characterBehavior) {
      // 简单的性格一致性检查
      const traits = character.traits
      
      if (traits.includes('冷酷') && characterBehavior.includes('温柔')) {
        this.addIssue({
          type: 'character_personality',
          severity: 'medium',
          title: `${name}性格表现不一致`,
          description: `${name}设定为冷酷，但在本章表现温柔`,
          suggestion: `调整${name}的行为描写或性格设定`,
          chapterNumber,
          characterName: name
        })
      }
      
      if (traits.includes('勇敢') && characterBehavior.includes('害怕')) {
        this.addIssue({
          type: 'character_personality',
          severity: 'medium',
          title: `${name}性格表现不一致`,
          description: `${name}设定为勇敢，但在本章表现害怕`,
          suggestion: `调整${name}的行为描写或增加合理解释`,
          chapterNumber,
          characterName: name
        })
      }
    }
  }

  // 检查角色能力一致性
  async checkCharacterAbilities(name, character, chapterNumber, chapterContent) {
    // 检查修炼等级（如果适用）
    const currentLevel = this.extractCultivationLevel(name, chapterContent)
    if (currentLevel && character.cultivationLevel) {
      if (this.compareCultivationLevel(currentLevel, character.cultivationLevel) < 0) {
        this.addIssue({
          type: 'character_ability',
          severity: 'high',
          title: `${name}修炼等级倒退`,
          description: `${name}从${character.cultivationLevel}降到${currentLevel}`,
          suggestion: `检查修炼等级设定，确保合理的进展`,
          chapterNumber,
          characterName: name
        })
      }
    }
  }

  // 情节一致性检查
  async checkPlotConsistency(chapterNumber, chapterContent) {
    // 检查主线情节发展
    await this.checkMainPlotProgression(chapterNumber, chapterContent)
    
    // 检查伏笔处理
    await this.checkForeshadowingConsistency(chapterNumber, chapterContent)
    
    // 检查创意实现
    await this.checkCreativityImplementation(chapterNumber, chapterContent)
  }

  // 检查主线情节发展
  async checkMainPlotProgression(chapterNumber, chapterContent) {
    const mainPlot = this.memoryDatabase.plotLines.mainPlot
    if (!mainPlot) return

    // 检查情节发展是否过快或过慢
    const expectedStage = this.calculateExpectedPlotStage(chapterNumber)
    const currentStage = mainPlot.currentStage

    if (this.isPlotProgressionTooFast(expectedStage, currentStage, chapterNumber)) {
      this.addIssue({
        type: 'plot_progression',
        severity: 'medium',
        title: '情节发展过快',
        description: `第${chapterNumber}章情节发展速度超出预期`,
        suggestion: '考虑增加过渡情节，让发展更自然',
        chapterNumber
      })
    }
  }

  // 检查伏笔处理
  async checkForeshadowingConsistency(chapterNumber, chapterContent) {
    const foreshadowing = this.memoryDatabase.foreshadowing
    
    for (const foreshadow of foreshadowing) {
      if (!foreshadow.resolved && chapterNumber > foreshadow.chapter + 50) {
        this.addIssue({
          type: 'foreshadowing',
          severity: 'low',
          title: '伏笔长期未解决',
          description: `第${foreshadow.chapter}章的伏笔"${foreshadow.content}"已超过50章未解决`,
          suggestion: '考虑在近期章节中解决此伏笔',
          chapterNumber,
          relatedChapter: foreshadow.chapter
        })
      }
    }
  }

  // 检查创意实现
  async checkCreativityImplementation(chapterNumber, chapterContent) {
    const creativity = this.memoryDatabase.metadata.creativity
    if (!creativity) return

    // 检查核心创意是否在章节中体现
    const creativityKeywords = this.extractCreativityKeywords(creativity)
    const hasCreativityElements = creativityKeywords.some(keyword => 
      chapterContent.toLowerCase().includes(keyword.toLowerCase())
    )

    if (!hasCreativityElements && chapterNumber % 10 === 0) { // 每10章检查一次
      this.addIssue({
        type: 'creativity_implementation',
        severity: 'medium',
        title: '核心创意体现不足',
        description: `第${chapterNumber}章中缺乏核心创意"${creativity}"的体现`,
        suggestion: '增加与核心创意相关的情节或描述',
        chapterNumber
      })
    }
  }

  // 世界观一致性检查
  async checkWorldConsistency(chapterNumber, chapterContent) {
    const worldSettings = this.memoryDatabase.worldSettings
    
    // 检查世界规则是否被违反
    if (worldSettings.rules) {
      for (const rule of worldSettings.rules) {
        if (this.isRuleViolated(rule, chapterContent)) {
          this.addIssue({
            type: 'world_consistency',
            severity: 'high',
            title: '世界观规则违反',
            description: `第${chapterNumber}章违反了世界规则"${rule}"`,
            suggestion: '修改章节内容以符合世界观设定',
            chapterNumber
          })
        }
      }
    }
  }

  // 时间线一致性检查
  async checkTimelineConsistency(chapterNumber, chapterContent) {
    // 检查时间流逝的合理性
    const timeReferences = this.extractTimeReferences(chapterContent)

    if (timeReferences.length > 0) {
      // 简单的时间一致性检查
      for (const timeRef of timeReferences) {
        if (this.isTimeReferenceInconsistent(timeRef, chapterNumber)) {
          this.addIssue({
            type: 'timeline',
            severity: 'medium',
            title: '时间线不一致',
            description: `第${chapterNumber}章的时间引用"${timeRef}"与之前章节不符`,
            suggestion: '检查时间线设定，确保前后一致',
            chapterNumber
          })
        }
      }
    }
  }

  // 章节连贯性检查（重点功能）
  async checkChapterContinuity(chapterNumber, chapterContent) {
    if (chapterNumber <= 1) return // 第一章无需检查连贯性

    const previousChapter = this.memoryDatabase.chapterSummaries[chapterNumber - 2]
    if (!previousChapter) {
      this.addIssue({
        type: 'chapter_continuity',
        severity: 'high',
        title: '缺少前章信息',
        description: `第${chapterNumber}章缺少第${chapterNumber - 1}章的信息，无法确保连贯性`,
        suggestion: '确保前一章的信息已正确保存到记忆库',
        chapterNumber
      })
      return
    }

    // 检查开头是否与前章结尾衔接（增强版）
    const currentOpening = this.extractChapterOpening(chapterContent)
    const previousEnding = this.extractPreviousEnding(previousChapter)

    if (!this.isOpeningConnectedToEnding(currentOpening, previousEnding)) {
      this.addIssue({
        type: 'chapter_continuity',
        severity: 'high',
        title: '章节衔接不自然',
        description: `第${chapterNumber}章开头与第${chapterNumber - 1}章结尾缺乏自然衔接`,
        suggestion: '修改章节开头，确保与前章结尾自然过渡',
        chapterNumber,
        details: {
          previousEnding: previousEnding,
          currentOpening: currentOpening
        }
      })
    }

    // 检查情感基调连续性
    await this.checkEmotionalToneContinuity(chapterNumber, chapterContent, previousChapter)

    // 检查环境连续性
    await this.checkEnvironmentContinuity(chapterNumber, chapterContent, previousChapter)

    // 检查对话风格连续性
    await this.checkDialogueStyleContinuity(chapterNumber, chapterContent, previousChapter)

    // 检查角色状态连续性
    await this.checkCharacterStateContinuity(chapterNumber, chapterContent, previousChapter)

    // 检查场景位置连续性
    await this.checkSceneContinuity(chapterNumber, chapterContent, previousChapter)
  }

  // 检查角色状态连续性
  async checkCharacterStateContinuity(chapterNumber, chapterContent, previousChapter) {
    const currentCharacters = this.extractCharactersInChapter(chapterContent)
    const previousCharacters = previousChapter.charactersAppeared || []

    for (const character of currentCharacters) {
      if (previousCharacters.includes(character)) {
        // 检查角色状态是否合理延续
        const previousState = this.extractCharacterStateFromSummary(character, previousChapter)
        const currentState = this.extractCharacterStateFromContent(character, chapterContent)

        if (this.isCharacterStateInconsistent(previousState, currentState)) {
          this.addIssue({
            type: 'character_state_continuity',
            severity: 'medium',
            title: `${character}状态不连续`,
            description: `${character}在第${chapterNumber - 1}章是${previousState}，但第${chapterNumber}章突然变成${currentState}`,
            suggestion: `为${character}的状态变化提供合理解释或过渡`,
            chapterNumber,
            characterName: character
          })
        }
      }
    }
  }

  // 检查场景连续性
  async checkSceneContinuity(chapterNumber, chapterContent, previousChapter) {
    const currentLocation = this.memoryDatabase.extractSceneLocation(chapterContent)
    const previousLocation = this.extractLocationFromSummary(previousChapter)

    if (previousLocation && currentLocation && previousLocation !== currentLocation) {
      // 检查是否有场景转换的说明
      if (!this.hasSceneTransition(chapterContent)) {
        this.addIssue({
          type: 'scene_continuity',
          severity: 'medium',
          title: '场景转换突兀',
          description: `从第${chapterNumber - 1}章的${previousLocation}突然转到第${chapterNumber}章的${currentLocation}，缺乏过渡`,
          suggestion: '添加场景转换的描述，如移动过程、时间跳跃等',
          chapterNumber
        })
      }
    }
  }

  // 检查情感基调连续性
  async checkEmotionalToneContinuity(chapterNumber, chapterContent, previousChapter) {
    const currentTone = this.memoryDatabase.extractEmotionalTone(chapterContent)
    const previousTone = previousChapter.emotionalTone || 'neutral'

    // 检查情感基调的突变
    if (this.isEmotionalToneInconsistent(previousTone, currentTone)) {
      this.addIssue({
        type: 'emotional_continuity',
        severity: 'medium',
        title: '情感基调突变',
        description: `第${chapterNumber - 1}章是${previousTone}基调，第${chapterNumber}章突然变成${currentTone}基调`,
        suggestion: '添加情感过渡，或提供基调变化的合理原因',
        chapterNumber
      })
    }
  }

  // 检查环境连续性
  async checkEnvironmentContinuity(chapterNumber, chapterContent, previousChapter) {
    const currentTime = this.memoryDatabase.extractTimeOfDay(chapterContent)
    const previousTime = previousChapter.timeOfDay || '时间不明确'

    // 检查时间流逝的合理性
    if (this.isTimeProgressionUnrealistic(previousTime, currentTime)) {
      this.addIssue({
        type: 'time_continuity',
        severity: 'medium',
        title: '时间流逝不合理',
        description: `第${chapterNumber - 1}章是${previousTime}，第${chapterNumber}章是${currentTime}，时间跳跃不自然`,
        suggestion: '添加时间过渡描述，或调整时间设定',
        chapterNumber
      })
    }

    // 检查天气氛围连续性
    const currentWeather = this.memoryDatabase.extractWeatherMood(chapterContent)
    const previousWeather = previousChapter.weatherMood || '天气不明确'

    if (this.isWeatherChangeAbrupt(previousWeather, currentWeather)) {
      this.addIssue({
        type: 'weather_continuity',
        severity: 'low',
        title: '天气变化突兀',
        description: `第${chapterNumber - 1}章是${previousWeather}，第${chapterNumber}章突然变成${currentWeather}`,
        suggestion: '添加天气变化的描述，或保持天气一致性',
        chapterNumber
      })
    }
  }

  // 检查对话风格连续性
  async checkDialogueStyleContinuity(chapterNumber, chapterContent, previousChapter) {
    if (!previousChapter.characterDialogueStyle) return

    const currentDialogueStyles = this.memoryDatabase.extractDialogueStyles(chapterContent)

    for (const [character, previousStyle] of Object.entries(previousChapter.characterDialogueStyle)) {
      if (currentDialogueStyles[character] && currentDialogueStyles[character] !== previousStyle) {
        this.addIssue({
          type: 'dialogue_style_continuity',
          severity: 'medium',
          title: `${character}对话风格不一致`,
          description: `${character}在第${chapterNumber - 1}章是${previousStyle}风格，第${chapterNumber}章变成${currentDialogueStyles[character]}风格`,
          suggestion: `保持${character}的对话风格一致，或提供风格变化的合理原因`,
          chapterNumber,
          characterName: character
        })
      }
    }
  }

  // 辅助判断方法
  isEmotionalToneInconsistent(previousTone, currentTone) {
    // 定义不合理的情感跳跃
    const inconsistentTransitions = [
      ['positive', 'negative'],
      ['peaceful', 'tense'],
      ['triumphant', 'worried']
    ]

    return inconsistentTransitions.some(([prev, curr]) =>
      previousTone === prev && currentTone === curr
    )
  }

  isTimeProgressionUnrealistic(previousTime, currentTime) {
    // 检查不合理的时间跳跃
    const timeOrder = ['清晨', '上午', '中午', '下午', '傍晚', '夜晚']
    const prevIndex = timeOrder.indexOf(previousTime)
    const currIndex = timeOrder.indexOf(currentTime)

    if (prevIndex === -1 || currIndex === -1) return false

    // 如果时间倒退（除非跨天），认为不合理
    return currIndex < prevIndex && Math.abs(currIndex - prevIndex) > 1
  }

  isWeatherChangeAbrupt(previousWeather, currentWeather) {
    // 检查突兀的天气变化
    const abruptChanges = [
      ['晴朗', '雨天'],
      ['雨天', '晴朗'],
      ['雪天', '晴朗']
    ]

    return abruptChanges.some(([prev, curr]) =>
      previousWeather === prev && currentWeather === curr
    )
  }

  // ConsistencyChecker专用的辅助方法

  extractAge(name, content) {
    // 简单的年龄提取
    const agePattern = new RegExp(`${name}.*?(\\d+)岁`, 'i')
    const match = content.match(agePattern)
    return match ? parseInt(match[1]) : null
  }

  extractCharacterBehavior(name, content) {
    // 提取角色行为描述
    const behaviorPattern = new RegExp(`${name}.*?[。！？]`, 'g')
    const matches = content.match(behaviorPattern)
    return matches ? matches.join(' ') : ''
  }

  extractCultivationLevel(name, content) {
    // 提取修炼等级
    const levelPattern = new RegExp(`${name}.*?(炼气|筑基|金丹|元婴|化神)`, 'i')
    const match = content.match(levelPattern)
    return match ? match[1] : null
  }

  extractCreativityKeywords(creativity) {
    // 从创意中提取关键词
    const keywords = []
    if (creativity.includes('系统')) keywords.push('系统')
    if (creativity.includes('反派')) keywords.push('反派')
    if (creativity.includes('打脸')) keywords.push('打脸')
    if (creativity.includes('重生')) keywords.push('重生')
    if (creativity.includes('穿越')) keywords.push('穿越')
    return keywords
  }

  extractTimeReferences(content) {
    // 提取时间引用
    const timePatterns = [
      /(\d+)年前/g,
      /(\d+)个月前/g,
      /(\d+)天前/g,
      /昨天|今天|明天/g
    ]

    const references = []
    for (const pattern of timePatterns) {
      const matches = content.match(pattern)
      if (matches) {
        references.push(...matches)
      }
    }
    return references
  }

  extractChapterOpening(content) {
    // 提取章节开头（前100字）
    return content.substring(0, 100)
  }

  extractCharactersInChapter(content) {
    const characters = []
    for (const [name] of this.memoryDatabase.characters) {
      if (content.includes(name)) {
        characters.push(name)
      }
    }
    return characters
  }

  extractCharacterStateFromSummary(character, summary) {
    // 从摘要中提取角色状态
    if (summary.summary && summary.summary.includes(`${character}受伤`)) return '受伤'
    if (summary.summary && summary.summary.includes(`${character}愤怒`)) return '愤怒'
    if (summary.summary && summary.summary.includes(`${character}高兴`)) return '高兴'
    return '正常'
  }

  extractCharacterStateFromContent(character, content) {
    // 从内容中提取角色状态
    if (content.includes(`${character}受伤`)) return '受伤'
    if (content.includes(`${character}愤怒`)) return '愤怒'
    if (content.includes(`${character}高兴`)) return '高兴'
    return '正常'
  }

  extractKeywords(text) {
    // 简单的关键词提取
    const keywords = []
    const characters = Array.from(this.memoryDatabase.characters.keys())

    for (const character of characters) {
      if (text.includes(character)) {
        keywords.push(character)
      }
    }

    // 添加场景关键词
    const locations = ['宗门', '山洞', '城市', '森林', '宫殿', '客栈', '家中']
    for (const location of locations) {
      if (text.includes(location)) {
        keywords.push(location)
      }
    }

    return keywords
  }

  // 辅助方法
  addIssue(issue) {
    issue.id = Date.now() + Math.random()
    issue.createdAt = new Date().toISOString()
    this.issues.push(issue)
  }

  isCharacterActive(name, content) {
    // 检查角色是否在内容中有主动行为
    const activePatterns = [
      `${name}说`,
      `${name}做`,
      `${name}走`,
      `${name}看`,
      `${name}想`
    ]
    return activePatterns.some(pattern => content.includes(pattern))
  }

  extractAge(name, content) {
    // 简单的年龄提取
    const agePattern = new RegExp(`${name}.*?(\\d+)岁`, 'i')
    const match = content.match(agePattern)
    return match ? parseInt(match[1]) : null
  }

  extractCharacterBehavior(name, content) {
    // 提取角色行为描述
    const behaviorPattern = new RegExp(`${name}.*?[。！？]`, 'g')
    const matches = content.match(behaviorPattern)
    return matches ? matches.join(' ') : ''
  }

  extractCultivationLevel(name, content) {
    // 提取修炼等级
    const levelPattern = new RegExp(`${name}.*?(炼气|筑基|金丹|元婴|化神)`, 'i')
    const match = content.match(levelPattern)
    return match ? match[1] : null
  }

  compareCultivationLevel(level1, level2) {
    const levels = ['炼气', '筑基', '金丹', '元婴', '化神']
    const index1 = levels.indexOf(level1)
    const index2 = levels.indexOf(level2)
    return index1 - index2
  }

  calculateExpectedPlotStage(chapterNumber) {
    // 根据章节数计算预期的情节阶段
    if (chapterNumber <= 100) return 'beginning'
    if (chapterNumber <= 400) return 'development'
    if (chapterNumber <= 700) return 'climax'
    return 'ending'
  }

  isPlotProgressionTooFast(expected, current, chapterNumber) {
    // 简单的情节发展速度检查
    return false // 暂时返回false，后续可以实现更复杂的逻辑
  }

  extractCreativityKeywords(creativity) {
    // 从创意中提取关键词
    const keywords = []
    if (creativity.includes('系统')) keywords.push('系统')
    if (creativity.includes('反派')) keywords.push('反派')
    if (creativity.includes('打脸')) keywords.push('打脸')
    if (creativity.includes('重生')) keywords.push('重生')
    if (creativity.includes('穿越')) keywords.push('穿越')
    return keywords
  }

  isRuleViolated(rule, content) {
    // 检查世界规则是否被违反
    // 这里需要更复杂的逻辑，暂时返回false
    return false
  }

  extractTimeReferences(content) {
    // 提取时间引用
    const timePatterns = [
      /(\d+)年前/g,
      /(\d+)个月前/g,
      /(\d+)天前/g,
      /昨天|今天|明天/g
    ]
    
    const references = []
    for (const pattern of timePatterns) {
      const matches = content.match(pattern)
      if (matches) {
        references.push(...matches)
      }
    }
    return references
  }

  isTimeReferenceInconsistent(timeRef, chapterNumber) {
    // 检查时间引用是否不一致
    // 这里需要更复杂的逻辑，暂时返回false
    return false
  }

  // 连贯性检查辅助方法
  extractChapterOpening(content) {
    // 提取章节开头（前100字）
    return content.substring(0, 100)
  }

  extractPreviousEnding(previousChapter) {
    // 从前章摘要中提取结尾信息
    if (previousChapter.summary && previousChapter.summary.includes('章节结尾：')) {
      const endingMatch = previousChapter.summary.match(/章节结尾：([^\\n]+)/)
      return endingMatch ? endingMatch[1] : '结尾信息不明确'
    }
    return '结尾信息缺失'
  }

  isOpeningConnectedToEnding(opening, ending) {
    // 简单的连接性检查
    if (ending === '结尾信息缺失' || ending === '结尾信息不明确') {
      return true // 无法判断时不报错
    }

    // 检查是否有关键词连接
    const endingKeywords = this.extractKeywords(ending)
    const openingKeywords = this.extractKeywords(opening)

    // 如果有共同关键词，认为有连接
    const commonKeywords = endingKeywords.filter(word => openingKeywords.includes(word))
    return commonKeywords.length > 0
  }

  extractKeywords(text) {
    // 简单的关键词提取
    const keywords = []
    const characters = Array.from(this.memoryDatabase.characters.keys())

    for (const character of characters) {
      if (text.includes(character)) {
        keywords.push(character)
      }
    }

    // 添加场景关键词
    const locations = ['宗门', '山洞', '城市', '森林', '宫殿', '客栈', '家中']
    for (const location of locations) {
      if (text.includes(location)) {
        keywords.push(location)
      }
    }

    return keywords
  }

  extractCharactersInChapter(content) {
    const characters = []
    for (const [name] of this.memoryDatabase.characters) {
      if (content.includes(name)) {
        characters.push(name)
      }
    }
    return characters
  }

  extractCharacterStateFromSummary(character, summary) {
    // 从摘要中提取角色状态
    if (summary.summary && summary.summary.includes(`${character}受伤`)) return '受伤'
    if (summary.summary && summary.summary.includes(`${character}愤怒`)) return '愤怒'
    if (summary.summary && summary.summary.includes(`${character}高兴`)) return '高兴'
    return '正常'
  }

  extractCharacterStateFromContent(character, content) {
    // 从内容中提取角色状态
    if (content.includes(`${character}受伤`)) return '受伤'
    if (content.includes(`${character}愤怒`)) return '愤怒'
    if (content.includes(`${character}高兴`)) return '高兴'
    return '正常'
  }

  isCharacterStateInconsistent(previousState, currentState) {
    // 检查角色状态变化是否合理
    if (previousState === '受伤' && currentState === '正常') {
      return true // 受伤突然好了，不合理
    }
    return false
  }

  extractSceneLocation(content) {
    const locations = ['宗门', '山洞', '城市', '森林', '宫殿', '客栈', '家中']
    for (const location of locations) {
      if (content.includes(location)) {
        return location
      }
    }
    return '位置不明确'
  }

  extractLocationFromSummary(summary) {
    // 从摘要中提取位置信息
    if (summary.summary && summary.summary.includes('场景位置：')) {
      const locationMatch = summary.summary.match(/场景位置：([^\\n]+)/)
      return locationMatch ? locationMatch[1] : null
    }
    return null
  }

  hasSceneTransition(content) {
    // 检查是否有场景转换的描述
    const transitionWords = ['来到', '走向', '前往', '到达', '离开', '进入', '回到']
    return transitionWords.some(word => content.includes(word))
  }

  // 获取检查结果统计
  getIssuesSummary() {
    const summary = {
      total: this.issues.length,
      high: this.issues.filter(issue => issue.severity === 'high').length,
      medium: this.issues.filter(issue => issue.severity === 'medium').length,
      low: this.issues.filter(issue => issue.severity === 'low').length,
      byType: {}
    }

    // 按类型统计
    for (const issue of this.issues) {
      summary.byType[issue.type] = (summary.byType[issue.type] || 0) + 1
    }

    return summary
  }
}
