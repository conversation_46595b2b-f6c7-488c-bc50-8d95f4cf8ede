// 工具函数库

// 文本处理工具
export const textUtils = {
  // 计算字数（中文按字计算，英文按单词计算）
  getWordCount(text) {
    if (!text) return 0
    
    // 移除空白字符
    const cleanText = text.replace(/\s/g, '')
    
    // 中文字符
    const chineseChars = cleanText.match(/[\u4e00-\u9fa5]/g) || []
    
    // 英文单词
    const englishWords = text.match(/[a-zA-Z]+/g) || []
    
    return chineseChars.length + englishWords.length
  },

  // 计算段落数
  getParagraphCount(text) {
    if (!text) return 0
    return text.split('\n').filter(p => p.trim()).length
  },

  // 计算对话数量
  getDialogueCount(text) {
    if (!text) return 0
    const quotes = text.match(/["""]/g) || []
    return Math.floor(quotes.length / 2)
  },

  // 提取文本摘要
  extractSummary(text, maxLength = 200) {
    if (!text) return ''
    const clean = text.replace(/\n/g, ' ').trim()
    return clean.length > maxLength ? clean.substring(0, maxLength) + '...' : clean
  },

  // 清理文本格式
  cleanText(text) {
    return text
      .replace(/\r\n/g, '\n')
      .replace(/\r/g, '\n')
      .replace(/\n{3,}/g, '\n\n')
      .trim()
  },

  // 格式化文本（添加段落缩进等）
  formatText(text) {
    return text
      .split('\n')
      .filter(line => line.trim())
      .map(line => line.trim().startsWith('　') ? line : '　　' + line.trim())
      .join('\n')
  }
}

// 时间工具
export const timeUtils = {
  // 格式化时间
  formatTime(date, format = 'YYYY-MM-DD HH:mm:ss') {
    const d = new Date(date)
    const year = d.getFullYear()
    const month = String(d.getMonth() + 1).padStart(2, '0')
    const day = String(d.getDate()).padStart(2, '0')
    const hours = String(d.getHours()).padStart(2, '0')
    const minutes = String(d.getMinutes()).padStart(2, '0')
    const seconds = String(d.getSeconds()).padStart(2, '0')

    return format
      .replace('YYYY', year)
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hours)
      .replace('mm', minutes)
      .replace('ss', seconds)
  },

  // 获取相对时间
  getRelativeTime(date) {
    const now = new Date()
    const target = new Date(date)
    const diff = Math.abs(now - target)
    
    const minute = 60 * 1000
    const hour = minute * 60
    const day = hour * 24
    const month = day * 30
    const year = day * 365

    if (diff < minute) {
      return '刚刚'
    } else if (diff < hour) {
      return Math.floor(diff / minute) + '分钟前'
    } else if (diff < day) {
      return Math.floor(diff / hour) + '小时前'
    } else if (diff < month) {
      return Math.floor(diff / day) + '天前'
    } else if (diff < year) {
      return Math.floor(diff / month) + '个月前'
    } else {
      return Math.floor(diff / year) + '年前'
    }
  },

  // 获取今日开始时间
  getTodayStart() {
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    return today
  },

  // 获取今日结束时间
  getTodayEnd() {
    const today = new Date()
    today.setHours(23, 59, 59, 999)
    return today
  }
}

// 文件工具
export const fileUtils = {
  // 导出文本文件
  async exportText(filename, content) {
    try {
      const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
      const url = URL.createObjectURL(blob)
      
      const a = document.createElement('a')
      a.href = url
      a.download = filename
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      
      URL.revokeObjectURL(url)
      return { success: true }
    } catch (error) {
      return { success: false, error: error.message }
    }
  },

  // 读取文件
  async readFile(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = (e) => resolve(e.target.result)
      reader.onerror = (e) => reject(e)
      reader.readAsText(file, 'utf-8')
    })
  },

  // 获取文件扩展名
  getFileExtension(filename) {
    return filename.split('.').pop().toLowerCase()
  },

  // 格式化文件大小
  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes'
    
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }
}

// 数据验证工具
export const validationUtils = {
  // 验证邮箱
  isEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return re.test(email)
  },

  // 验证手机号
  isPhoneNumber(phone) {
    const re = /^1[3-9]\d{9}$/
    return re.test(phone)
  },

  // 验证项目名称
  isValidProjectName(name) {
    return name && name.trim().length >= 2 && name.trim().length <= 50
  },

  // 验证章节标题
  isValidChapterTitle(title) {
    return title && title.trim().length >= 1 && title.trim().length <= 100
  },

  // 验证API Key格式
  isValidApiKey(key) {
    return key && key.trim().length > 10
  }
}

// 本地存储工具
export const storageUtils = {
  // 设置本地存储
  set(key, value) {
    try {
      localStorage.setItem(key, JSON.stringify(value))
      return true
    } catch (error) {
      console.error('Storage set error:', error)
      return false
    }
  },

  // 获取本地存储
  get(key, defaultValue = null) {
    try {
      const item = localStorage.getItem(key)
      return item ? JSON.parse(item) : defaultValue
    } catch (error) {
      console.error('Storage get error:', error)
      return defaultValue
    }
  },

  // 删除本地存储
  remove(key) {
    try {
      localStorage.removeItem(key)
      return true
    } catch (error) {
      console.error('Storage remove error:', error)
      return false
    }
  },

  // 清空本地存储
  clear() {
    try {
      localStorage.clear()
      return true
    } catch (error) {
      console.error('Storage clear error:', error)
      return false
    }
  }
}

// 通用工具
export const commonUtils = {
  // 生成唯一ID
  generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  },

  // 深拷贝
  deepClone(obj) {
    return JSON.parse(JSON.stringify(obj))
  },

  // 防抖函数
  debounce(func, wait) {
    let timeout
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout)
        func(...args)
      }
      clearTimeout(timeout)
      timeout = setTimeout(later, wait)
    }
  },

  // 节流函数
  throttle(func, limit) {
    let inThrottle
    return function(...args) {
      if (!inThrottle) {
        func.apply(this, args)
        inThrottle = true
        setTimeout(() => inThrottle = false, limit)
      }
    }
  },

  // 延迟函数
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  },

  // 重试函数
  async retry(fn, times = 3, delay = 1000) {
    for (let i = 0; i < times; i++) {
      try {
        return await fn()
      } catch (error) {
        if (i === times - 1) throw error
        await this.delay(delay)
      }
    }
  }
}

// 网文相关工具
export const novelUtils = {
  // 网文类型配置
  genreConfig: {
    '玄幻': {
      subGenres: ['修仙', '异世大陆', '东方玄幻', '现代修真', '洪荒封神'],
      styles: ['热血激情', '深沉内敛', '轻松幽默'],
      commonElements: ['功法', '法宝', '仙界', '修炼', '神通']
    },
    '都市': {
      subGenres: ['都市生活', '都市重生', '都市异能', '商战职场', '娱乐明星'],
      styles: ['轻松幽默', '深沉内敛', '热血激情'],
      commonElements: ['系统', '重生', '异能', '豪门', '商战']
    },
    '科幻': {
      subGenres: ['星际战争', '时空穿梭', '机甲', '末世危机', '进化变异'],
      styles: ['悬疑紧张', '热血激情', '深沉内敛'],
      commonElements: ['科技', '星际', '机甲', '变异', '未来']
    }
  },

  // 获取子类型
  getSubGenres(genre) {
    return this.genreConfig[genre]?.subGenres || []
  },

  // 获取推荐风格
  getRecommendedStyles(genre) {
    return this.genreConfig[genre]?.styles || []
  },

  // 获取常见元素
  getCommonElements(genre) {
    return this.genreConfig[genre]?.commonElements || []
  },

  // 计算阅读时间（按每分钟300字计算）
  getReadingTime(wordCount) {
    const wordsPerMinute = 300
    const minutes = Math.ceil(wordCount / wordsPerMinute)
    
    if (minutes < 60) {
      return `${minutes}分钟`
    } else {
      const hours = Math.floor(minutes / 60)
      const remainingMinutes = minutes % 60
      return `${hours}小时${remainingMinutes}分钟`
    }
  },

  // 生成章节标题建议
  generateChapterTitleSuggestions(chapterNumber, genre, style) {
    const templates = {
      '玄幻': [
        `第${chapterNumber}章：初入修仙路`,
        `第${chapterNumber}章：神秘的力量`,
        `第${chapterNumber}章：突破瓶颈`,
        `第${chapterNumber}章：强敌来袭`
      ],
      '都市': [
        `第${chapterNumber}章：新的开始`,
        `第${chapterNumber}章：意外的收获`,
        `第${chapterNumber}章：商场如战场`,
        `第${chapterNumber}章：真相大白`
      ],
      '科幻': [
        `第${chapterNumber}章：星际迷航`,
        `第${chapterNumber}章：未知的威胁`,
        `第${chapterNumber}章：科技的力量`,
        `第${chapterNumber}章：终极对决`
      ]
    }
    
    return templates[genre] || [`第${chapterNumber}章：新的篇章`]
  }
}