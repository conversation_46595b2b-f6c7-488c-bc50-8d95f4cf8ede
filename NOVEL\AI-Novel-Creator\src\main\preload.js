// Electron 预加载脚本
const { contextBridge, ipc<PERSON>enderer } = require('electron')

// 向渲染进程暴露安全的API
contextBridge.exposeInMainWorld('electronAPI', {
  // 调用主进程方法
  invoke: (channel, data) => {
    // 允许的IPC通道
    const validChannels = [
      'ai-api-request',
      'network-diagnose',
      'get-app-version',
      'minimize-window',
      'maximize-window',
      'close-window'
    ]
    
    if (validChannels.includes(channel)) {
      return ipcRenderer.invoke(channel, data)
    } else {
      throw new Error(`Invalid IPC channel: ${channel}`)
    }
  },

  // 发送消息到主进程
  send: (channel, data) => {
    const validChannels = ['app-message']
    
    if (validChannels.includes(channel)) {
      ipcRenderer.send(channel, data)
    } else {
      throw new Error(`Invalid IPC channel: ${channel}`)
    }
  },

  // 监听主进程消息
  on: (channel, callback) => {
    const validChannels = ['app-notification']
    
    if (validChannels.includes(channel)) {
      ipcRenderer.on(channel, callback)
    } else {
      throw new Error(`Invalid IPC channel: ${channel}`)
    }
  },

  // 移除监听器
  removeAllListeners: (channel) => {
    ipcRenderer.removeAllListeners(channel)
  }
})

// 暴露一些有用的信息
contextBridge.exposeInMainWorld('appInfo', {
  platform: process.platform,
  version: process.versions.electron,
  node: process.versions.node,
  chrome: process.versions.chrome
})

console.log('Preload script loaded successfully')
