<template>
  <div class="collaboration-panel">
    <!-- 头部 -->
    <div class="panel-header">
      <h3>
        <el-icon><UserFilled /></el-icon>
        协作功能
      </h3>
      <div class="header-actions">
        <el-button 
          v-if="!activeSession" 
          @click="showCreateDialog = true" 
          type="primary" 
          size="small"
        >
          <el-icon><Plus /></el-icon>
          创建会话
        </el-button>
        <el-button 
          v-if="!activeSession" 
          @click="showJoinDialog = true" 
          type="success" 
          size="small"
        >
          <el-icon><Connection /></el-icon>
          加入会话
        </el-button>
        <el-button 
          v-if="activeSession" 
          @click="leaveSession" 
          type="danger" 
          size="small"
        >
          <el-icon><Close /></el-icon>
          离开会话
        </el-button>
      </div>
    </div>

    <!-- 会话信息 -->
    <div v-if="activeSession" class="session-info">
      <el-card>
        <template #header>
          <div class="session-header">
            <span>{{ activeSession.name }}</span>
            <el-tag :type="isHost ? 'danger' : 'primary'" size="small">
              {{ isHost ? '主持人' : '协作者' }}
            </el-tag>
          </div>
        </template>
        
        <div class="session-details">
          <p><strong>会话ID:</strong> {{ activeSession.id }}</p>
          <p><strong>创建时间:</strong> {{ formatTime(activeSession.createdAt) }}</p>
          <p><strong>协作者数量:</strong> {{ activeSession.collaborators.size }}</p>
          
          <div v-if="isHost" class="host-actions">
            <el-button @click="generateInvite" size="small" type="primary">
              <el-icon><Share /></el-icon>
              生成邀请链接
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 协作者列表 -->
    <div v-if="activeSession" class="collaborators-section">
      <el-card>
        <template #header>
          <span>协作者 ({{ collaboratorsList.length }})</span>
        </template>
        
        <div class="collaborators-list">
          <div 
            v-for="collaborator in collaboratorsList"
            :key="collaborator.id"
            class="collaborator-item"
          >
            <div class="collaborator-info">
              <el-avatar :size="32">
                {{ collaborator.name.charAt(0) }}
              </el-avatar>
              <div class="collaborator-details">
                <div class="collaborator-name">
                  {{ collaborator.name }}
                  <el-tag v-if="collaborator.role === 'host'" type="danger" size="small">主持人</el-tag>
                </div>
                <div class="collaborator-status">
                  <span
                    class="status-dot"
                    :style="{ backgroundColor: collaborator.isActive ? '#67c23a' : '#909399' }"
                  ></span>
                  {{ collaborator.isActive ? '在线' : '离线' }}
                </div>
              </div>
            </div>
            <div class="collaborator-actions">
              <el-button 
                v-if="isHost && collaborator.id !== userId" 
                @click="removeCollaborator(collaborator.id)"
                size="small" 
                type="danger" 
                text
              >
                移除
              </el-button>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 评论系统 -->
    <div v-if="activeSession" class="comments-section">
      <el-card>
        <template #header>
          <div class="comments-header">
            <span>评论 ({{ comments.length }})</span>
            <el-button @click="showAddComment = true" size="small" type="primary">
              <el-icon><ChatDotRound /></el-icon>
              添加评论
            </el-button>
          </div>
        </template>
        
        <div class="comments-list">
          <div 
            v-for="comment in comments"
            :key="comment.id"
            class="comment-item"
          >
            <div class="comment-header">
              <el-avatar :size="24">
                {{ comment.authorName.charAt(0) }}
              </el-avatar>
              <div class="comment-meta">
                <span class="comment-author">{{ comment.authorName }}</span>
                <span class="comment-time">{{ formatTime(comment.createdAt) }}</span>
                <el-tag :type="getCommentTypeColor(comment.type)" size="small">
                  {{ getCommentTypeLabel(comment.type) }}
                </el-tag>
                <el-tag :type="getCommentStatusColor(comment.status)" size="small">
                  {{ getCommentStatusLabel(comment.status) }}
                </el-tag>
              </div>
            </div>
            
            <div class="comment-content">
              <div v-if="comment.selectedText" class="comment-context">
                <strong>关于:</strong> "{{ comment.selectedText }}"
              </div>
              <div class="comment-text">{{ comment.content }}</div>
            </div>
            
            <div class="comment-actions">
              <el-button @click="replyToComment(comment)" size="small" type="text">
                <el-icon><ChatDotRound /></el-icon>
                回复 ({{ comment.replies.length }})
              </el-button>
              <el-button 
                v-if="comment.status === 'open'"
                @click="resolveComment(comment.id)" 
                size="small" 
                type="text"
              >
                <el-icon><Check /></el-icon>
                解决
              </el-button>
            </div>
            
            <!-- 回复列表 -->
            <div v-if="comment.replies.length > 0" class="replies-list">
              <div 
                v-for="reply in comment.replies"
                :key="reply.id"
                class="reply-item"
              >
                <el-avatar :size="20">
                  {{ reply.authorName.charAt(0) }}
                </el-avatar>
                <div class="reply-content">
                  <div class="reply-meta">
                    <span class="reply-author">{{ reply.authorName }}</span>
                    <span class="reply-time">{{ formatTime(reply.createdAt) }}</span>
                  </div>
                  <div class="reply-text">{{ reply.content }}</div>
                </div>
              </div>
            </div>
          </div>
          
          <el-empty v-if="comments.length === 0" description="暂无评论" />
        </div>
      </el-card>
    </div>

    <!-- 协作统计 -->
    <div v-if="activeSession" class="stats-section">
      <el-card>
        <template #header>
          <span>协作统计</span>
        </template>
        
        <div class="stats-grid">
          <el-statistic title="总协作者" :value="stats.totalCollaborators" />
          <el-statistic title="活跃协作者" :value="stats.activeCollaborators" />
          <el-statistic title="总评论" :value="stats.totalComments" />
          <el-statistic title="待解决" :value="stats.openComments" />
        </div>
      </el-card>
    </div>

    <!-- 创建会话对话框 -->
    <el-dialog v-model="showCreateDialog" title="创建协作会话" width="500px">
      <el-form :model="createForm" label-width="100px">
        <el-form-item label="会话名称" required>
          <el-input v-model="createForm.name" placeholder="输入会话名称" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input 
            v-model="createForm.description" 
            type="textarea" 
            placeholder="输入会话描述"
            :rows="3"
          />
        </el-form-item>
        <el-form-item label="最大人数">
          <el-input-number v-model="createForm.maxCollaborators" :min="2" :max="20" />
        </el-form-item>
        <el-form-item label="权限设置">
          <el-checkbox v-model="createForm.canEdit">允许编辑</el-checkbox>
          <el-checkbox v-model="createForm.canComment">允许评论</el-checkbox>
          <el-checkbox v-model="createForm.requireApproval">需要审批</el-checkbox>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button @click="createSession" type="primary" :loading="creating">创建</el-button>
      </template>
    </el-dialog>

    <!-- 加入会话对话框 -->
    <el-dialog v-model="showJoinDialog" title="加入协作会话" width="400px">
      <el-form :model="joinForm" label-width="100px">
        <el-form-item label="会话ID" required>
          <el-input v-model="joinForm.sessionId" placeholder="输入会话ID" />
        </el-form-item>
        <el-form-item label="邀请码">
          <el-input v-model="joinForm.inviteCode" placeholder="输入邀请码（可选）" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showJoinDialog = false">取消</el-button>
        <el-button @click="joinSession" type="primary" :loading="joining">加入</el-button>
      </template>
    </el-dialog>

    <!-- 添加评论对话框 -->
    <el-dialog v-model="showAddComment" title="添加评论" width="500px">
      <el-form :model="commentForm" label-width="80px">
        <el-form-item label="类型">
          <el-select v-model="commentForm.type" placeholder="选择评论类型">
            <el-option label="一般评论" value="general" />
            <el-option label="建议" value="suggestion" />
            <el-option label="问题" value="question" />
            <el-option label="问题报告" value="issue" />
          </el-select>
        </el-form-item>
        <el-form-item label="内容" required>
          <el-input 
            v-model="commentForm.content" 
            type="textarea" 
            placeholder="输入评论内容"
            :rows="4"
          />
        </el-form-item>
        <el-form-item label="关联文本">
          <el-input 
            v-model="commentForm.selectedText" 
            placeholder="可选：关联的文本片段"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showAddComment = false">取消</el-button>
        <el-button @click="addComment" type="primary" :loading="commenting">添加</el-button>
      </template>
    </el-dialog>

    <!-- 邀请链接对话框 -->
    <el-dialog v-model="showInviteDialog" title="邀请链接" width="500px">
      <div class="invite-content">
        <el-alert 
          title="邀请链接已生成" 
          type="success" 
          :closable="false"
          style="margin-bottom: 16px;"
        />
        
        <el-form label-width="80px">
          <el-form-item label="邀请链接">
            <el-input 
              v-model="inviteLink.url" 
              readonly
              style="margin-bottom: 8px;"
            >
              <template #append>
                <el-button @click="copyInviteLink">复制</el-button>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="邀请码">
            <el-input v-model="inviteLink.code" readonly />
          </el-form-item>
          <el-form-item label="过期时间">
            <span>{{ formatTime(inviteLink.expiresAt) }}</span>
          </el-form-item>
        </el-form>
      </div>
    </el-dialog>

    <!-- 回复评论对话框 -->
    <el-dialog v-model="showReplyDialog" title="回复评论" width="400px">
      <el-input 
        v-model="replyContent" 
        type="textarea" 
        placeholder="输入回复内容"
        :rows="3"
      />
      
      <template #footer>
        <el-button @click="showReplyDialog = false">取消</el-button>
        <el-button @click="submitReply" type="primary" :loading="replying">回复</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  UserFilled, Plus, Connection, Close, Share, ChatDotRound,
  Check
} from '@element-plus/icons-vue'
import { collaborationService } from '../services/collaborationService'

// 响应式数据
const activeSession = ref(null)
const isHost = ref(false)
const userId = ref('')
const comments = ref([])
const stats = ref({})

const showCreateDialog = ref(false)
const showJoinDialog = ref(false)
const showAddComment = ref(false)
const showInviteDialog = ref(false)
const showReplyDialog = ref(false)

const creating = ref(false)
const joining = ref(false)
const commenting = ref(false)
const replying = ref(false)

const createForm = reactive({
  name: '',
  description: '',
  maxCollaborators: 5,
  canEdit: true,
  canComment: true,
  requireApproval: false
})

const joinForm = reactive({
  sessionId: '',
  inviteCode: ''
})

const commentForm = reactive({
  type: 'general',
  content: '',
  selectedText: ''
})

const inviteLink = ref({})
const replyContent = ref('')
const currentReplyComment = ref(null)

// 计算属性
const collaboratorsList = computed(() => {
  if (!activeSession.value) return []
  return Array.from(activeSession.value.collaborators.values())
})

// 方法
const createSession = async () => {
  creating.value = true
  
  try {
    const session = collaborationService.createSession({
      name: createForm.name,
      description: createForm.description,
      maxCollaborators: createForm.maxCollaborators,
      canEdit: createForm.canEdit,
      canComment: createForm.canComment,
      requireApproval: createForm.requireApproval
    })
    
    activeSession.value = session
    isHost.value = true
    showCreateDialog.value = false
    
    // 重置表单
    Object.assign(createForm, {
      name: '',
      description: '',
      maxCollaborators: 5,
      canEdit: true,
      canComment: true,
      requireApproval: false
    })
    
    loadComments()
    loadStats()
    
    ElMessage.success('协作会话创建成功')
  } catch (error) {
    console.error('创建会话失败:', error)
    ElMessage.error('创建会话失败')
  } finally {
    creating.value = false
  }
}

const joinSession = async () => {
  joining.value = true
  
  try {
    const session = await collaborationService.joinSession(
      joinForm.sessionId,
      joinForm.inviteCode
    )
    
    activeSession.value = session
    isHost.value = session.hostId === userId.value
    showJoinDialog.value = false
    
    // 重置表单
    Object.assign(joinForm, {
      sessionId: '',
      inviteCode: ''
    })
    
    loadComments()
    loadStats()
    
    ElMessage.success('成功加入协作会话')
  } catch (error) {
    console.error('加入会话失败:', error)
    ElMessage.error(error.message || '加入会话失败')
  } finally {
    joining.value = false
  }
}

const leaveSession = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要离开当前协作会话吗？',
      '确认离开',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    collaborationService.leaveSession()
    activeSession.value = null
    isHost.value = false
    comments.value = []
    stats.value = {}
    
    ElMessage.success('已离开协作会话')
  } catch {
    // 用户取消
  }
}

const addComment = async () => {
  commenting.value = true
  
  try {
    const comment = collaborationService.addComment({
      type: commentForm.type,
      content: commentForm.content,
      selectedText: commentForm.selectedText
    })
    
    comments.value.unshift(comment)
    showAddComment.value = false
    
    // 重置表单
    Object.assign(commentForm, {
      type: 'general',
      content: '',
      selectedText: ''
    })
    
    loadStats()
    ElMessage.success('评论添加成功')
  } catch (error) {
    console.error('添加评论失败:', error)
    ElMessage.error('添加评论失败')
  } finally {
    commenting.value = false
  }
}

const replyToComment = (comment) => {
  currentReplyComment.value = comment
  replyContent.value = ''
  showReplyDialog.value = true
}

const submitReply = async () => {
  replying.value = true
  
  try {
    const reply = collaborationService.replyToComment(
      currentReplyComment.value.id,
      replyContent.value
    )
    
    currentReplyComment.value.replies.push(reply)
    showReplyDialog.value = false
    replyContent.value = ''
    currentReplyComment.value = null
    
    ElMessage.success('回复成功')
  } catch (error) {
    console.error('回复失败:', error)
    ElMessage.error('回复失败')
  } finally {
    replying.value = false
  }
}

const resolveComment = async (commentId) => {
  try {
    const comment = collaborationService.resolveComment(commentId)
    
    // 更新本地评论状态
    const index = comments.value.findIndex(c => c.id === commentId)
    if (index > -1) {
      comments.value[index].status = 'resolved'
    }
    
    loadStats()
    ElMessage.success('评论已解决')
  } catch (error) {
    console.error('解决评论失败:', error)
    ElMessage.error('解决评论失败')
  }
}

const generateInvite = async () => {
  try {
    const invite = collaborationService.generateInviteLink()
    inviteLink.value = invite
    showInviteDialog.value = true
  } catch (error) {
    console.error('生成邀请链接失败:', error)
    ElMessage.error('生成邀请链接失败')
  }
}

const copyInviteLink = async () => {
  try {
    await navigator.clipboard.writeText(inviteLink.value.url)
    ElMessage.success('邀请链接已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败')
  }
}

const removeCollaborator = async (collaboratorId) => {
  try {
    await ElMessageBox.confirm(
      '确定要移除此协作者吗？',
      '确认移除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 实际应用中这里会调用服务方法
    ElMessage.success('协作者已移除')
  } catch {
    // 用户取消
  }
}

const loadComments = () => {
  comments.value = collaborationService.getComments()
}

const loadStats = () => {
  stats.value = collaborationService.getCollaborationStats()
}

// 辅助方法
const formatTime = (timestamp) => {
  const date = new Date(timestamp)
  return date.toLocaleString()
}

const getCommentTypeColor = (type) => {
  const colorMap = {
    general: 'info',
    suggestion: 'success',
    question: 'warning',
    issue: 'danger'
  }
  return colorMap[type] || 'info'
}

const getCommentTypeLabel = (type) => {
  const labelMap = {
    general: '一般',
    suggestion: '建议',
    question: '问题',
    issue: '问题报告'
  }
  return labelMap[type] || '一般'
}

const getCommentStatusColor = (status) => {
  const colorMap = {
    open: 'warning',
    resolved: 'success',
    dismissed: 'info'
  }
  return colorMap[status] || 'info'
}

const getCommentStatusLabel = (status) => {
  const labelMap = {
    open: '待解决',
    resolved: '已解决',
    dismissed: '已忽略'
  }
  return labelMap[status] || '待解决'
}

// 事件监听
const setupEventListeners = () => {
  collaborationService.on('session-created', (session) => {
    console.log('会话已创建:', session)
  })
  
  collaborationService.on('session-joined', (session) => {
    console.log('已加入会话:', session)
  })
  
  collaborationService.on('collaborator-joined', (collaborator) => {
    ElMessage.info(`${collaborator.name} 加入了协作会话`)
  })
  
  collaborationService.on('comment-added', (comment) => {
    if (comment.authorId !== userId.value) {
      ElMessage.info(`${comment.authorName} 添加了新评论`)
    }
  })
}

// 生命周期
onMounted(() => {
  userId.value = collaborationService.userId
  setupEventListeners()
  
  // 检查是否有活动会话
  if (collaborationService.activeSession) {
    activeSession.value = collaborationService.activeSession
    isHost.value = collaborationService.isHost
    loadComments()
    loadStats()
  }
})

onUnmounted(() => {
  // 清理事件监听器
  collaborationService.eventListeners.clear()
})
</script>

<style scoped>
.collaboration-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  gap: 16px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--border-light);
}

.panel-header h3 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-primary);
}

.header-actions {
  display: flex;
  gap: 8px;
}

.session-info .session-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.session-details p {
  margin: 8px 0;
  color: var(--text-secondary);
}

.host-actions {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--border-light);
}

.collaborators-list {
  max-height: 300px;
  overflow-y: auto;
}

.collaborator-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid var(--border-light);
}

.collaborator-item:last-child {
  border-bottom: none;
}

.collaborator-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.collaborator-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.collaborator-name {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: var(--text-primary);
}

.collaborator-status {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: var(--text-muted);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
}

.comments-section {
  flex: 1;
  overflow: hidden;
}

.comments-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.comments-list {
  max-height: 400px;
  overflow-y: auto;
}

.comment-item {
  padding: 16px 0;
  border-bottom: 1px solid var(--border-light);
}

.comment-item:last-child {
  border-bottom: none;
}

.comment-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.comment-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.comment-author {
  font-weight: 500;
  color: var(--text-primary);
}

.comment-time {
  font-size: 12px;
  color: var(--text-muted);
}

.comment-content {
  margin: 8px 0;
  padding-left: 36px;
}

.comment-context {
  padding: 8px;
  background: var(--bg-secondary);
  border-radius: var(--radius-sm);
  margin-bottom: 8px;
  font-size: 14px;
  color: var(--text-secondary);
}

.comment-text {
  line-height: 1.6;
  color: var(--text-primary);
}

.comment-actions {
  display: flex;
  gap: 8px;
  padding-left: 36px;
}

.replies-list {
  margin-top: 12px;
  padding-left: 36px;
  border-left: 2px solid var(--border-light);
}

.reply-item {
  display: flex;
  gap: 8px;
  padding: 8px 0;
  border-bottom: 1px solid var(--border-light);
}

.reply-item:last-child {
  border-bottom: none;
}

.reply-content {
  flex: 1;
}

.reply-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.reply-author {
  font-weight: 500;
  font-size: 14px;
  color: var(--text-primary);
}

.reply-time {
  font-size: 12px;
  color: var(--text-muted);
}

.reply-text {
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-secondary);
}

.stats-section .stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
}

.invite-content {
  padding: 16px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .collaboration-panel {
    padding: 12px;
    gap: 12px;
  }

  .panel-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: center;
  }

  .collaborator-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .comment-content,
  .comment-actions,
  .replies-list {
    padding-left: 0;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .comment-context {
    background: var(--bg-tertiary);
  }

  .replies-list {
    border-left-color: var(--border-dark);
  }
}
</style>
