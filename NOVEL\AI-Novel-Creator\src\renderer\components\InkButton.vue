<template>
  <button 
    :class="buttonClasses"
    :disabled="disabled"
    @click="handleClick"
    v-bind="$attrs"
  >
    <div class="btn-content">
      <div v-if="icon" class="btn-icon">
        <el-icon>
          <component :is="icon" />
        </el-icon>
      </div>
      <span class="btn-text">
        <slot />
      </span>
    </div>
    <div class="btn-ripple"></div>
  </button>
</template>

<script>
import { computed } from 'vue'

export default {
  name: 'InkButton',
  props: {
    type: {
      type: String,
      default: 'default',
      validator: (value) => ['default', 'primary', 'gold', 'jade', 'vermillion', 'paper'].includes(value)
    },
    size: {
      type: String,
      default: 'medium',
      validator: (value) => ['small', 'medium', 'large'].includes(value)
    },
    icon: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  emits: ['click'],
  setup(props, { emit }) {
    const buttonClasses = computed(() => [
      'ink-button',
      `ink-button--${props.type}`,
      `ink-button--${props.size}`,
      {
        'is-disabled': props.disabled,
        'is-loading': props.loading
      }
    ])

    const handleClick = (event) => {
      if (!props.disabled && !props.loading) {
        emit('click', event)
      }
    }

    return {
      buttonClasses,
      handleClick
    }
  }
}
</script>

<style scoped>
.ink-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: 2px solid;
  border-radius: var(--radius-lg);
  font-family: var(--font-elegant);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-paper);
  overflow: hidden;
  background: transparent;
  text-decoration: none;
  outline: none;
}

.ink-button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.2);
}

/* 尺寸变体 */
.ink-button--small {
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: 13px;
  min-height: 32px;
}

.ink-button--medium {
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: 14px;
  min-height: 40px;
}

.ink-button--large {
  padding: var(--spacing-lg) var(--spacing-xl);
  font-size: 16px;
  min-height: 48px;
}

/* 类型变体 */
.ink-button--default {
  background: var(--bg-primary);
  border-color: var(--border-medium);
  color: var(--text-primary);
}

.ink-button--default:hover {
  border-color: var(--border-accent);
  background: var(--bg-elevated);
  transform: translateY(-2px);
  box-shadow: var(--shadow-paper-md);
}

.ink-button--primary {
  background: var(--gradient-ink);
  border-color: var(--ink-nong);
  color: var(--paper-xuan);
}

.ink-button--primary:hover {
  background: var(--ink-jiao);
  border-color: var(--ink-jiao);
  transform: translateY(-2px);
  box-shadow: var(--shadow-paper-lg);
}

.ink-button--gold {
  background: var(--gradient-gold);
  border-color: var(--huang-jin);
  color: var(--ink-jiao);
}

.ink-button--gold:hover {
  background: #f1c40f;
  transform: translateY(-2px) scale(1.02);
  box-shadow: var(--shadow-paper-xl);
}

.ink-button--jade {
  background: var(--gradient-jade);
  border-color: var(--song-lv);
  color: var(--paper-xuan);
}

.ink-button--jade:hover {
  background: var(--dan-qing);
  border-color: var(--dan-qing);
  transform: translateY(-2px);
  box-shadow: var(--shadow-paper-lg);
}

.ink-button--vermillion {
  background: var(--zhu-sha);
  border-color: var(--zhu-sha);
  color: var(--paper-xuan);
}

.ink-button--vermillion:hover {
  background: var(--yan-zhi);
  border-color: var(--yan-zhi);
  transform: translateY(-2px);
  box-shadow: var(--shadow-paper-lg);
}

.ink-button--paper {
  background: var(--bg-primary);
  border-color: var(--border-light);
  color: var(--text-primary);
  position: relative;
}

.ink-button--paper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-paper);
  opacity: 0;
  transition: opacity var(--duration-normal) var(--ease-paper);
  border-radius: inherit;
}

.ink-button--paper:hover::before {
  opacity: 1;
}

.ink-button--paper:hover {
  border-color: var(--border-accent);
  box-shadow: var(--shadow-paper-md);
  transform: translateY(-1px);
}

/* 按钮内容 */
.btn-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  position: relative;
  z-index: 2;
}

.btn-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2em;
}

.btn-text {
  line-height: 1;
}

/* 水波纹效果 */
.btn-ripple {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transform: translateX(-100%);
  transition: transform var(--duration-slow) var(--ease-brush);
  border-radius: inherit;
}

.ink-button:hover .btn-ripple {
  transform: translateX(100%);
}

/* 禁用状态 */
.ink-button.is-disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

.ink-button.is-disabled:hover {
  transform: none !important;
  box-shadow: none !important;
}

.ink-button.is-disabled .btn-ripple {
  display: none;
}

/* 加载状态 */
.ink-button.is-loading {
  cursor: wait;
}

.ink-button.is-loading .btn-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ink-button--large {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: 15px;
    min-height: 44px;
  }
  
  .ink-button--medium {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 13px;
    min-height: 36px;
  }
}
</style>
