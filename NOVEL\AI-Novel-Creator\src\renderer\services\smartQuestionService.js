/**
 * 智能问题建议服务
 * 基于文本内容、上下文和用户历史生成更精准的问题建议
 */

class SmartQuestionService {
  constructor() {
    this.questionTemplates = this.initializeQuestionTemplates()
    this.contextAnalyzer = new ContextAnalyzer()
    this.userPreferences = this.loadUserPreferences()
  }

  /**
   * 生成智能问题建议
   * @param {string} selectedText 选中的文本
   * @param {Object} context 上下文信息
   * @param {Object} options 选项
   */
  generateSmartQuestions(selectedText, context = {}, options = {}) {
    const {
      maxQuestions = 6,
      includeGeneral = true,
      prioritizeUserPreferences = true
    } = options

    // 分析文本特征
    const textFeatures = this.analyzeTextFeatures(selectedText)
    
    // 分析上下文
    const contextFeatures = this.contextAnalyzer.analyze(context)
    
    // 生成候选问题
    const candidateQuestions = this.generateCandidateQuestions(
      selectedText, 
      textFeatures, 
      contextFeatures
    )
    
    // 根据用户偏好排序
    const rankedQuestions = this.rankQuestions(
      candidateQuestions, 
      textFeatures, 
      contextFeatures,
      prioritizeUserPreferences
    )
    
    // 返回最佳问题
    return rankedQuestions.slice(0, maxQuestions)
  }

  /**
   * 分析文本特征
   */
  analyzeTextFeatures(text) {
    const features = {
      length: text.length,
      wordCount: this.countWords(text),
      sentenceCount: this.countSentences(text),
      hasDialogue: this.hasDialogue(text),
      hasAction: this.hasAction(text),
      hasDescription: this.hasDescription(text),
      hasEmotion: this.hasEmotion(text),
      hasConflict: this.hasConflict(text),
      tense: this.detectTense(text),
      perspective: this.detectPerspective(text),
      tone: this.detectTone(text),
      complexity: this.calculateComplexity(text),
      keywords: this.extractKeywords(text)
    }
    
    return features
  }

  /**
   * 生成候选问题
   */
  generateCandidateQuestions(text, textFeatures, contextFeatures) {
    const questions = []
    
    // 基于文本类型的问题
    questions.push(...this.generateTypeBasedQuestions(textFeatures))
    
    // 基于文本特征的问题
    questions.push(...this.generateFeatureBasedQuestions(textFeatures))
    
    // 基于上下文的问题
    questions.push(...this.generateContextBasedQuestions(contextFeatures))
    
    // 基于关键词的问题
    questions.push(...this.generateKeywordBasedQuestions(textFeatures.keywords))
    
    // 通用问题
    questions.push(...this.generateGeneralQuestions())
    
    // 去重
    return [...new Set(questions)]
  }

  /**
   * 基于文本类型生成问题
   */
  generateTypeBasedQuestions(features) {
    const questions = []
    
    if (features.hasDialogue) {
      questions.push(
        '这段对话是否符合角色的性格特点？',
        '对话的语言风格是否一致？',
        '这段对话是否推进了情节发展？',
        '对话中是否有多余的信息？'
      )
    }
    
    if (features.hasAction) {
      questions.push(
        '这个动作描写是否生动具体？',
        '动作的节奏是否合适？',
        '动作是否体现了角色特点？',
        '动作序列是否逻辑清晰？'
      )
    }
    
    if (features.hasDescription) {
      questions.push(
        '这段描写是否过于冗长？',
        '描写是否营造了合适的氛围？',
        '细节是否恰到好处？',
        '描写是否与情节相关？'
      )
    }
    
    if (features.hasEmotion) {
      questions.push(
        '情感表达是否真实可信？',
        '情感变化是否自然？',
        '情感强度是否合适？',
        '是否过度煽情？'
      )
    }
    
    return questions
  }

  /**
   * 基于文本特征生成问题
   */
  generateFeatureBasedQuestions(features) {
    const questions = []
    
    // 长度相关
    if (features.length > 200) {
      questions.push('这段文字是否过长，需要分段？')
    } else if (features.length < 30) {
      questions.push('这段描述是否过于简略？')
    }
    
    // 复杂度相关
    if (features.complexity > 0.8) {
      questions.push('句式是否过于复杂，影响阅读？')
    } else if (features.complexity < 0.3) {
      questions.push('表达是否过于简单，缺乏变化？')
    }
    
    // 语调相关
    if (features.tone === 'formal') {
      questions.push('语调是否过于正式？')
    } else if (features.tone === 'casual') {
      questions.push('语调是否过于随意？')
    }
    
    return questions
  }

  /**
   * 基于上下文生成问题
   */
  generateContextBasedQuestions(contextFeatures) {
    const questions = []
    
    if (contextFeatures.chapterPosition === 'beginning') {
      questions.push(
        '开头是否足够吸引人？',
        '是否有效建立了场景？',
        '角色介绍是否自然？'
      )
    } else if (contextFeatures.chapterPosition === 'climax') {
      questions.push(
        '冲突是否足够激烈？',
        '转折是否合理？',
        '节奏是否紧凑？'
      )
    } else if (contextFeatures.chapterPosition === 'ending') {
      questions.push(
        '结尾是否令人满意？',
        '是否留下了合适的悬念？',
        '情感收尾是否到位？'
      )
    }
    
    return questions
  }

  /**
   * 基于关键词生成问题
   */
  generateKeywordBasedQuestions(keywords) {
    const questions = []
    
    keywords.forEach(keyword => {
      if (this.questionTemplates.keywords[keyword]) {
        questions.push(...this.questionTemplates.keywords[keyword])
      }
    })
    
    return questions
  }

  /**
   * 生成通用问题
   */
  generateGeneralQuestions() {
    return [
      '这段内容与前文的连贯性如何？',
      '文字表达是否清晰准确？',
      '是否有语法或用词问题？',
      '节奏把控是否合适？',
      '是否符合整体风格？',
      '读者理解是否容易？'
    ]
  }

  /**
   * 问题排序
   */
  rankQuestions(questions, textFeatures, contextFeatures, prioritizeUserPreferences) {
    return questions.map(question => ({
      text: question,
      score: this.calculateQuestionScore(question, textFeatures, contextFeatures, prioritizeUserPreferences)
    }))
    .sort((a, b) => b.score - a.score)
    .map(item => item.text)
  }

  /**
   * 计算问题分数
   */
  calculateQuestionScore(question, textFeatures, contextFeatures, prioritizeUserPreferences) {
    let score = 0.5 // 基础分数
    
    // 基于文本特征的相关性
    score += this.calculateTextRelevance(question, textFeatures) * 0.3
    
    // 基于上下文的相关性
    score += this.calculateContextRelevance(question, contextFeatures) * 0.2
    
    // 用户偏好
    if (prioritizeUserPreferences) {
      score += this.calculateUserPreference(question) * 0.3
    }
    
    // 问题质量
    score += this.calculateQuestionQuality(question) * 0.2
    
    return Math.min(score, 1.0)
  }

  /**
   * 计算文本相关性
   */
  calculateTextRelevance(question, features) {
    let relevance = 0
    
    // 检查问题是否与文本特征匹配
    if (features.hasDialogue && question.includes('对话')) relevance += 0.3
    if (features.hasAction && question.includes('动作')) relevance += 0.3
    if (features.hasDescription && question.includes('描写')) relevance += 0.3
    if (features.hasEmotion && question.includes('情感')) relevance += 0.3
    
    return Math.min(relevance, 1.0)
  }

  /**
   * 计算上下文相关性
   */
  calculateContextRelevance(question, contextFeatures) {
    // 简化实现，实际可以更复杂
    return 0.5
  }

  /**
   * 计算用户偏好分数
   */
  calculateUserPreference(question) {
    const preferences = this.userPreferences
    
    // 检查用户历史选择的问题类型
    for (const [category, weight] of Object.entries(preferences.questionCategories)) {
      if (question.includes(category)) {
        return weight
      }
    }
    
    return 0.5
  }

  /**
   * 计算问题质量
   */
  calculateQuestionQuality(question) {
    let quality = 0.5
    
    // 问题长度适中
    if (question.length >= 8 && question.length <= 30) {
      quality += 0.2
    }
    
    // 包含疑问词
    if (/[是否|如何|为什么|怎样|什么]/.test(question)) {
      quality += 0.2
    }
    
    // 具体性
    if (/[角色|情节|描写|对话|节奏]/.test(question)) {
      quality += 0.1
    }
    
    return Math.min(quality, 1.0)
  }

  // 辅助方法
  countWords(text) {
    const chineseChars = (text.match(/[\u4e00-\u9fa5]/g) || []).length
    const englishWords = (text.match(/[a-zA-Z]+/g) || []).length
    return chineseChars + englishWords
  }

  countSentences(text) {
    return (text.match(/[。！？.!?]/g) || []).length
  }

  hasDialogue(text) {
    return /["""]/.test(text)
  }

  hasAction(text) {
    return /[走|跑|跳|看|听|说|做|拿|放|开|关]/.test(text)
  }

  hasDescription(text) {
    return /[美丽|漂亮|高大|宽阔|明亮|黑暗|温暖|寒冷]/.test(text)
  }

  hasEmotion(text) {
    return /[高兴|悲伤|愤怒|恐惧|惊讶|厌恶|喜悦|痛苦]/.test(text)
  }

  hasConflict(text) {
    return /[冲突|矛盾|争吵|战斗|对抗]/.test(text)
  }

  detectTense(text) {
    if (/[了|过|曾经]/.test(text)) return 'past'
    if (/[将|会|要]/.test(text)) return 'future'
    return 'present'
  }

  detectPerspective(text) {
    if (/[我|我们]/.test(text)) return 'first'
    if (/[你|您]/.test(text)) return 'second'
    return 'third'
  }

  detectTone(text) {
    if (/[请|您|敬]/.test(text)) return 'formal'
    if (/[呢|吧|啊]/.test(text)) return 'casual'
    return 'neutral'
  }

  calculateComplexity(text) {
    const avgSentenceLength = text.length / Math.max(this.countSentences(text), 1)
    const complexWords = (text.match(/[\u4e00-\u9fa5]{4,}/g) || []).length
    return Math.min((avgSentenceLength + complexWords) / 50, 1.0)
  }

  extractKeywords(text) {
    // 简化的关键词提取
    const keywords = []
    const patterns = {
      '角色': /[主角|配角|反派|英雄|villain]/,
      '情节': /[开始|发展|高潮|结局|转折]/,
      '情感': /[爱情|友情|亲情|仇恨|嫉妒]/,
      '场景': /[室内|室外|城市|乡村|古代|现代]/
    }
    
    for (const [keyword, pattern] of Object.entries(patterns)) {
      if (pattern.test(text)) {
        keywords.push(keyword)
      }
    }
    
    return keywords
  }

  /**
   * 初始化问题模板
   */
  initializeQuestionTemplates() {
    return {
      keywords: {
        '角色': [
          '角色的性格是否鲜明？',
          '角色的行为是否符合设定？',
          '角色发展是否有层次？'
        ],
        '情节': [
          '情节发展是否合理？',
          '转折是否自然？',
          '节奏是否合适？'
        ],
        '情感': [
          '情感表达是否真实？',
          '情感变化是否自然？',
          '情感强度是否合适？'
        ],
        '场景': [
          '场景描写是否生动？',
          '环境是否与情节匹配？',
          '氛围营造是否到位？'
        ]
      }
    }
  }

  /**
   * 加载用户偏好
   */
  loadUserPreferences() {
    try {
      const saved = localStorage.getItem('smartQuestionPreferences')
      if (saved) {
        return JSON.parse(saved)
      }
    } catch (error) {
      console.error('加载用户偏好失败:', error)
    }
    
    return {
      questionCategories: {
        '角色': 0.8,
        '情节': 0.9,
        '情感': 0.7,
        '场景': 0.6,
        '对话': 0.8,
        '描写': 0.7
      },
      preferredQuestionTypes: ['具体性', '建设性', '开放性'],
      avoidedQuestionTypes: ['过于宽泛', '重复性']
    }
  }

  /**
   * 保存用户偏好
   */
  saveUserPreferences(preferences) {
    try {
      localStorage.setItem('smartQuestionPreferences', JSON.stringify(preferences))
      this.userPreferences = preferences
    } catch (error) {
      console.error('保存用户偏好失败:', error)
    }
  }

  /**
   * 学习用户选择
   */
  learnFromUserChoice(selectedQuestion, allQuestions, context) {
    // 分析用户选择的问题类型
    const questionType = this.categorizeQuestion(selectedQuestion)
    
    // 更新偏好权重
    if (this.userPreferences.questionCategories[questionType]) {
      this.userPreferences.questionCategories[questionType] = Math.min(
        this.userPreferences.questionCategories[questionType] + 0.1,
        1.0
      )
    }
    
    // 保存更新的偏好
    this.saveUserPreferences(this.userPreferences)
  }

  /**
   * 问题分类
   */
  categorizeQuestion(question) {
    const categories = {
      '角色': /[角色|性格|人物|主角|配角]/,
      '情节': /[情节|剧情|故事|发展|转折]/,
      '情感': /[情感|感情|情绪|心理]/,
      '场景': /[场景|环境|背景|氛围]/,
      '对话': /[对话|台词|说话|交流]/,
      '描写': /[描写|描述|刻画|表现]/
    }
    
    for (const [category, pattern] of Object.entries(categories)) {
      if (pattern.test(question)) {
        return category
      }
    }
    
    return '通用'
  }
}

/**
 * 上下文分析器
 */
class ContextAnalyzer {
  analyze(context) {
    return {
      chapterPosition: this.detectChapterPosition(context),
      storyArc: this.detectStoryArc(context),
      characterFocus: this.detectCharacterFocus(context),
      thematicElements: this.detectThematicElements(context)
    }
  }

  detectChapterPosition(context) {
    // 简化实现
    if (context.position < 0.2) return 'beginning'
    if (context.position > 0.8) return 'ending'
    if (context.position > 0.4 && context.position < 0.6) return 'climax'
    return 'middle'
  }

  detectStoryArc(context) {
    return context.storyArc || 'unknown'
  }

  detectCharacterFocus(context) {
    return context.characterFocus || 'unknown'
  }

  detectThematicElements(context) {
    return context.thematicElements || []
  }
}

// 创建单例实例
export const smartQuestionService = new SmartQuestionService()
export default smartQuestionService
