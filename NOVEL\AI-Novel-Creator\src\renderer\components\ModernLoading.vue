<template>
  <div class="modern-loading" :class="{ 'fullscreen': fullscreen }">
    <div class="loading-container glass-card">
      <!-- 水墨风格加载动画 -->
      <div class="ink-loading">
        <div class="ink-drop" v-for="i in 5" :key="i" :style="{ animationDelay: (i * 0.2) + 's' }"></div>
      </div>
      
      <!-- 加载文字 -->
      <div class="loading-content">
        <h3 class="loading-title gradient-text">{{ title }}</h3>
        <p class="loading-text">{{ text }}</p>
        
        <!-- 进度条 -->
        <div v-if="showProgress" class="progress-container">
          <div class="progress-bar">
            <div 
              class="progress-fill"
              :style="{ width: progress + '%' }"
            ></div>
          </div>
          <span class="progress-text">{{ progress }}%</span>
        </div>
      </div>
      
      <!-- 取消按钮 -->
      <button 
        v-if="cancelable"
        class="cancel-btn neumorphism-btn"
        @click="$emit('cancel')"
      >
        <el-icon><Close /></el-icon>
        取消
      </button>
    </div>
    
    <!-- 背景遮罩 -->
    <div v-if="fullscreen" class="loading-backdrop" @click="handleBackdropClick"></div>
  </div>
</template>

<script>
export default {
  name: 'ModernLoading',
  props: {
    title: {
      type: String,
      default: '正在处理'
    },
    text: {
      type: String,
      default: '请稍候...'
    },
    progress: {
      type: Number,
      default: 0,
      validator: (value) => value >= 0 && value <= 100
    },
    showProgress: {
      type: Boolean,
      default: false
    },
    fullscreen: {
      type: Boolean,
      default: false
    },
    cancelable: {
      type: Boolean,
      default: false
    },
    backdropClosable: {
      type: Boolean,
      default: false
    }
  },
  emits: ['cancel', 'backdrop-click'],
  setup(props, { emit }) {
    const handleBackdropClick = () => {
      if (props.backdropClosable) {
        emit('backdrop-click')
      }
    }

    return {
      handleBackdropClick
    }
  }
}
</script>

<style scoped>
/* 引入水墨书香主题 */
@import '@/assets/styles/modern-theme.css';

.modern-loading {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

.modern-loading.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(4px);
}

.loading-container {
  position: relative;
  z-index: 10000;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-lg);
  padding: var(--spacing-3xl);
  background: var(--glass-heavy);
  backdrop-filter: var(--backdrop-blur);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-paper-xl);
  min-width: 320px;
  max-width: 480px;
  text-align: center;
}

/* 水墨风格加载动画 */
.ink-loading {
  position: relative;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ink-drop {
  position: absolute;
  width: 12px;
  height: 12px;
  background: var(--gradient-gold);
  border-radius: 50%;
  animation: ink-flow 2s ease-in-out infinite;
}

.ink-drop:nth-child(1) { transform: rotate(0deg) translateX(30px); }
.ink-drop:nth-child(2) { transform: rotate(72deg) translateX(30px); }
.ink-drop:nth-child(3) { transform: rotate(144deg) translateX(30px); }
.ink-drop:nth-child(4) { transform: rotate(216deg) translateX(30px); }
.ink-drop:nth-child(5) { transform: rotate(288deg) translateX(30px); }

@keyframes ink-flow {
  0%, 100% {
    opacity: 0.3;
    transform: rotate(var(--rotation, 0deg)) translateX(30px) scale(0.8);
  }
  50% {
    opacity: 1;
    transform: rotate(var(--rotation, 0deg)) translateX(30px) scale(1.2);
  }
}

/* 加载内容 */
.loading-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  width: 100%;
}

.loading-title {
  font-family: var(--font-calligraphy);
  font-size: 20px;
  font-weight: var(--font-weight-semibold);
  margin: 0;
  line-height: 1.2;
}

.loading-text {
  font-family: var(--font-elegant);
  font-size: 14px;
  color: var(--text-muted);
  margin: 0;
  line-height: 1.4;
}

/* 进度条 */
.progress-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  width: 100%;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background: var(--bg-elevated);
  border-radius: var(--radius-full);
  overflow: hidden;
  box-shadow: var(--shadow-inset-light);
}

.progress-fill {
  height: 100%;
  background: var(--gradient-gold);
  border-radius: var(--radius-full);
  transition: width var(--duration-normal) var(--ease-paper);
  position: relative;
  overflow: hidden;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: progress-shimmer 1.5s ease-in-out infinite;
}

@keyframes progress-shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.progress-text {
  font-family: var(--font-elegant);
  font-size: 12px;
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
  min-width: 40px;
  text-align: right;
}

/* 取消按钮 */
.cancel-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-lg);
  font-family: var(--font-elegant);
  font-size: 14px;
  color: var(--text-muted);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-paper);
}

.cancel-btn:hover {
  color: var(--text-primary);
  transform: translateY(-1px);
}

/* 背景遮罩 */
.loading-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .loading-container {
    min-width: 280px;
    max-width: calc(100vw - 32px);
    padding: var(--spacing-xl);
  }
  
  .ink-loading {
    width: 60px;
    height: 60px;
  }
  
  .ink-drop {
    width: 10px;
    height: 10px;
  }
  
  .ink-drop:nth-child(1) { transform: rotate(0deg) translateX(25px); }
  .ink-drop:nth-child(2) { transform: rotate(72deg) translateX(25px); }
  .ink-drop:nth-child(3) { transform: rotate(144deg) translateX(25px); }
  .ink-drop:nth-child(4) { transform: rotate(216deg) translateX(25px); }
  .ink-drop:nth-child(5) { transform: rotate(288deg) translateX(25px); }
  
  .loading-title {
    font-size: 18px;
  }
  
  .loading-text {
    font-size: 13px;
  }
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
  .loading-container {
    background: rgba(26, 26, 26, 0.9);
    border-color: rgba(255, 255, 255, 0.1);
  }
  
  .progress-bar {
    background: rgba(255, 255, 255, 0.1);
  }
}
</style>
