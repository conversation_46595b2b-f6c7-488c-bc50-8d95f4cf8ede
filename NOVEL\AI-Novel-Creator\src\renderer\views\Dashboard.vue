<template>
  <div class="ink-dashboard">
    <!-- 书卷展开式欢迎区域 -->
    <div class="scroll-welcome fade-in-up">
      <div class="scroll-container beautiful-card glow-effect">
        <!-- 书卷顶部装饰 -->
        <div class="scroll-header">
          <div class="scroll-rod left"></div>
          <div class="scroll-title">
            <h1 class="main-title gradient-text elegant">墨韵文轩</h1>
            <p class="sub-title">AI助力，妙笔生花</p>
          </div>
          <div class="scroll-rod right floating"></div>
        </div>

        <!-- 书卷内容 -->
        <div class="scroll-content">
          <!-- 欢迎诗句 -->
          <div class="welcome-poem">
            <div class="poem-line">
              <span class="poem-text">挥毫落纸如云烟</span>
              <span class="poem-punctuation">，</span>
            </div>
            <div class="poem-line">
              <span class="poem-text">智能助力著华篇</span>
              <span class="poem-punctuation">。</span>
            </div>
          </div>

          <!-- AI状态指示 -->
          <div class="ai-status-section">
            <div class="status-indicator" :class="aiStatusClass">
              <div class="status-icon">
                <el-icon v-if="isConfigured"><CircleCheck /></el-icon>
                <el-icon v-else><WarningFilled /></el-icon>
              </div>
              <div class="status-info">
                <span class="status-title">{{ aiStatusText }}</span>
                <span class="status-desc">{{ aiStatusDesc }}</span>
              </div>
            </div>
          </div>

          <!-- 快捷操作按钮 -->
          <div class="action-buttons">
            <button
              class="ink-action-btn primary beautiful-card rainbow-border"
              @click="$router.push('/auto-create')"
              :disabled="!isConfigured"
              :title="!isConfigured ? '请先配置AI服务' : ''"
            >
              <div class="btn-icon floating">
                <el-icon><StarFilled /></el-icon>
              </div>
              <div class="btn-content">
                <span class="btn-title gradient-text purple">妙笔生花</span>
                <span class="btn-desc">AI全自动创作</span>
              </div>
            </button>

            <button
              class="ink-action-btn secondary elegant-card"
              @click="handleOpenEditor"
            >
              <div class="btn-icon">
                <el-icon><EditPen /></el-icon>
              </div>
              <div class="btn-content">
                <span class="btn-title gradient-text jade">挥毫泼墨</span>
                <span class="btn-desc">智能编辑器</span>
              </div>
            </button>

            <button
              class="ink-action-btn tertiary elegant-card"
              @click="$router.push('/projects')"
            >
              <div class="btn-icon">
                <el-icon><Document /></el-icon>
              </div>
              <div class="btn-content">
                <span class="btn-title gradient-text warm">书案管理</span>
                <span class="btn-desc">项目管理</span>
              </div>
            </button>
          </div>

          <!-- 配置提示 -->
          <div v-if="!isConfigured" class="config-notice">
            <div class="notice-content">
              <div class="notice-icon">
                <el-icon><InfoFilled /></el-icon>
              </div>
              <div class="notice-text">
                <p class="notice-title">初次使用提醒</p>
                <p class="notice-desc">请先前往文房设置配置AI服务，方可开启创作之旅</p>
              </div>
              <button class="notice-btn" @click="$router.push('/settings')">
                前往配置
              </button>
            </div>
          </div>
        </div>

        <!-- 书卷底部装饰 -->
        <div class="scroll-footer">
          <div class="scroll-pattern"></div>
        </div>
      </div>
    </div>

    <!-- 文房四宝统计 -->
    <div class="four-treasures">
      <div class="treasures-title">
        <h2 class="section-title">文房四宝</h2>
        <p class="section-subtitle">创作数据一览</p>
      </div>

      <div class="treasures-grid">
        <!-- 笔 - 项目总数 -->
        <div class="treasure-card brush" @click="$router.push('/projects')">
          <div class="treasure-icon">
            <div class="icon-bg">
              <el-icon><Document /></el-icon>
            </div>
          </div>
          <div class="treasure-content">
            <div class="treasure-number">{{ projectStats.total }}</div>
            <div class="treasure-label">笔·项目</div>
            <div class="treasure-desc">创作项目总数</div>
          </div>
          <div class="treasure-accent"></div>
        </div>

        <!-- 墨 - 总章节数 -->
        <div class="treasure-card ink">
          <div class="treasure-icon">
            <div class="icon-bg">
              <el-icon><Edit /></el-icon>
            </div>
          </div>
          <div class="treasure-content">
            <div class="treasure-number">{{ projectStats.totalChapters }}</div>
            <div class="treasure-label">墨·章节</div>
            <div class="treasure-desc">已创作章节</div>
          </div>
          <div class="treasure-accent"></div>
        </div>

        <!-- 纸 - 总字数 -->
        <div class="treasure-card paper">
          <div class="treasure-icon">
            <div class="icon-bg">
              <el-icon><Notebook /></el-icon>
            </div>
          </div>
          <div class="treasure-content">
            <div class="treasure-number">{{ formatNumber(projectStats.totalWords) }}</div>
            <div class="treasure-label">纸·文字</div>
            <div class="treasure-desc">累计字数</div>
          </div>
          <div class="treasure-accent"></div>
        </div>

        <!-- 砚 - 今日字数 -->
        <div class="treasure-card inkstone">
          <div class="treasure-icon">
            <div class="icon-bg">
              <el-icon><Calendar /></el-icon>
            </div>
          </div>
          <div class="treasure-content">
            <div class="treasure-number">{{ formatNumber(todayStats.words) }}</div>
            <div class="treasure-label">砚·今日</div>
            <div class="treasure-desc">今日创作</div>
          </div>
          <div class="treasure-accent"></div>
        </div>
      </div>
    </div>

    <!-- 双栏布局：最近项目 & 创作进度 -->
    <div class="content-columns">
      <!-- 最近项目 - 书案 -->
      <div class="column-card recent-works">
        <div class="card-header">
          <div class="header-title">
            <h3 class="card-title">近期书案</h3>
            <p class="card-subtitle">最近创作的项目</p>
          </div>
          <button class="header-action" @click="$router.push('/projects')">
            <span>查看全部</span>
            <el-icon><Right /></el-icon>
          </button>
        </div>

        <div class="card-content">
          <div v-if="recentProjects.length === 0" class="empty-works">
            <div class="empty-icon">
              <el-icon><Document /></el-icon>
            </div>
            <p class="empty-text">书案空空，待君挥毫</p>
            <button class="empty-action" @click="$router.push('/auto-create')">
              开始创作
            </button>
          </div>

          <div v-else class="works-list">
            <div
              v-for="project in recentProjects"
              :key="project.id"
              class="work-item"
              @click="openProject(project)"
            >
              <div class="work-header">
                <h4 class="work-title">{{ project.title }}</h4>
                <span class="work-genre">{{ project.genre }}</span>
              </div>
              <div class="work-meta">
                <div class="work-stats">
                  <span class="stat">{{ project.chaptersCount || 0 }}章</span>
                  <span class="separator">·</span>
                  <span class="stat">{{ formatNumber(project.totalWords || 0) }}字</span>
                </div>
                <div class="work-time">{{ formatTime(project.updatedAt) }}</div>
              </div>
              <div class="work-progress">
                <div class="progress-bar">
                  <div
                    class="progress-fill"
                    :style="{ width: getProjectProgress(project) + '%' }"
                  ></div>
                </div>
                <span class="progress-text">{{ getProjectProgress(project) }}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 创作进度 - 当前项目 -->
      <div class="column-card current-progress">
        <div class="card-header">
          <div class="header-title">
            <h3 class="card-title">当前进度</h3>
            <p class="card-subtitle">正在创作的项目</p>
          </div>
          <button class="header-action" @click="refreshProgress">
            <el-icon><Refresh /></el-icon>
          </button>
        </div>

        <div class="card-content">
          <div v-if="currentProject" class="current-work">
            <div class="work-banner">
              <div class="banner-content">
                <h4 class="current-title">{{ currentProject.title }}</h4>
                <div class="current-meta">
                  <span class="current-genre">{{ currentProject.genre }}</span>
                  <span class="current-category">{{ currentProject.category }}</span>
                </div>
              </div>
              <div class="banner-decoration"></div>
            </div>

            <div class="progress-details">
              <div class="progress-main">
                <div class="progress-label">章节进度</div>
                <div class="progress-visual">
                  <div class="progress-track">
                    <div
                      class="progress-bar-fill"
                      :style="{ width: chapterProgress + '%' }"
                    ></div>
                  </div>
                  <span class="progress-percentage">{{ chapterProgress }}%</span>
                </div>
              </div>

              <div class="progress-stats">
                <div class="stat-group">
                  <div class="stat-item">
                    <span class="stat-label">已完成</span>
                    <span class="stat-value">{{ currentProjectStats.completed }}</span>
                    <span class="stat-unit">章</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">总计划</span>
                    <span class="stat-value">{{ currentProject.totalChapters || 50 }}</span>
                    <span class="stat-unit">章</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">总字数</span>
                    <span class="stat-value">{{ formatNumber(currentProjectStats.totalWords) }}</span>
                    <span class="stat-unit">字</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div v-else class="no-current">
            <div class="no-current-icon">
              <el-icon><Edit /></el-icon>
            </div>
            <p class="no-current-text">选择项目，开始创作</p>
            <button class="no-current-action" @click="$router.push('/projects')">
              选择项目
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 快捷功能 -->
    <div class="shortcuts-section">
      <el-card>
        <template #header>
          <span>快捷功能</span>
        </template>
        
        <div class="shortcuts">
          <div class="shortcut-item" @click="$router.push('/auto-create')">
            <el-icon size="24" color="#409eff"><Star /></el-icon>
            <span>全自动创作</span>
            <p>AI自动生成大纲和内容</p>
          </div>
          
          <div class="shortcut-item" @click="handleOpenEditor">
            <el-icon size="24" color="#67c23a"><Edit /></el-icon>
            <span>智能编辑</span>
            <p>AI辅助写作和优化</p>
          </div>
          
          <div class="shortcut-item" @click="$router.push('/projects')">
            <el-icon size="24" color="#e6a23c"><FolderOpened /></el-icon>
            <span>项目管理</span>
            <p>管理所有创作项目</p>
          </div>
          
          <div class="shortcut-item" @click="$router.push('/settings')">
            <el-icon size="24" color="#909399"><Setting /></el-icon>
            <span>应用设置</span>
            <p>配置API Key和偏好</p>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { timeUtils } from '@/utils'

export default {
  name: 'Dashboard',
  setup() {
    const router = useRouter()

    // 模拟store数据
    const mockAppStore = {
      isConfigured: false
    }

    const mockProjectStore = {
      projects: []
    }
    
    // 检查AI配置状态
    const isConfigured = computed(() => mockAppStore.isConfigured)

    // AI状态相关
    const aiStatusClass = computed(() => {
      return isConfigured.value ? 'configured' : 'not-configured'
    })

    const aiStatusText = computed(() => {
      return isConfigured.value ? 'AI笔墨就绪' : '待配置笔墨'
    })

    const aiStatusDesc = computed(() => {
      return isConfigured.value ? '可以开始创作' : '需要配置API Key'
    })

    // 项目统计
    const projectStats = computed(() => {
      const stats = {
        total: mockProjectStore.projects.length,
        totalChapters: 0,
        totalWords: 0
      }

      mockProjectStore.projects.forEach(project => {
        stats.totalChapters += project.chaptersCount || 0
        stats.totalWords += project.totalWords || 0
      })

      return stats
    })

    // 今日统计
    const todayStats = computed(() => {
      // 这里应该从数据库获取今日写作统计
      // 暂时返回模拟数据
      return {
        words: 0,
        chapters: 0
      }
    })

    // 获取项目进度
    const getProjectProgress = (project) => {
      if (!project.totalChapters) return 0
      const completed = project.chaptersCount || 0
      return Math.round((completed / project.totalChapters) * 100)
    }
    
    // 最近项目（最多显示5个）
    const recentProjects = computed(() => {
      return mockProjectStore.projects
        .sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt))
        .slice(0, 5)
    })
    
    // 当前项目
    const currentProject = computed(() => mockProjectStore.projects[0] || null)
    
    // 当前项目统计
    const currentProjectStats = computed(() => {
      if (!currentProject.value) {
        return { completed: 0, totalWords: 0 }
      }

      return {
        completed: currentProject.value.chaptersCount || 0,
        totalWords: currentProject.value.totalWords || 0
      }
    })
    
    // 章节进度
    const chapterProgress = computed(() => {
      if (!currentProject.value) return 0
      
      const total = currentProject.value.totalChapters || 50
      const completed = currentProjectStats.value.completed
      return Math.round((completed / total) * 100)
    })
    
    // 格式化数字
    const formatNumber = (num) => {
      if (!num) return '0'
      if (num >= 10000) {
        return (num / 10000).toFixed(1) + '万'
      }
      return num.toString()
    }
    
    // 格式化时间
    const formatTime = (time) => {
      if (!time) return '未知时间'
      const date = new Date(time)
      const now = new Date()
      const diff = now - date
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))

      if (days === 0) return '今天'
      if (days === 1) return '昨天'
      if (days < 7) return `${days}天前`
      return date.toLocaleDateString('zh-CN')
    }
    
    // 打开项目
    const openProject = async (project) => {
      try {
        // 模拟设置当前项目
        
        ElMessage({
          message: `已打开项目：${project.title}`,
          type: 'success'
        })
        
        // 跳转到编辑器
        router.push('/editor')
      } catch (error) {
        ElMessage({
          message: '打开项目失败',
          type: 'error'
        })
      }
    }
    
    // 打开编辑器
    const handleOpenEditor = () => {
      if (currentProject.value) {
        router.push('/editor')
      } else if (recentProjects.value.length > 0) {
        // 如果没有当前项目但有最近项目，选择最近的项目
        openProject(recentProjects.value[0])
      } else {
        // 如果没有任何项目，提示创建项目
        ElMessage({
          message: '请先创建一个项目',
          type: 'warning'
        })
        router.push('/auto-create')
      }
    }
    
    // 刷新进度
    const refreshProgress = async () => {
      if (currentProject.value) {
        try {
          // 模拟刷新进度
          ElMessage({
            message: '进度已刷新',
            type: 'success'
          })
        } catch (error) {
          ElMessage({
            message: '刷新失败',
            type: 'error'
          })
        }
      }
    }
    
    // 初始化
    onMounted(async () => {
      console.log('Dashboard initializing...')
      try {
        // 加载项目列表
        await projectStore.loadProjects()

        // 如果有当前项目，加载章节信息
        if (currentProject.value) {
          await projectStore.loadProjectChapters(currentProject.value.id)
        }

        console.log('Dashboard initialization completed')
      } catch (error) {
        console.error('Dashboard initialization failed:', error)
      }
    })
    
    return {
      isConfigured,
      aiStatusClass,
      aiStatusText,
      aiStatusDesc,
      projectStats,
      todayStats,
      recentProjects,
      currentProject,
      currentProjectStats,
      chapterProgress,
      formatNumber,
      formatTime,
      getProjectProgress,
      openProject,
      handleOpenEditor,
      refreshProgress
    }
  }
}
</script>

<style scoped>
/* === 水墨书香Dashboard样式 === */
.ink-dashboard {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0;
  min-height: 100vh;
  position: relative;
}

/* === 书卷展开式欢迎区域 === */
.scroll-welcome {
  margin-bottom: var(--spacing-3xl);
  position: relative;
}

.scroll-container {
  background: var(--gradient-paper);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-3xl);
  box-shadow: var(--shadow-paper-xl);
  position: relative;
  overflow: hidden;
  padding: var(--spacing-3xl) var(--spacing-2xl);
}

.scroll-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 30%, rgba(212, 175, 55, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(74, 103, 65, 0.02) 0%, transparent 50%);
  pointer-events: none;
}

/* 书卷顶部装饰 */
.scroll-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-3xl);
  position: relative;
}

.scroll-rod {
  width: 80px;
  height: 8px;
  background: var(--gradient-gold);
  border-radius: var(--radius-full);
  position: relative;
}

.scroll-rod::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -4px;
  right: -4px;
  bottom: -2px;
  background: var(--ink-zhong);
  border-radius: var(--radius-full);
  z-index: -1;
}

.scroll-title {
  text-align: center;
  margin: 0 var(--spacing-xl);
  position: relative;
}

.main-title {
  font-family: var(--font-calligraphy);
  font-size: 42px;
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-sm) 0;
  letter-spacing: 0.05em;
  text-shadow: 0 2px 8px rgba(13, 13, 13, 0.1);
}

.sub-title {
  font-family: var(--font-elegant);
  font-size: 16px;
  color: var(--text-muted);
  margin: 0;
  letter-spacing: 0.02em;
}

/* 书卷内容 */
.scroll-content {
  position: relative;
  z-index: 10;
}

/* 欢迎诗句 */
.welcome-poem {
  text-align: center;
  margin-bottom: var(--spacing-3xl);
  padding: var(--spacing-xl);
  background: rgba(247, 245, 241, 0.3);
  border-radius: var(--radius-xl);
  border: 1px solid var(--border-hair);
}

.poem-line {
  display: flex;
  justify-content: center;
  align-items: baseline;
  margin-bottom: var(--spacing-md);
  font-family: var(--font-calligraphy);
  font-size: 20px;
  color: var(--text-secondary);
}

.poem-line:last-child {
  margin-bottom: 0;
}

.poem-text {
  letter-spacing: 0.1em;
  position: relative;
}

.poem-punctuation {
  color: var(--huang-jin);
  font-weight: var(--font-weight-bold);
  margin-left: var(--spacing-xs);
}

/* AI状态指示 */
.ai-status-section {
  display: flex;
  justify-content: center;
  margin-bottom: var(--spacing-3xl);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-lg) var(--spacing-xl);
  background: var(--bg-primary);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-paper-md);
  transition: all var(--duration-normal) var(--ease-paper);
}

.status-indicator.configured {
  border-color: var(--song-lv);
  background: rgba(74, 103, 65, 0.05);
}

.status-indicator.not-configured {
  border-color: var(--huang-jin);
  background: rgba(212, 175, 55, 0.05);
}

.status-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.status-indicator.configured .status-icon {
  background: var(--song-lv);
  color: var(--paper-xuan);
}

.status-indicator.not-configured .status-icon {
  background: var(--huang-jin);
  color: var(--ink-jiao);
}

.status-info {
  display: flex;
  flex-direction: column;
}

.status-title {
  font-family: var(--font-elegant);
  font-size: 16px;
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.status-desc {
  font-family: var(--font-elegant);
  font-size: 13px;
  color: var(--text-muted);
}

/* 快捷操作按钮 */
.action-buttons {
  display: flex;
  justify-content: center;
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-3xl);
  flex-wrap: wrap;
}

.ink-action-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-lg) var(--spacing-xl);
  background: var(--bg-primary);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-xl);
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-paper);
  text-decoration: none;
  color: inherit;
  position: relative;
  overflow: hidden;
  min-width: 200px;
}

.ink-action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  opacity: 0.1;
  transition: left var(--duration-slow) var(--ease-brush);
}

.ink-action-btn:hover::before {
  left: 0;
}

.ink-action-btn.primary {
  border-color: var(--huang-jin);
}

.ink-action-btn.primary::before {
  background: var(--gradient-gold);
}

.ink-action-btn.primary:hover {
  border-color: var(--huang-jin);
  box-shadow: var(--shadow-paper-lg);
  transform: translateY(-3px);
}

.ink-action-btn.secondary {
  border-color: var(--song-lv);
}

.ink-action-btn.secondary::before {
  background: var(--gradient-jade);
}

.ink-action-btn.secondary:hover {
  border-color: var(--song-lv);
  box-shadow: var(--shadow-paper-lg);
  transform: translateY(-3px);
}

.ink-action-btn.tertiary {
  border-color: var(--ink-zhong);
}

.ink-action-btn.tertiary::before {
  background: var(--gradient-ink);
}

.ink-action-btn.tertiary:hover {
  border-color: var(--ink-zhong);
  box-shadow: var(--shadow-paper-lg);
  transform: translateY(-3px);
}

.ink-action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

.btn-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  flex-shrink: 0;
}

.ink-action-btn.primary .btn-icon {
  background: var(--huang-jin);
  color: var(--ink-jiao);
}

.ink-action-btn.secondary .btn-icon {
  background: var(--song-lv);
  color: var(--paper-xuan);
}

.ink-action-btn.tertiary .btn-icon {
  background: var(--ink-zhong);
  color: var(--paper-xuan);
}

.btn-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.btn-title {
  font-family: var(--font-calligraphy);
  font-size: 16px;
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.btn-desc {
  font-family: var(--font-elegant);
  font-size: 13px;
  color: var(--text-muted);
}

/* 配置提示 */
.config-notice {
  background: rgba(212, 175, 55, 0.05);
  border: 1px solid rgba(212, 175, 55, 0.2);
  border-radius: var(--radius-xl);
  padding: var(--spacing-lg);
}

.notice-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.notice-icon {
  width: 32px;
  height: 32px;
  background: var(--huang-jin);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--ink-jiao);
  font-size: 16px;
  flex-shrink: 0;
}

.notice-text {
  flex: 1;
}

.notice-title {
  font-family: var(--font-elegant);
  font-size: 14px;
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xs) 0;
}

.notice-desc {
  font-family: var(--font-elegant);
  font-size: 13px;
  color: var(--text-muted);
  margin: 0;
  line-height: 1.5;
}

.notice-btn {
  background: var(--huang-jin);
  color: var(--ink-jiao);
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--spacing-sm) var(--spacing-lg);
  font-family: var(--font-elegant);
  font-weight: var(--font-weight-medium);
  font-size: 13px;
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-paper);
  flex-shrink: 0;
}

.notice-btn:hover {
  background: #f1c40f;
  transform: translateY(-1px);
}

/* 书卷底部装饰 */
.scroll-footer {
  margin-top: var(--spacing-3xl);
  display: flex;
  justify-content: center;
}

.scroll-pattern {
  width: 200px;
  height: 4px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    var(--huang-jin) 20%,
    var(--song-lv) 40%,
    var(--huang-jin) 60%,
    var(--song-lv) 80%,
    transparent 100%
  );
  border-radius: var(--radius-full);
  opacity: 0.6;
}

/* === 文房四宝统计 === */
.four-treasures {
  margin-bottom: var(--spacing-3xl);
}

.treasures-title {
  text-align: center;
  margin-bottom: var(--spacing-3xl);
}

.section-title {
  font-family: var(--font-calligraphy);
  font-size: 28px;
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-sm) 0;
  letter-spacing: 0.03em;
}

.section-subtitle {
  font-family: var(--font-elegant);
  font-size: 14px;
  color: var(--text-muted);
  margin: 0;
}

.treasures-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-xl);
}

.treasure-card {
  background: var(--bg-primary);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-paper);
  position: relative;
  overflow: hidden;
}

.treasure-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  opacity: 0.6;
  transition: opacity var(--duration-normal) var(--ease-paper);
}

.treasure-card.brush::before {
  background: var(--gradient-gold);
}

.treasure-card.ink::before {
  background: var(--gradient-ink);
}

.treasure-card.paper::before {
  background: var(--gradient-jade);
}

.treasure-card.inkstone::before {
  background: linear-gradient(90deg, var(--zhu-sha) 0%, var(--yan-zhi) 100%);
}

.treasure-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-paper-xl);
}

.treasure-card:hover::before {
  opacity: 1;
}

.treasure-icon {
  margin-bottom: var(--spacing-lg);
}

.icon-bg {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  position: relative;
}

.treasure-card.brush .icon-bg {
  background: var(--huang-jin);
  color: var(--ink-jiao);
}

.treasure-card.ink .icon-bg {
  background: var(--ink-zhong);
  color: var(--paper-xuan);
}

.treasure-card.paper .icon-bg {
  background: var(--song-lv);
  color: var(--paper-xuan);
}

.treasure-card.inkstone .icon-bg {
  background: var(--zhu-sha);
  color: var(--paper-xuan);
}

.treasure-content {
  position: relative;
}

.treasure-number {
  font-family: var(--font-calligraphy);
  font-size: 36px;
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
  line-height: 1;
}

.treasure-label {
  font-family: var(--font-calligraphy);
  font-size: 16px;
  font-weight: var(--font-weight-semibold);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.treasure-desc {
  font-family: var(--font-elegant);
  font-size: 13px;
  color: var(--text-muted);
  line-height: 1.4;
}

.treasure-accent {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 40px;
  border-radius: var(--radius-full);
  opacity: 0;
  transition: opacity var(--duration-normal) var(--ease-paper);
}

.treasure-card:hover .treasure-accent {
  opacity: 1;
}

.treasure-card.brush .treasure-accent {
  background: var(--huang-jin);
}

.treasure-card.ink .treasure-accent {
  background: var(--ink-zhong);
}

.treasure-card.paper .treasure-accent {
  background: var(--song-lv);
}

.treasure-card.inkstone .treasure-accent {
  background: var(--zhu-sha);
}

/* === 双栏布局 === */
.content-columns {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-3xl);
}

.column-card {
  background: var(--bg-primary);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-paper-md);
  transition: all var(--duration-normal) var(--ease-paper);
}

.column-card:hover {
  box-shadow: var(--shadow-paper-lg);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-lg);
  border-bottom: 1px solid var(--border-light);
  background: var(--gradient-paper);
}

.header-title {
  flex: 1;
}

.card-title {
  font-family: var(--font-calligraphy);
  font-size: 18px;
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xs) 0;
}

.card-subtitle {
  font-family: var(--font-elegant);
  font-size: 13px;
  color: var(--text-muted);
  margin: 0;
}

.header-action {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  background: transparent;
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--spacing-sm) var(--spacing-md);
  font-family: var(--font-elegant);
  font-size: 13px;
  color: var(--text-muted);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-paper);
}

.header-action:hover {
  border-color: var(--huang-jin);
  color: var(--huang-jin);
}

.card-content {
  padding: var(--spacing-xl);
}

/* 最近项目样式 */
.empty-works {
  text-align: center;
  padding: var(--spacing-3xl) var(--spacing-lg);
}

.empty-icon {
  width: 64px;
  height: 64px;
  background: var(--bg-elevated);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--spacing-lg);
  font-size: 24px;
  color: var(--text-placeholder);
}

.empty-text {
  font-family: var(--font-calligraphy);
  font-size: 16px;
  color: var(--text-muted);
  margin: 0 0 var(--spacing-lg) 0;
}

.empty-action {
  background: var(--huang-jin);
  color: var(--ink-jiao);
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--spacing-md) var(--spacing-xl);
  font-family: var(--font-elegant);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-paper);
}

.empty-action:hover {
  background: #f1c40f;
  transform: translateY(-2px);
}

.works-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.work-item {
  padding: var(--spacing-lg);
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-paper);
}

.work-item:hover {
  border-color: var(--huang-jin);
  transform: translateY(-2px);
  box-shadow: var(--shadow-paper-md);
}

.work-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.work-title {
  font-family: var(--font-calligraphy);
  font-size: 16px;
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0;
}

.work-genre {
  font-family: var(--font-elegant);
  font-size: 12px;
  color: var(--text-muted);
  background: var(--bg-primary);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-hair);
}

.work-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.work-stats {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-family: var(--font-elegant);
  font-size: 13px;
  color: var(--text-muted);
}

.separator {
  color: var(--text-placeholder);
}

.work-time {
  font-family: var(--font-elegant);
  font-size: 12px;
  color: var(--text-placeholder);
}

.work-progress {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.progress-bar {
  flex: 1;
  height: 4px;
  background: var(--bg-primary);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--gradient-jade);
  border-radius: var(--radius-full);
  transition: width var(--duration-normal) var(--ease-paper);
}

.progress-text {
  font-family: var(--font-elegant);
  font-size: 12px;
  color: var(--text-muted);
  font-weight: var(--font-weight-medium);
  min-width: 32px;
  text-align: right;
}

/* 当前进度样式 */
.current-work {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.work-banner {
  background: var(--gradient-paper);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  position: relative;
  overflow: hidden;
}

.banner-content {
  position: relative;
  z-index: 10;
}

.current-title {
  font-family: var(--font-calligraphy);
  font-size: 20px;
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-sm) 0;
}

.current-meta {
  display: flex;
  gap: var(--spacing-md);
}

.current-genre,
.current-category {
  font-family: var(--font-elegant);
  font-size: 13px;
  color: var(--text-muted);
  background: var(--bg-primary);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-hair);
}

.banner-decoration {
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100%;
  background: linear-gradient(45deg, transparent 0%, var(--huang-jin) 100%);
  opacity: 0.05;
}

.progress-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.progress-main {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.progress-label {
  font-family: var(--font-elegant);
  font-size: 14px;
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
}

.progress-visual {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.progress-track {
  flex: 1;
  height: 8px;
  background: var(--bg-elevated);
  border-radius: var(--radius-full);
  overflow: hidden;
  border: 1px solid var(--border-light);
}

.progress-bar-fill {
  height: 100%;
  background: var(--gradient-jade);
  border-radius: var(--radius-full);
  transition: width var(--duration-slow) var(--ease-paper);
  position: relative;
}

.progress-bar-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%);
  animation: inkFlow 2s ease infinite;
}

.progress-percentage {
  font-family: var(--font-elegant);
  font-size: 14px;
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  min-width: 40px;
  text-align: right;
}

.progress-stats {
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
}

.stat-group {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-lg);
}

.stat-item {
  text-align: center;
}

.stat-label {
  display: block;
  font-family: var(--font-elegant);
  font-size: 12px;
  color: var(--text-muted);
  margin-bottom: var(--spacing-xs);
}

.stat-value {
  font-family: var(--font-calligraphy);
  font-size: 24px;
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
}

.stat-unit {
  font-family: var(--font-elegant);
  font-size: 12px;
  color: var(--text-muted);
  margin-left: var(--spacing-xs);
}

.no-current {
  text-align: center;
  padding: var(--spacing-3xl) var(--spacing-lg);
}

.no-current-icon {
  width: 64px;
  height: 64px;
  background: var(--bg-elevated);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--spacing-lg);
  font-size: 24px;
  color: var(--text-placeholder);
}

.no-current-text {
  font-family: var(--font-calligraphy);
  font-size: 16px;
  color: var(--text-muted);
  margin: 0 0 var(--spacing-lg) 0;
}

.no-current-action {
  background: var(--song-lv);
  color: var(--paper-xuan);
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--spacing-md) var(--spacing-xl);
  font-family: var(--font-elegant);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-paper);
}

.no-current-action:hover {
  background: var(--dan-qing);
  transform: translateY(-2px);
}

/* === 响应式设计 === */
@media (max-width: 1024px) {
  .content-columns {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .treasures-grid {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  }
}

@media (max-width: 768px) {
  .scroll-container {
    padding: var(--spacing-xl) var(--spacing-lg);
  }

  .main-title {
    font-size: 32px;
  }

  .action-buttons {
    flex-direction: column;
    align-items: center;
  }

  .ink-action-btn {
    width: 100%;
    max-width: 300px;
  }

  .treasures-grid {
    grid-template-columns: 1fr;
  }

  .stat-group {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
}

@media (max-width: 480px) {
  .scroll-header {
    flex-direction: column;
    gap: var(--spacing-lg);
  }

  .scroll-rod {
    width: 60px;
    height: 6px;
  }

  .poem-line {
    font-size: 18px;
  }

  .card-content {
    padding: var(--spacing-lg);
  }
}
</style>