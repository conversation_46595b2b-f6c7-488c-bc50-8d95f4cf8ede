<template>
  <div class="ai-config-panel">
    <!-- 服务商选择 -->
    <el-form-item label="AI服务商">
      <el-select 
        v-model="config.provider" 
        @change="onProviderChange"
        style="width: 100%"
        placeholder="选择AI服务提供商"
      >
        <el-option
          v-for="(provider, key) in AI_PROVIDERS"
          :key="key"
          :label="provider.name"
          :value="key"
        >
          <div class="provider-option">
            <span class="provider-icon">{{ provider.icon }}</span>
            <div class="provider-info">
              <div class="provider-name">{{ provider.name }}</div>
              <div class="provider-desc">{{ provider.description }}</div>
            </div>
          </div>
        </el-option>
      </el-select>
    </el-form-item>

    <!-- API Key -->
    <el-form-item label="API Key">
      <el-input 
        v-model="config.apiKey"
        type="password"
        placeholder="请输入您的API Key"
        show-password
        clearable
      >
        <template #suffix>
          <el-tooltip content="API Key用于身份验证，请妥善保管" placement="top">
            <el-icon class="help-icon"><QuestionFilled /></el-icon>
          </el-tooltip>
        </template>
      </el-input>
    </el-form-item>

    <!-- API地址 -->
    <el-form-item label="API地址">
      <el-autocomplete
        v-model="config.apiUrl"
        :fetch-suggestions="getUrlSuggestions"
        placeholder="API服务地址（留空使用默认地址）"
        style="width: 100%"
        clearable
      >
        <template #suffix>
          <el-button 
            text 
            type="primary" 
            @click="useDefaultUrl"
            size="small"
          >
            使用默认
          </el-button>
        </template>
      </el-autocomplete>
      <div class="url-hint" v-if="currentProvider">
        <span class="hint-label">默认地址：</span>
        <code class="hint-url">{{ currentProvider.defaultUrl }}</code>
      </div>
    </el-form-item>

    <!-- 模型选择 -->
    <el-form-item label="模型选择">
      <div class="model-selection">
        <el-select 
          v-model="config.model" 
          style="width: 100%"
          placeholder="选择AI模型"
          filterable
          :filter-method="filterModels"
        >
          <el-option-group label="推荐模型">
            <el-option
              v-for="model in recommendedModels"
              :key="model.id"
              :label="model.name"
              :value="model.id"
            >
              <div class="model-option">
                <div class="model-header">
                  <span class="model-name">{{ model.name }}</span>
                  <el-tag size="small" type="success">推荐</el-tag>
                </div>
                <div class="model-desc">{{ model.description }}</div>
                <div class="model-tokens">最大上下文: {{ formatTokens(model.maxTokens) }}</div>
              </div>
            </el-option>
          </el-option-group>
          
          <el-option-group label="其他模型" v-if="otherModels.length > 0">
            <el-option
              v-for="model in otherModels"
              :key="model.id"
              :label="model.name"
              :value="model.id"
            >
              <div class="model-option">
                <div class="model-header">
                  <span class="model-name">{{ model.name }}</span>
                </div>
                <div class="model-desc">{{ model.description }}</div>
                <div class="model-tokens">最大上下文: {{ formatTokens(model.maxTokens) }}</div>
              </div>
            </el-option>
          </el-option-group>
        </el-select>
        
        <div class="model-actions">
          <el-button
            type="primary"
            @click="refreshModels"
            :loading="refreshing"
            size="small"
          >
            <el-icon><Refresh /></el-icon>
            刷新模型列表
          </el-button>

          <el-button
            type="info"
            @click="showModelInfo"
            size="small"
            text
          >
            <el-icon><InfoFilled /></el-icon>
            模型信息
          </el-button>
        </div>
      </div>
    </el-form-item>

    <!-- 配置验证 -->
    <el-form-item>
      <div class="config-actions">
        <el-button 
          type="primary" 
          @click="testConnection"
          :loading="testing"
          :disabled="!isConfigValid"
        >
          <el-icon><Connection /></el-icon>
          测试连接
        </el-button>
        
        <el-button 
          type="success" 
          @click="saveConfig"
          :disabled="!isConfigValid"
        >
          <el-icon><Check /></el-icon>
          保存配置
        </el-button>
      </div>
    </el-form-item>

    <!-- 配置状态 -->
    <div class="config-status" v-if="validationErrors.length > 0">
      <el-alert
        title="配置验证失败"
        type="warning"
        :closable="false"
        show-icon
      >
        <ul class="error-list">
          <li v-for="error in validationErrors" :key="error">{{ error }}</li>
        </ul>
      </el-alert>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  AI_PROVIDERS,
  searchModels,
  getRecommendedModels,
  getDefaultUrl,
  getCommonUrls,
  validateApiConfig
} from '@/utils/aiModels.js'
import { testAIConnection, fetchModelList } from '@/utils/aiTester.js'

export default {
  name: 'AIConfigPanel',
  props: {
    modelValue: {
      type: Object,
      default: () => ({
        provider: '',
        apiKey: '',
        apiUrl: '',
        model: ''
      })
    }
  },
  emits: ['update:modelValue', 'save', 'test'],
  setup(props, { emit }) {
    const config = ref({ ...props.modelValue })
    const testing = ref(false)
    const refreshing = ref(false)
    const modelFilter = ref('')

    // 动态模型列表
    const dynamicModels = ref({})

    // 当前选中的服务商
    const currentProvider = computed(() => {
      return config.value.provider ? AI_PROVIDERS[config.value.provider] : null
    })

    // 获取当前模型列表（优先使用动态获取的模型）
    const getCurrentModels = () => {
      if (!config.value.provider) return []

      // 如果有动态获取的模型，使用动态模型
      if (dynamicModels.value[config.value.provider]) {
        return dynamicModels.value[config.value.provider]
      }

      // 否则使用预定义模型
      return AI_PROVIDERS[config.value.provider]?.models || []
    }

    // 推荐模型
    const recommendedModels = computed(() => {
      const models = getCurrentModels()
      return models.filter(model => model.recommended)
    })

    // 其他模型
    const otherModels = computed(() => {
      const models = getCurrentModels()
      return models.filter(model => !model.recommended)
    })

    // 配置验证
    const validation = computed(() => validateApiConfig(config.value))
    const isConfigValid = computed(() => validation.value.isValid)
    const validationErrors = computed(() => validation.value.errors)

    // 监听配置变化
    watch(config, (newConfig) => {
      emit('update:modelValue', newConfig)
    }, { deep: true })

    // 服务商变化处理
    const onProviderChange = (provider) => {
      config.value.apiUrl = getDefaultUrl(provider)
      config.value.model = ''

      // 自动选择推荐模型
      const models = getCurrentModels()
      const recommended = models.filter(model => model.recommended)

      if (recommended.length > 0) {
        // 对于Gemini，优先选择2.0 Flash
        if (provider === 'gemini') {
          const gemini2Flash = recommended.find(m => m.id.includes('2.0-flash'))
          config.value.model = gemini2Flash ? gemini2Flash.id : recommended[0].id
        } else {
          config.value.model = recommended[0].id
        }
      }
    }

    // 使用默认URL
    const useDefaultUrl = () => {
      if (currentProvider.value) {
        config.value.apiUrl = currentProvider.value.defaultUrl
      }
    }

    // URL建议
    const getUrlSuggestions = (queryString, cb) => {
      if (!currentProvider.value) {
        cb([])
        return
      }
      
      const urls = getCommonUrls(config.value.provider)
      const suggestions = urls.map(url => ({ value: url }))
      
      if (queryString) {
        const filtered = suggestions.filter(item => 
          item.value.toLowerCase().includes(queryString.toLowerCase())
        )
        cb(filtered)
      } else {
        cb(suggestions)
      }
    }

    // 过滤模型
    const filterModels = (query) => {
      modelFilter.value = query
    }

    // 刷新模型列表
    const refreshModels = async () => {
      if (!config.value.provider) {
        ElMessage.warning('请先选择AI服务提供商')
        return
      }

      refreshing.value = true
      try {
        let models = []

        // 如果有API Key，尝试从API获取模型列表
        if (config.value.apiKey) {
          try {
            models = await fetchModelList(config.value)
            ElMessage.success(`从API获取到 ${models.length} 个可用模型`)
          } catch (apiError) {
            console.warn('从API获取模型失败，使用预定义列表:', apiError)
            // API获取失败时，使用预定义模型列表
            models = AI_PROVIDERS[config.value.provider]?.models || []
            ElMessage.info('使用预定义模型列表')
          }
        } else {
          // 没有API Key时，直接使用预定义模型列表
          models = AI_PROVIDERS[config.value.provider]?.models || []
          ElMessage.info('使用预定义模型列表（未提供API Key）')
        }

        // 更新动态模型列表
        dynamicModels.value[config.value.provider] = models

        // 如果当前选择的模型不在新列表中，自动选择推荐模型
        const modelIds = models.map(m => m.id)
        if (config.value.model && !modelIds.includes(config.value.model)) {
          const recommended = models.filter(model => model.recommended)
          if (recommended.length > 0) {
            // 对于Gemini，优先选择2.0 Flash
            if (config.value.provider === 'gemini') {
              const gemini2Flash = recommended.find(m => m.id.includes('2.0-flash'))
              config.value.model = gemini2Flash ? gemini2Flash.id : recommended[0].id
            } else {
              config.value.model = recommended[0].id
            }
            ElMessage.info(`已自动选择推荐模型: ${config.value.model}`)
          } else {
            config.value.model = ''
            ElMessage.info('当前选择的模型不可用，请重新选择')
          }
        }

      } catch (error) {
        console.error('刷新模型列表失败:', error)
        ElMessage.error(`刷新模型列表失败: ${error.message}`)
      } finally {
        refreshing.value = false
      }
    }

    // 测试连接
    const testConnection = async () => {
      if (!isConfigValid.value) {
        ElMessage.warning('请完善配置信息后再测试')
        return
      }

      testing.value = true
      try {
        ElMessage.info('正在测试AI服务连接...')

        const result = await testAIConnection(config.value)

        // 显示详细的测试结果
        await ElMessageBox.alert(
          `<div style="text-align: left;">
            <p><strong>服务商:</strong> ${result.provider}</p>
            <p><strong>模型:</strong> ${result.model}</p>
            <p><strong>测试响应:</strong> ${result.response}</p>
            ${result.usage ? `<p><strong>Token使用:</strong> ${JSON.stringify(result.usage)}</p>` : ''}
            ${result.latency ? `<p><strong>响应延迟:</strong> ${result.latency}ms</p>` : ''}
            ${result.note ? `<p><strong>注意:</strong> ${result.note}</p>` : ''}
          </div>`,
          'AI服务连接测试成功',
          {
            dangerouslyUseHTMLString: true,
            type: 'success'
          }
        )

        emit('test', { config: config.value, result })

      } catch (error) {
        console.error('AI连接测试失败:', error)

        await ElMessageBox.alert(
          `<div style="text-align: left;">
            <p><strong>错误信息:</strong> ${error.message}</p>
            <p><strong>可能原因:</strong></p>
            <ul>
              <li>API Key 无效或已过期</li>
              <li>API 地址不正确</li>
              <li>网络连接问题</li>
              <li>模型名称不正确</li>
              <li>服务商API配额不足</li>
            </ul>
            <p><strong>建议:</strong> 请检查配置信息并重试</p>
          </div>`,
          'AI服务连接测试失败',
          {
            dangerouslyUseHTMLString: true,
            type: 'error'
          }
        )

      } finally {
        testing.value = false
      }
    }

    // 保存配置
    const saveConfig = () => {
      if (isConfigValid.value) {
        emit('save', config.value)
        ElMessage.success('AI配置已保存')
      }
    }

    // 显示模型信息
    const showModelInfo = () => {
      if (!config.value.provider) {
        ElMessage.warning('请先选择AI服务提供商')
        return
      }

      const models = getCurrentModels()
      const isDynamic = !!dynamicModels.value[config.value.provider]

      const modelListHtml = models.map(model => `
        <div style="margin-bottom: 12px; padding: 8px; border: 1px solid #eee; border-radius: 4px;">
          <div style="font-weight: bold; color: #409eff;">
            ${model.name} ${model.recommended ? '<span style="color: #67c23a;">[推荐]</span>' : ''}
          </div>
          <div style="font-size: 12px; color: #666; margin: 4px 0;">
            ID: <code>${model.id}</code>
          </div>
          <div style="font-size: 12px; color: #666; margin: 4px 0;">
            ${model.description}
          </div>
          <div style="font-size: 12px; color: #666;">
            最大上下文: ${formatTokens(model.maxTokens)}
          </div>
        </div>
      `).join('')

      ElMessageBox.alert(
        `<div style="text-align: left; max-height: 400px; overflow-y: auto;">
          <p><strong>服务商:</strong> ${currentProvider.value?.name}</p>
          <p><strong>模型来源:</strong> ${isDynamic ? 'API动态获取' : '预定义列表'}</p>
          <p><strong>模型数量:</strong> ${models.length} 个</p>
          <hr style="margin: 12px 0;">
          ${modelListHtml}
        </div>`,
        '模型列表信息',
        {
          dangerouslyUseHTMLString: true,
          type: 'info'
        }
      )
    }

    // 格式化token数量
    const formatTokens = (tokens) => {
      if (tokens >= 1000000) {
        return `${(tokens / 1000000).toFixed(1)}M`
      } else if (tokens >= 1000) {
        return `${(tokens / 1000).toFixed(0)}K`
      }
      return tokens.toString()
    }

    return {
      config,
      testing,
      refreshing,
      dynamicModels,
      AI_PROVIDERS,
      currentProvider,
      recommendedModels,
      otherModels,
      isConfigValid,
      validationErrors,
      onProviderChange,
      useDefaultUrl,
      getUrlSuggestions,
      filterModels,
      refreshModels,
      showModelInfo,
      testConnection,
      saveConfig,
      formatTokens
    }
  }
}
</script>

<style scoped>
.ai-config-panel {
  max-width: 600px;
}

.provider-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 4px 0;
}

.provider-icon {
  font-size: 20px;
}

.provider-info {
  flex: 1;
}

.provider-name {
  font-weight: 500;
  color: var(--text-primary);
}

.provider-desc {
  font-size: 12px;
  color: var(--text-muted);
  margin-top: 2px;
}

.url-hint {
  margin-top: 8px;
  font-size: 12px;
  color: var(--text-muted);
}

.hint-label {
  font-weight: 500;
}

.hint-url {
  background: var(--bg-secondary);
  padding: 2px 6px;
  border-radius: 4px;
  font-family: monospace;
}

.model-selection {
  width: 100%;
}

.model-actions {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.model-option {
  padding: 8px 0;
}

.model-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.model-name {
  font-weight: 500;
  color: var(--text-primary);
}

.model-desc {
  font-size: 12px;
  color: var(--text-muted);
  margin-bottom: 4px;
}

.model-tokens {
  font-size: 11px;
  color: var(--text-placeholder);
}

.config-actions {
  display: flex;
  gap: 12px;
}

.config-status {
  margin-top: 16px;
}

.error-list {
  margin: 0;
  padding-left: 20px;
}

.help-icon {
  color: var(--text-muted);
  cursor: help;
}
</style>
