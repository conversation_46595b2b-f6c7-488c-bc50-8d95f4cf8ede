<template>
  <div id="app">
    <div class="app-layout">
      <!-- 现代化侧边栏 -->
      <ModernSidebar />
      
      <!-- 主内容区域 -->
      <div class="main-content-wrapper">
        <!-- 顶部导航栏 -->
        <header class="top-header">
          <div class="header-left">
            <el-breadcrumb separator="/">
              <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
              <el-breadcrumb-item>{{ getPageTitle() }}</el-breadcrumb-item>
            </el-breadcrumb>
          </div>
          
          <div class="header-right">
            <!-- AI状态指示器 -->
            <div class="ai-status">
              <el-tag v-if="aiStore.isReady" type="success" size="small" class="status-tag">
                <el-icon><Check /></el-icon>
                AI已连接
              </el-tag>
              <el-tag v-else-if="appStore.config.geminiApiKey" type="warning" size="small" class="status-tag">
                <el-icon><WarningFilled /></el-icon>
                AI未连接
              </el-tag>
              <el-tag v-else type="info" size="small" class="status-tag">
                <el-icon><InfoFilled /></el-icon>
                未配置API Key
              </el-tag>
            </div>
            
            <!-- 用户操作 -->
            <div class="user-actions">
              <el-tooltip content="主题切换" placement="bottom">
                <el-button circle class="action-btn" @click="toggleTheme">
                  <el-icon><Sunny /></el-icon>
                </el-button>
              </el-tooltip>
              
              <el-tooltip content="关于应用" placement="bottom">
                <el-button circle class="action-btn" @click="showAbout">
                  <el-icon><QuestionFilled /></el-icon>
                </el-button>
              </el-tooltip>
            </div>
          </div>
        </header>

        <!-- 页面内容 -->
        <main class="page-content">
          <router-view />
        </main>
      </div>
    </div>
  </div>
</template>

<script>
import { computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useAppStore } from '@/store/app'
import { useAIStore } from '@/store/ai'
import { ElMessage } from 'element-plus'
import ModernSidebar from '@/components/ModernSidebar.vue'

export default {
  name: 'App',
  components: {
    ModernSidebar
  },
  setup() {
    const route = useRoute()
    const appStore = useAppStore()
    const aiStore = useAIStore()

    // 页面标题映射
    const pageTitles = {
      '/dashboard': '工作台',
      '/projects': '项目管理',
      '/auto-create': '全自动创作',
      '/editor': '智能编辑器',
      '/settings': '设置'
    }

    const getPageTitle = () => {
      return pageTitles[route.path] || route.meta?.title || '页面'
    }

    const toggleTheme = () => {
      // 主题切换逻辑 - 暂时显示消息
      ElMessage({
        message: '主题切换功能开发中...',
        type: 'info'
      })
    }

    const showAbout = () => {
      ElMessage({
        message: 'AI网文小说创作助手 v1.0.0 - 现代化界面版本',
        type: 'info',
        duration: 3000
      })
    }

    // 初始化应用
    onMounted(async () => {
      try {
        await appStore.initializeApp()
        console.log('应用初始化完成')
      } catch (error) {
        console.error('App initialization failed:', error)
      }
    })

    return {
      appStore,
      aiStore,
      getPageTitle,
      toggleTheme,
      showAbout
    }
  }
}
</script>

<style>
/* 全局重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

#app {
  font-family: var(--font-body);
  font-weight: var(--font-weight-normal);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100vh;
  overflow: hidden;
  background: var(--bg-secondary);
  position: relative;
}

.app-layout {
  display: flex;
  height: 100vh;
}

/* 主内容区域 */
.main-content-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-left: 300px; /* 为桌面侧边栏留出空间 */
  transition: margin-left var(--duration-slow) var(--ease-out-quart);
  background: var(--bg-primary);
  border-radius: var(--radius-xl) 0 0 0;
  box-shadow: var(--shadow-paper-lg);
  border: 1px solid var(--border-subtle);
  border-right: none;
  border-bottom: none;
  overflow: hidden;
  position: relative;
}

/* 顶部导航栏 */
.top-header {
  height: 72px;
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-subtle);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-2xl);
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: var(--shadow-paper-sm);
}

.header-left {
  flex: 1;
}

.header-left .el-breadcrumb {
  font-size: 14px;
  font-family: var(--font-body);
  font-weight: var(--font-weight-medium);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.ai-status {
  display: flex;
  align-items: center;
}

.status-tag {
  border-radius: 20px;
  padding: 6px 12px;
  font-weight: 500;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.user-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  background: rgba(0, 0, 0, 0.05);
  color: #64748b;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn:hover {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

/* 页面内容 */
.page-content {
  flex: 1;
  overflow-y: auto;
  padding: 40px;
  background: transparent;
  position: relative;
}

.page-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 90% 10%, rgba(102, 126, 234, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 10% 90%, rgba(118, 75, 162, 0.03) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

/* 滚动条样式 */
.page-content::-webkit-scrollbar {
  width: 6px;
}

.page-content::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.page-content::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
  transition: background 0.3s ease;
}

.page-content::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content-wrapper {
    margin-left: 0;
    margin-top: 64px; /* 为移动端顶部导航留出空间 */
  }
  
  .top-header {
    display: none; /* 移动端隐藏顶部导航，使用侧边栏的导航 */
  }
  
  .page-content {
    padding: 20px 16px;
  }
}

/* Element Plus 专业配色覆盖 */
.el-breadcrumb__item:last-child .el-breadcrumb__inner {
  color: var(--accent-warm);
  font-weight: var(--font-weight-semibold);
  font-family: var(--font-body);
}

.el-breadcrumb__item:not(:last-child) .el-breadcrumb__inner {
  color: var(--text-muted);
  font-family: var(--font-body);
  transition: color var(--duration-fast) var(--ease-out-quart);
}

.el-breadcrumb__item:not(:last-child) .el-breadcrumb__inner:hover {
  color: var(--text-secondary);
}

.el-breadcrumb__separator {
  color: var(--text-subtle);
}

/* 页面过渡动画 */
.page-enter-active,
.page-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.page-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.page-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(4px);
}

/* 自定义动画 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.floating {
  animation: float 3s ease-in-out infinite;
}

/* 渐变文字效果 */
.gradient-text {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 玻璃态效果 */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 卡片阴影效果 */
.card-shadow {
  box-shadow: 
    0 1px 3px rgba(0, 0, 0, 0.1),
    0 1px 2px rgba(0, 0, 0, 0.06);
  transition: box-shadow 0.3s ease;
}

.card-shadow:hover {
  box-shadow: 
    0 10px 25px rgba(0, 0, 0, 0.1),
    0 4px 6px rgba(0, 0, 0, 0.05);
}
</style>