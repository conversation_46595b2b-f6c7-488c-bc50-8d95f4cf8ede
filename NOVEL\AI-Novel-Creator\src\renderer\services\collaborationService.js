/**
 * 协作功能服务
 * 支持多用户同时编辑和评论功能
 */

class CollaborationService {
  constructor() {
    this.collaborators = new Map()
    this.comments = new Map()
    this.activeSession = null
    this.isHost = false
    this.eventListeners = new Map()
    this.syncQueue = []
    this.lastSyncTime = Date.now()
    this.initializeCollaboration()
  }

  /**
   * 初始化协作功能
   */
  initializeCollaboration() {
    // 模拟协作环境，实际应用中可以集成WebSocket或WebRTC
    this.sessionId = this.generateSessionId()
    this.userId = this.generateUserId()
    this.userName = this.loadUserName()
    
    console.log('协作服务初始化完成:', {
      sessionId: this.sessionId,
      userId: this.userId,
      userName: this.userName
    })
  }

  /**
   * 创建协作会话
   * @param {Object} options 会话选项
   */
  createSession(options = {}) {
    const session = {
      id: this.generateSessionId(),
      name: options.name || '新的协作会话',
      description: options.description || '',
      hostId: this.userId,
      hostName: this.userName,
      createdAt: new Date(),
      collaborators: new Map([[this.userId, {
        id: this.userId,
        name: this.userName,
        role: 'host',
        joinedAt: new Date(),
        isActive: true,
        cursor: null,
        selection: null
      }]]),
      permissions: {
        canEdit: options.canEdit !== false,
        canComment: options.canComment !== false,
        canInvite: options.canInvite !== false,
        requireApproval: options.requireApproval || false
      },
      settings: {
        maxCollaborators: options.maxCollaborators || 10,
        autoSave: options.autoSave !== false,
        conflictResolution: options.conflictResolution || 'merge',
        notificationLevel: options.notificationLevel || 'normal'
      }
    }

    this.activeSession = session
    this.isHost = true
    this.saveSession(session)
    
    this.emit('session-created', session)
    return session
  }

  /**
   * 加入协作会话
   * @param {string} sessionId 会话ID
   * @param {string} inviteCode 邀请码
   */
  async joinSession(sessionId, inviteCode = null) {
    try {
      // 模拟加入会话的过程
      const session = this.loadSession(sessionId)
      
      if (!session) {
        throw new Error('会话不存在')
      }

      if (session.settings.requireApproval && !inviteCode) {
        throw new Error('需要邀请码')
      }

      // 检查是否已经在会话中
      if (session.collaborators.has(this.userId)) {
        this.activeSession = session
        this.isHost = session.hostId === this.userId
        this.emit('session-joined', session)
        return session
      }

      // 检查人数限制
      if (session.collaborators.size >= session.settings.maxCollaborators) {
        throw new Error('会话人数已满')
      }

      // 添加协作者
      const collaborator = {
        id: this.userId,
        name: this.userName,
        role: 'collaborator',
        joinedAt: new Date(),
        isActive: true,
        cursor: null,
        selection: null
      }

      session.collaborators.set(this.userId, collaborator)
      this.activeSession = session
      this.isHost = false
      
      this.saveSession(session)
      this.emit('session-joined', session)
      this.emit('collaborator-joined', collaborator)
      
      return session
    } catch (error) {
      console.error('加入会话失败:', error)
      throw error
    }
  }

  /**
   * 离开协作会话
   */
  leaveSession() {
    if (!this.activeSession) return

    const session = this.activeSession
    session.collaborators.delete(this.userId)
    
    if (session.collaborators.size === 0) {
      // 如果没有协作者了，删除会话
      this.deleteSession(session.id)
    } else {
      this.saveSession(session)
    }

    this.emit('session-left', session)
    this.activeSession = null
    this.isHost = false
  }

  /**
   * 添加评论
   * @param {Object} comment 评论信息
   */
  addComment(comment) {
    if (!this.activeSession) {
      throw new Error('没有活动的协作会话')
    }

    const commentData = {
      id: this.generateCommentId(),
      sessionId: this.activeSession.id,
      authorId: this.userId,
      authorName: this.userName,
      content: comment.content,
      position: comment.position || null,
      selectedText: comment.selectedText || '',
      type: comment.type || 'general', // general, suggestion, question, issue
      status: 'open', // open, resolved, dismissed
      createdAt: new Date(),
      updatedAt: new Date(),
      replies: [],
      reactions: new Map(),
      metadata: comment.metadata || {}
    }

    this.comments.set(commentData.id, commentData)
    this.saveComments()
    
    this.emit('comment-added', commentData)
    this.syncChange('comment-add', commentData)
    
    return commentData
  }

  /**
   * 回复评论
   * @param {string} commentId 评论ID
   * @param {string} content 回复内容
   */
  replyToComment(commentId, content) {
    const comment = this.comments.get(commentId)
    if (!comment) {
      throw new Error('评论不存在')
    }

    const reply = {
      id: this.generateCommentId(),
      authorId: this.userId,
      authorName: this.userName,
      content,
      createdAt: new Date(),
      reactions: new Map()
    }

    comment.replies.push(reply)
    comment.updatedAt = new Date()
    
    this.saveComments()
    this.emit('comment-replied', { comment, reply })
    this.syncChange('comment-reply', { commentId, reply })
    
    return reply
  }

  /**
   * 解决评论
   * @param {string} commentId 评论ID
   */
  resolveComment(commentId) {
    const comment = this.comments.get(commentId)
    if (!comment) {
      throw new Error('评论不存在')
    }

    comment.status = 'resolved'
    comment.updatedAt = new Date()
    
    this.saveComments()
    this.emit('comment-resolved', comment)
    this.syncChange('comment-resolve', { commentId })
    
    return comment
  }

  /**
   * 获取评论列表
   * @param {Object} filters 筛选条件
   */
  getComments(filters = {}) {
    let comments = Array.from(this.comments.values())

    // 按会话筛选
    if (this.activeSession) {
      comments = comments.filter(comment => comment.sessionId === this.activeSession.id)
    }

    // 按状态筛选
    if (filters.status) {
      comments = comments.filter(comment => comment.status === filters.status)
    }

    // 按类型筛选
    if (filters.type) {
      comments = comments.filter(comment => comment.type === filters.type)
    }

    // 按作者筛选
    if (filters.authorId) {
      comments = comments.filter(comment => comment.authorId === filters.authorId)
    }

    // 按位置筛选
    if (filters.position) {
      comments = comments.filter(comment => 
        comment.position && 
        comment.position.start <= filters.position.end &&
        comment.position.end >= filters.position.start
      )
    }

    // 排序
    comments.sort((a, b) => {
      if (filters.sortBy === 'updated') {
        return b.updatedAt - a.updatedAt
      }
      return b.createdAt - a.createdAt
    })

    return comments
  }

  /**
   * 更新协作者状态
   * @param {Object} status 状态信息
   */
  updateCollaboratorStatus(status) {
    if (!this.activeSession) return

    const collaborator = this.activeSession.collaborators.get(this.userId)
    if (!collaborator) return

    Object.assign(collaborator, status, {
      lastActiveAt: new Date()
    })

    this.saveSession(this.activeSession)
    this.emit('collaborator-updated', collaborator)
    this.syncChange('collaborator-update', { userId: this.userId, status })
  }

  /**
   * 同步文本变更
   * @param {Object} change 变更信息
   */
  syncTextChange(change) {
    if (!this.activeSession) return

    const changeData = {
      id: this.generateChangeId(),
      sessionId: this.activeSession.id,
      authorId: this.userId,
      authorName: this.userName,
      type: 'text-change',
      operation: change.operation, // insert, delete, replace
      position: change.position,
      content: change.content,
      timestamp: new Date(),
      metadata: change.metadata || {}
    }

    this.syncChange('text-change', changeData)
    this.emit('text-changed', changeData)
    
    return changeData
  }

  /**
   * 处理冲突
   * @param {Array} conflicts 冲突列表
   */
  resolveConflicts(conflicts) {
    const resolution = {
      id: this.generateChangeId(),
      sessionId: this.activeSession.id,
      conflicts,
      resolution: 'auto-merge', // auto-merge, manual, reject
      resolvedBy: this.userId,
      resolvedAt: new Date()
    }

    this.emit('conflicts-resolved', resolution)
    return resolution
  }

  /**
   * 获取协作统计
   */
  getCollaborationStats() {
    if (!this.activeSession) {
      return {
        totalCollaborators: 0,
        activeCollaborators: 0,
        totalComments: 0,
        openComments: 0,
        resolvedComments: 0
      }
    }

    const session = this.activeSession
    const comments = this.getComments()
    
    return {
      sessionId: session.id,
      sessionName: session.name,
      totalCollaborators: session.collaborators.size,
      activeCollaborators: Array.from(session.collaborators.values())
        .filter(c => c.isActive).length,
      totalComments: comments.length,
      openComments: comments.filter(c => c.status === 'open').length,
      resolvedComments: comments.filter(c => c.status === 'resolved').length,
      hostName: session.hostName,
      createdAt: session.createdAt
    }
  }

  /**
   * 生成邀请链接
   * @param {Object} options 邀请选项
   */
  generateInviteLink(options = {}) {
    if (!this.activeSession || !this.isHost) {
      throw new Error('只有主持人可以生成邀请链接')
    }

    const inviteCode = this.generateInviteCode()
    const inviteLink = {
      sessionId: this.activeSession.id,
      inviteCode,
      createdBy: this.userId,
      createdAt: new Date(),
      expiresAt: options.expiresAt || new Date(Date.now() + 24 * 60 * 60 * 1000), // 24小时后过期
      maxUses: options.maxUses || null,
      usedCount: 0,
      permissions: options.permissions || this.activeSession.permissions
    }

    // 保存邀请链接
    this.saveInviteLink(inviteLink)
    
    return {
      url: `${window.location.origin}/collaborate?session=${this.activeSession.id}&invite=${inviteCode}`,
      code: inviteCode,
      ...inviteLink
    }
  }

  // 事件系统
  on(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, [])
    }
    this.eventListeners.get(event).push(callback)
  }

  off(event, callback) {
    if (!this.eventListeners.has(event)) return
    const listeners = this.eventListeners.get(event)
    const index = listeners.indexOf(callback)
    if (index > -1) {
      listeners.splice(index, 1)
    }
  }

  emit(event, data) {
    if (!this.eventListeners.has(event)) return
    this.eventListeners.get(event).forEach(callback => {
      try {
        callback(data)
      } catch (error) {
        console.error('事件处理器错误:', error)
      }
    })
  }

  // 同步变更
  syncChange(type, data) {
    const change = {
      type,
      data,
      timestamp: Date.now(),
      userId: this.userId
    }
    
    this.syncQueue.push(change)
    
    // 模拟网络同步延迟
    setTimeout(() => {
      this.processSyncQueue()
    }, 100)
  }

  processSyncQueue() {
    if (this.syncQueue.length === 0) return
    
    const changes = this.syncQueue.splice(0)
    this.lastSyncTime = Date.now()
    
    // 在实际应用中，这里会发送到服务器
    console.log('同步变更:', changes)
    
    this.emit('changes-synced', changes)
  }

  // 辅助方法
  generateSessionId() {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  generateUserId() {
    let userId = localStorage.getItem('collaborationUserId')
    if (!userId) {
      userId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      localStorage.setItem('collaborationUserId', userId)
    }
    return userId
  }

  generateCommentId() {
    return `comment_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  generateChangeId() {
    return `change_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  generateInviteCode() {
    return Math.random().toString(36).substr(2, 8).toUpperCase()
  }

  loadUserName() {
    return localStorage.getItem('collaborationUserName') || `用户${Math.floor(Math.random() * 1000)}`
  }

  setUserName(name) {
    localStorage.setItem('collaborationUserName', name)
    this.userName = name
  }

  // 数据持久化
  saveSession(session) {
    try {
      const sessions = this.loadSessions()
      sessions[session.id] = {
        ...session,
        collaborators: Array.from(session.collaborators.entries())
      }
      localStorage.setItem('collaborationSessions', JSON.stringify(sessions))
    } catch (error) {
      console.error('保存会话失败:', error)
    }
  }

  loadSession(sessionId) {
    try {
      const sessions = this.loadSessions()
      const session = sessions[sessionId]
      if (session) {
        session.collaborators = new Map(session.collaborators)
        return session
      }
    } catch (error) {
      console.error('加载会话失败:', error)
    }
    return null
  }

  loadSessions() {
    try {
      const data = localStorage.getItem('collaborationSessions')
      return data ? JSON.parse(data) : {}
    } catch (error) {
      console.error('加载会话列表失败:', error)
      return {}
    }
  }

  deleteSession(sessionId) {
    try {
      const sessions = this.loadSessions()
      delete sessions[sessionId]
      localStorage.setItem('collaborationSessions', JSON.stringify(sessions))
    } catch (error) {
      console.error('删除会话失败:', error)
    }
  }

  saveComments() {
    try {
      const commentsArray = Array.from(this.comments.entries())
      localStorage.setItem('collaborationComments', JSON.stringify(commentsArray))
    } catch (error) {
      console.error('保存评论失败:', error)
    }
  }

  loadComments() {
    try {
      const data = localStorage.getItem('collaborationComments')
      if (data) {
        const commentsArray = JSON.parse(data)
        this.comments = new Map(commentsArray)
      }
    } catch (error) {
      console.error('加载评论失败:', error)
    }
  }

  saveInviteLink(inviteLink) {
    try {
      const invites = this.loadInviteLinks()
      invites[inviteLink.inviteCode] = inviteLink
      localStorage.setItem('collaborationInvites', JSON.stringify(invites))
    } catch (error) {
      console.error('保存邀请链接失败:', error)
    }
  }

  loadInviteLinks() {
    try {
      const data = localStorage.getItem('collaborationInvites')
      return data ? JSON.parse(data) : {}
    } catch (error) {
      console.error('加载邀请链接失败:', error)
      return {}
    }
  }
}

// 创建单例实例
export const collaborationService = new CollaborationService()
export default collaborationService
