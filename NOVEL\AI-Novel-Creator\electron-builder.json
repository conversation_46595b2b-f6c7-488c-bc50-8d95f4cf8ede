{"appId": "com.novelcreator.app", "productName": "AI Novel Creator", "copyright": "Copyright © 2024 Novel Creator Team", "directories": {"output": "dist/electron", "buildResources": "build"}, "files": ["src/main/**/*", "dist/renderer/**/*", "node_modules/**/*", "package.json"], "extraResources": [{"from": "src/database", "to": "database"}], "win": {"target": [{"target": "nsis", "arch": ["x64"]}, {"target": "portable", "arch": ["x64"]}], "icon": "build/icon.ico", "artifactName": "${productName}-${version}-${arch}.${ext}", "requestedExecutionLevel": "asInvoker"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "allowElevation": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "AI小说创作助手", "include": "build/installer.nsh", "license": "LICENSE.txt"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}], "icon": "build/icon.icns", "category": "public.app-category.productivity", "artifactName": "${productName}-${version}-${arch}.${ext}"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}], "icon": "build/icon.png", "category": "Office", "artifactName": "${productName}-${version}-${arch}.${ext}"}, "publish": {"provider": "github", "owner": "novel-creator", "repo": "ai-novel-creator"}, "compression": "maximum", "npmRebuild": false}