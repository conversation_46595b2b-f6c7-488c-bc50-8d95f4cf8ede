<template>
  <nav class="modern-navbar glass-card">
    <div class="navbar-container">
      <!-- Logo区域 -->
      <div class="navbar-brand">
        <div class="brand-icon floating">
          <span class="brand-symbol">📚</span>
        </div>
        <div class="brand-text">
          <h1 class="brand-title gradient-text">墨韵文轩</h1>
          <p class="brand-subtitle">AI创作助手</p>
        </div>
      </div>

      <!-- 导航菜单 -->
      <div class="navbar-menu">
        <div class="menu-items">
          <router-link
            v-for="item in menuItems"
            :key="item.path"
            :to="item.path"
            class="menu-item"
            :class="{ active: $route.path === item.path }"
          >
            <div class="menu-icon">
              <el-icon>
                <component :is="item.icon" />
              </el-icon>
            </div>
            <span class="menu-text">{{ item.name }}</span>
            <div class="menu-indicator"></div>
          </router-link>
        </div>
      </div>

      <!-- 用户区域 -->
      <div class="navbar-user">
        <div class="user-avatar neumorphism-btn">
          <el-icon><User /></el-icon>
        </div>
        <div class="user-menu">
          <button class="user-btn neumorphism-btn" @click="toggleTheme">
            <el-icon><Moon /></el-icon>
          </button>
          <button class="user-btn neumorphism-btn" @click="showHelp">
            <el-icon><QuestionFilled /></el-icon>
          </button>
        </div>
      </div>
    </div>
  </nav>
</template>

<script>
import { ref } from 'vue'

export default {
  name: 'ModernNavbar',
  setup() {
    const menuItems = ref([
      {
        name: '文房四宝',
        path: '/',
        icon: 'House'
      },
      {
        name: '书案管理',
        path: '/projects',
        icon: 'Document'
      },
      {
        name: '妙笔生花',
        path: '/auto-create',
        icon: 'StarFilled'
      },
      {
        name: '挥毫泼墨',
        path: '/editor',
        icon: 'EditPen'
      },
      {
        name: '文房设置',
        path: '/settings',
        icon: 'Setting'
      }
    ])

    const toggleTheme = () => {
      // 主题切换逻辑
      console.log('切换主题')
    }

    const showHelp = () => {
      // 显示帮助
      console.log('显示帮助')
    }

    return {
      menuItems,
      toggleTheme,
      showHelp
    }
  }
}
</script>

<style scoped>
/* 引入水墨书香主题 */
@import '@/assets/styles/modern-theme.css';

.modern-navbar {
  position: sticky;
  top: 0;
  z-index: 1000;
  background: var(--glass-heavy);
  backdrop-filter: var(--backdrop-blur);
  border: none;
  border-bottom: 1px solid var(--border-light);
  border-radius: 0;
  box-shadow: var(--shadow-paper-md);
  transition: all var(--duration-normal) var(--ease-paper);
}

.navbar-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--spacing-xl);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

/* Logo区域 */
.navbar-brand {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.brand-icon {
  width: 48px;
  height: 48px;
  background: var(--gradient-gold);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-paper-md);
}

.brand-symbol {
  font-size: 24px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.brand-text {
  display: flex;
  flex-direction: column;
}

.brand-title {
  font-family: var(--font-calligraphy);
  font-size: 20px;
  font-weight: var(--font-weight-semibold);
  margin: 0;
  line-height: 1;
}

.brand-subtitle {
  font-family: var(--font-elegant);
  font-size: 12px;
  color: var(--text-muted);
  margin: 0;
  line-height: 1;
}

/* 导航菜单 */
.navbar-menu {
  flex: 1;
  display: flex;
  justify-content: center;
}

.menu-items {
  display: flex;
  gap: var(--spacing-sm);
  background: var(--bg-elevated);
  padding: var(--spacing-xs);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-inset-light);
}

.menu-item {
  position: relative;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--radius-lg);
  text-decoration: none;
  color: var(--text-muted);
  font-family: var(--font-elegant);
  font-weight: var(--font-weight-medium);
  font-size: 14px;
  transition: all var(--duration-normal) var(--ease-paper);
  overflow: hidden;
}

.menu-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.1), transparent);
  transition: left var(--duration-slow) var(--ease-paper);
}

.menu-item:hover {
  color: var(--text-primary);
  background: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
}

.menu-item:hover::before {
  left: 100%;
}

.menu-item.active {
  background: var(--bg-primary);
  color: var(--text-primary);
  box-shadow: var(--shadow-paper-sm);
}

.menu-item.active .menu-text {
  background: var(--gradient-gold);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.menu-icon {
  font-size: 16px;
  transition: all var(--duration-fast) var(--ease-paper);
}

.menu-item:hover .menu-icon {
  transform: scale(1.1);
}

.menu-indicator {
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: var(--gradient-gold);
  border-radius: var(--radius-full);
  transform: translateX(-50%);
  transition: width var(--duration-normal) var(--ease-paper);
}

.menu-item.active .menu-indicator {
  width: 80%;
}

/* 用户区域 */
.navbar-user {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: var(--text-muted);
  cursor: pointer;
}

.user-menu {
  display: flex;
  gap: var(--spacing-sm);
}

.user-btn {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: var(--text-muted);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-paper);
}

.user-btn:hover {
  color: var(--text-primary);
  transform: translateY(-1px);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .navbar-container {
    padding: 0 var(--spacing-lg);
  }
  
  .menu-item {
    padding: var(--spacing-sm) var(--spacing-md);
  }
  
  .menu-text {
    display: none;
  }
}

@media (max-width: 768px) {
  .navbar-container {
    height: 60px;
    padding: 0 var(--spacing-md);
  }
  
  .brand-text {
    display: none;
  }
  
  .user-menu {
    gap: var(--spacing-xs);
  }
  
  .user-btn {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }
}
</style>
