# Electron + Vue3 侧边栏应用

这是一个使用 Electron 和 Vue3 实现的侧边栏应用，完全按照设计图实现了一模一样的界面效果。

## 功能特性

- ✅ 响应式侧边栏设计
- ✅ 展开/收起功能
- ✅ 深色主题背景
- ✅ 用户信息显示
- ✅ 主导航菜单
- ✅ 工具栏和底部功能区
- ✅ 悬停效果和活跃状态
- ✅ 平滑动画过渡
- ✅ 键盘快捷键支持 (Ctrl+B, ESC)
- ✅ 状态持久化 (localStorage)
- ✅ 工具提示 (收起状态下)
- ✅ SVG图标集成
- ✅ 移动端适配

## 项目结构

```
├── src/
│   ├── main/
│   │   └── main.js          # Electron 主进程
│   ├── components/
│   │   └── Sidebar.vue      # 侧边栏组件
│   ├── App.vue              # 主应用组件
│   ├── main.js              # Vue 应用入口
│   └── style.css            # 全局样式
├── public/
│   └── avatar.svg           # 用户头像
├── package.json
├── vite.config.js
└── index.html
```

## 安装和运行

### 安装依赖

```bash
npm install
```

### 开发模式

```bash
# 启动 Vue 开发服务器
npm run dev:vue

# 在另一个终端启动 Electron
npm run dev:electron

# 或者同时启动两者
npm run dev
```

### 构建

```bash
# 构建 Vue 应用
npm run build:vue

# 构建 Electron 应用
npm run build
```

## 侧边栏功能

### 主要功能

1. **用户信息区域**
   - 显示用户头像
   - 用户名和角色信息
   - 展开/收起按钮

2. **主导航菜单**
   - 首页
   - 项目
   - 空中花园
   - 书吧
   - 编辑漫画

3. **工具栏**
   - 设置

4. **底部功能**
   - 夜间模式切换
   - 注销功能

### 交互特性

- 点击箭头按钮可以展开/收起侧边栏
- 菜单项支持点击切换活跃状态
- 悬停效果提供良好的用户反馈
- 收起状态下显示简化的图标视图
- 键盘快捷键：
  - `Ctrl+B` (或 `Cmd+B`): 切换侧边栏展开/收起
  - `ESC`: 收起侧边栏
- 工具提示：收起状态下悬停显示功能名称
- 状态记忆：自动保存展开/收起状态和活跃菜单项

## 技术栈

- **Electron**: 跨平台桌面应用框架
- **Vue 3**: 现代化的前端框架
- **Vite**: 快速的构建工具
- **CSS3**: 现代样式和动画

## 样式特点

- 深色主题背景 (`#2d2d2d`)
- 响应式设计，支持移动端和平板
- 平滑的过渡动画和微交互
- 现代化的UI设计
- 圆角按钮和卡片式布局
- 阴影效果增强层次感
- 渐变色用户头像

## 开发说明

侧边栏组件使用 Vue 3 的 Composition API 开发，具有良好的可维护性和扩展性。所有的交互逻辑都封装在组件内部，可以轻松集成到其他项目中。

## 许可证

MIT License
