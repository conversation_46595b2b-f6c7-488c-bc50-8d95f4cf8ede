<template>
  <div class="ink-sidebar glass-card" :class="{ 'collapsed': isCollapsed }">
    <!-- 顶部品牌区域 -->
    <div class="sidebar-header">
      <div class="brand-section">
        <div class="brand-icon floating">
          <div class="ink-circle">
            <span class="brand-text gradient-text">墨</span>
          </div>
        </div>
        <div class="brand-info fade-in-up" v-show="!isCollapsed">
          <h2 class="brand-title gradient-text">墨韵文轩</h2>
          <p class="brand-subtitle">AI创作助手</p>
        </div>
      </div>
      
      <!-- 折叠按钮移到底部 -->
    </div>

    <!-- 导航菜单 -->
    <nav class="sidebar-nav">
      <div class="nav-section">
        <h3 class="nav-title" v-show="!isCollapsed">创作工坊</h3>
        
        <div class="nav-items">
          <router-link
            v-for="(item, index) in mainNavItems"
            :key="item.path"
            :to="item.path"
            class="nav-item neumorphism-btn"
            :class="{ 'active': $route.path === item.path }"
            :title="isCollapsed ? item.title : ''"
            :style="{ 'animation-delay': (index * 0.1) + 's' }"
          >
            <div class="nav-icon" :class="{ 'floating': $route.path === item.path }">
              <el-icon>
                <component :is="item.icon" />
              </el-icon>
            </div>
            <span class="nav-text gradient-text" v-show="!isCollapsed" :class="{ 'jade': $route.path === item.path }">{{ item.title }}</span>
            <div class="nav-accent" v-show="$route.path === item.path"></div>
          </router-link>
        </div>
      </div>

      <div class="nav-section">
        <h3 class="nav-title" v-show="!isCollapsed">工具箱</h3>
        
        <div class="nav-items">
          <router-link 
            v-for="item in toolNavItems" 
            :key="item.path"
            :to="item.path"
            class="nav-item"
            :class="{ 'active': $route.path === item.path }"
            :title="isCollapsed ? item.title : ''"
          >
            <div class="nav-icon">
              <el-icon>
                <component :is="item.icon" />
              </el-icon>
            </div>
            <span class="nav-text" v-show="!isCollapsed">{{ item.title }}</span>
            <div class="nav-accent" v-show="$route.path === item.path"></div>
          </router-link>
        </div>
      </div>
    </nav>

    <!-- 底部状态区域 -->
    <div class="sidebar-footer">
      <!-- AI状态指示器 -->
      <div class="ai-status" v-show="!isCollapsed">
        <div class="status-indicator" :class="aiStatusClass">
          <div class="status-dot"></div>
          <span class="status-text">{{ aiStatusText }}</span>
        </div>
      </div>

      <!-- 用户信息 -->
      <div class="user-section" v-show="!isCollapsed">
        <div class="user-avatar">
          <span>文</span>
        </div>
        <div class="user-info">
          <p class="user-name">文墨书生</p>
          <p class="user-role">创作者</p>
        </div>
      </div>

      <!-- 美观的折叠按钮区域 -->
      <div class="collapse-section">
        <button
          class="elegant-collapse-btn"
          @click="toggleCollapse"
          :title="isCollapsed ? '展开侧边栏' : '收起侧边栏'"
        >
          <div class="btn-content">
            <el-icon class="collapse-icon">
              <component :is="isCollapsed ? 'DArrowRight' : 'DArrowLeft'" />
            </el-icon>
            <span v-show="!isCollapsed" class="collapse-text">收起</span>
          </div>
        </button>
      </div>
    </div>

    <!-- 装饰性元素 -->
    <div class="sidebar-decoration">
      <div class="ink-wash top"></div>
      <div class="ink-wash bottom"></div>
      <div class="bamboo-pattern"></div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'

export default {
  name: 'InkSidebar',
  setup() {
    const isCollapsed = ref(false)

    // 模拟store数据
    const mockAppStore = {
      isConfigured: false
    }

    // 主要导航项
    const mainNavItems = [
      { path: '/dashboard', title: '文房四宝', icon: 'HomeFilled' },
      { path: '/projects', title: '书案管理', icon: 'Document' },
      { path: '/auto-create', title: '妙笔生花', icon: 'StarFilled' },
      { path: '/editor', title: '挥毫泼墨', icon: 'EditPen' }
    ]

    // 工具导航项
    const toolNavItems = [
      { path: '/settings', title: '文房设置', icon: 'Setting' },
      { path: '/ui-showcase', title: 'UI展示', icon: 'View' }
    ]

    // AI状态
    const aiStatusClass = computed(() => {
      return mockAppStore.isConfigured ? 'connected' : 'disconnected'
    })

    const aiStatusText = computed(() => {
      return mockAppStore.isConfigured ? 'AI笔墨就绪' : '待配置笔墨'
    })

    // 切换折叠状态
    const toggleCollapse = () => {
      isCollapsed.value = !isCollapsed.value
    }

    return {
      isCollapsed,
      mainNavItems,
      toolNavItems,
      aiStatusClass,
      aiStatusText,
      toggleCollapse
    }
  }
}
</script>

<style scoped>
.ink-sidebar {
  width: 280px;
  height: 100vh;
  background: var(--gradient-paper);
  border-right: 2px solid var(--border-light);
  position: relative;
  transition: all var(--duration-normal) var(--ease-paper);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;  /* 防止侧边栏被压缩 */
}

.ink-sidebar.collapsed {
  width: 80px;
}

/* 收缩状态下的品牌区域居中 */
.ink-sidebar.collapsed .brand-section {
  justify-content: center;
  margin-bottom: var(--spacing-sm);
}

.ink-sidebar.collapsed .sidebar-header {
  padding: var(--spacing-md) var(--spacing-sm);
  text-align: center;
}

/* === 顶部品牌区域 === */
.sidebar-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-light);
  position: relative;
  z-index: 10;
  overflow: hidden;  /* 确保子元素不会溢出侧边栏 */
}

.brand-section {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.brand-icon {
  flex-shrink: 0;
}

.ink-circle {
  width: 48px;
  height: 48px;
  background: var(--gradient-ink);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-paper-md);
  position: relative;
  overflow: hidden;
}

.ink-circle::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-gold);
  opacity: 0.2;
  border-radius: 50%;
}

.brand-text {
  font-family: var(--font-calligraphy);
  font-size: 20px;
  font-weight: var(--font-weight-bold);
  color: var(--paper-xuan);
  position: relative;
  z-index: 1;
}

.brand-info {
  flex: 1;
  min-width: 0;
}

.brand-title {
  font-family: var(--font-calligraphy);
  font-size: 18px;
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xs) 0;
  letter-spacing: 0.02em;
}

.brand-subtitle {
  font-family: var(--font-elegant);
  font-size: 12px;
  color: var(--text-muted);
  margin: 0;
  letter-spacing: 0.01em;
}

/* 美观的折叠按钮区域 */
.collapse-section {
  padding: var(--spacing-md);
  border-top: 1px solid var(--border-light);
  background: var(--gradient-paper);
}

.elegant-collapse-btn {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--glass-medium);
  backdrop-filter: var(--backdrop-blur-light);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-paper);
  position: relative;
  overflow: hidden;
}

.elegant-collapse-btn:hover {
  background: var(--glass-heavy);
  transform: translateY(-1px);
  box-shadow: var(--shadow-paper-md);
}

.elegant-collapse-btn:active {
  transform: translateY(0);
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  color: var(--text-muted);
  font-family: var(--font-elegant);
  font-size: 13px;
  font-weight: var(--font-weight-medium);
}

.collapse-icon {
  font-size: 16px;
  transition: all var(--duration-fast) var(--ease-paper);
}

.collapse-text {
  transition: all var(--duration-fast) var(--ease-paper);
}

.elegant-collapse-btn:hover .btn-content {
  color: var(--text-primary);
}

.elegant-collapse-btn:hover .collapse-icon {
  transform: scale(1.1);
}

/* 收缩状态下的样式 */
.ink-sidebar.collapsed .elegant-collapse-btn {
  padding: var(--spacing-sm);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  margin: 0 auto;  /* 确保按钮在容器中居中 */
}

.ink-sidebar.collapsed .btn-content {
  justify-content: center;
}

.ink-sidebar.collapsed .collapse-section {
  display: flex;
  justify-content: center;  /* 确保按钮容器居中 */
  padding: var(--spacing-md) var(--spacing-sm);
}

/* === 导航菜单 === */
.sidebar-nav {
  flex: 1;
  padding: var(--spacing-lg) var(--spacing-md);
  overflow-y: auto;
}

.nav-section {
  margin-bottom: var(--spacing-xl);
}

.nav-title {
  font-family: var(--font-calligraphy);
  font-size: 14px;
  font-weight: var(--font-weight-medium);
  color: var(--text-muted);
  margin: 0 0 var(--spacing-md) var(--spacing-md);
  letter-spacing: 0.02em;
  position: relative;
}

.nav-title::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 24px;
  height: 1px;
  background: var(--gradient-gold);
  opacity: 0.6;
}

.nav-items {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.nav-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-md);
  border-radius: var(--radius-lg);
  text-decoration: none;
  color: var(--text-secondary);
  transition: all var(--duration-normal) var(--ease-paper);
  position: relative;
  overflow: hidden;
  font-family: var(--font-elegant);
  font-weight: var(--font-weight-medium);
}

.nav-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: var(--gradient-gold);
  opacity: 0.08;
  transition: left var(--duration-slow) var(--ease-brush);
}

.nav-item:hover::before {
  left: 0;
}

.nav-item:hover {
  color: var(--huang-jin);
  background: rgba(212, 175, 55, 0.05);
  transform: translateX(4px);
}

.nav-item.active {
  background: var(--gradient-gold);
  color: var(--ink-jiao);
  font-weight: var(--font-weight-semibold);
  box-shadow: var(--shadow-paper-md);
}

.nav-item.active::before {
  display: none;
}

.nav-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-md);
  flex-shrink: 0;
  transition: all var(--duration-normal) var(--ease-paper);
}

/* 收缩状态下的导航项样式 */
.ink-sidebar.collapsed .nav-item {
  padding: var(--spacing-md);  /* 与展开状态完全相同的padding */
  justify-content: center;
  margin: 0 var(--spacing-xs) var(--spacing-xs) var(--spacing-xs);
  min-height: 44px;
  width: calc(100% - var(--spacing-md));
  display: flex;
  align-items: center;
}

.ink-sidebar.collapsed .nav-icon {
  margin-right: 0;
  width: 20px;
  height: 20px;
  margin: 0;  /* 移除auto margin，让flex居中处理 */
}

/* 确保收缩状态下的导航区域居中 */
.ink-sidebar.collapsed .nav-items {
  align-items: center;
}

.ink-sidebar.collapsed .nav-section {
  padding: 0 var(--spacing-xs);
}

.nav-text {
  flex: 1;
  font-size: 14px;
  letter-spacing: 0.01em;
}

.nav-accent {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 60%;
  background: var(--ink-jiao);
  border-radius: var(--radius-full);
}

/* === 底部状态区域 === */
.sidebar-footer {
  padding: var(--spacing-lg) var(--spacing-md);
  border-top: 1px solid var(--border-light);
  background: rgba(247, 245, 241, 0.5);
}

.ai-status {
  margin-bottom: var(--spacing-lg);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-lg);
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
}

.status-indicator.connected .status-dot {
  background: var(--dan-qing);
  box-shadow: 0 0 8px rgba(39, 174, 96, 0.4);
}

.status-indicator.disconnected .status-dot {
  background: var(--text-placeholder);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-text {
  font-family: var(--font-elegant);
  font-size: 12px;
  color: var(--text-muted);
}

.user-section {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
}

.user-avatar {
  width: 36px;
  height: 36px;
  background: var(--gradient-jade);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: var(--font-calligraphy);
  font-weight: var(--font-weight-semibold);
  color: var(--paper-xuan);
  font-size: 14px;
}

.user-info {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-family: var(--font-elegant);
  font-size: 13px;
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin: 0 0 2px 0;
}

.user-role {
  font-family: var(--font-elegant);
  font-size: 11px;
  color: var(--text-muted);
  margin: 0;
}

/* === 装饰性元素 === */
.sidebar-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.ink-wash {
  position: absolute;
  width: 100%;
  height: 120px;
  opacity: 0.03;
}

.ink-wash.top {
  top: 0;
  background: radial-gradient(ellipse at center top, var(--ink-nong) 0%, transparent 70%);
}

.ink-wash.bottom {
  bottom: 0;
  background: radial-gradient(ellipse at center bottom, var(--ink-zhong) 0%, transparent 70%);
}

.bamboo-pattern {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 2px;
  height: 200px;
  background: 
    linear-gradient(to bottom, 
      transparent 0%, 
      var(--song-lv) 20%, 
      transparent 25%,
      transparent 30%,
      var(--song-lv) 50%,
      transparent 55%,
      transparent 60%,
      var(--song-lv) 80%,
      transparent 100%
    );
  opacity: 0.1;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ink-sidebar {
    width: 80px;  /* 在移动端收缩为图标模式 */
    position: relative;  /* 改为相对定位，避免覆盖 */
    z-index: 100;
  }

  .ink-sidebar.collapsed {
    width: 80px;
  }

  .brand-info,
  .nav-text,
  .user-info {
    display: none;  /* 在移动端隐藏文字 */
  }

  /* 移动端折叠按钮优化 */
  .collapse-section {
    padding: var(--spacing-sm);
  }

  .elegant-collapse-btn {
    padding: var(--spacing-xs);
  }

  .btn-content {
    font-size: 12px;
  }

  .collapse-icon {
    font-size: 14px;
  }

  .ink-sidebar.collapsed .elegant-collapse-btn {
    width: 32px;
    height: 32px;
    margin: 0 auto;  /* 确保移动端也居中 */
  }

  .ink-sidebar.collapsed .collapse-section {
    display: flex;
    justify-content: center;
    padding: var(--spacing-sm);
  }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
  .ink-sidebar {
    width: 60px;  /* 更小的宽度 */
  }
}
</style>
