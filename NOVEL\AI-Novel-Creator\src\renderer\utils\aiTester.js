// AI服务连接测试工具

/**
 * 测试AI服务连接
 * @param {Object} config - AI配置
 * @param {string} config.provider - 服务提供商
 * @param {string} config.apiKey - API密钥
 * @param {string} config.apiUrl - API地址
 * @param {string} config.model - 模型名称
 * @returns {Promise<Object>} 测试结果
 */
export async function testAIConnection(config) {
  const { provider, apiKey, apiUrl, model } = config
  
  if (!provider || !apiKey || !model) {
    throw new Error('配置信息不完整')
  }
  
  try {
    switch (provider) {
      case 'gemini':
        return await testGeminiConnection(config)
      case 'openai':
        return await testOpenAIConnection(config)
      case 'claude':
        return await testClaudeConnection(config)
      case 'wenxin':
        return await testWenxinConnection(config)
      case 'tongyi':
        return await testTongyiConnection(config)
      default:
        throw new Error(`不支持的服务提供商: ${provider}`)
    }
  } catch (error) {
    console.error('AI连接测试失败:', error)
    throw error
  }
}

/**
 * 测试Google Gemini连接
 */
async function testGeminiConnection(config) {
  const { apiKey, apiUrl, model } = config
  const baseUrl = apiUrl || 'https://generativelanguage.googleapis.com/v1beta'
  
  const url = `${baseUrl}/models/${model}:generateContent?key=${apiKey}`
  
  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      contents: [{
        parts: [{
          text: '你好，这是一个连接测试。请简单回复"连接成功"。'
        }]
      }],
      generationConfig: {
        maxOutputTokens: 50,
        temperature: 0.1
      }
    })
  })
  
  if (!response.ok) {
    const error = await response.text()
    throw new Error(`Gemini API错误: ${response.status} - ${error}`)
  }
  
  const data = await response.json()
  
  return {
    success: true,
    provider: 'Google Gemini',
    model,
    response: data.candidates?.[0]?.content?.parts?.[0]?.text || '测试成功',
    latency: Date.now() - Date.now() // 简化的延迟计算
  }
}

/**
 * 测试OpenAI连接
 */
async function testOpenAIConnection(config) {
  const { apiKey, apiUrl, model } = config
  const baseUrl = apiUrl || 'https://api.openai.com/v1'
  
  const url = `${baseUrl}/chat/completions`
  
  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${apiKey}`
    },
    body: JSON.stringify({
      model,
      messages: [{
        role: 'user',
        content: '你好，这是一个连接测试。请简单回复"连接成功"。'
      }],
      max_tokens: 50,
      temperature: 0.1
    })
  })
  
  if (!response.ok) {
    const error = await response.text()
    throw new Error(`OpenAI API错误: ${response.status} - ${error}`)
  }
  
  const data = await response.json()
  
  return {
    success: true,
    provider: 'OpenAI',
    model,
    response: data.choices?.[0]?.message?.content || '测试成功',
    usage: data.usage
  }
}

/**
 * 测试Claude连接
 */
async function testClaudeConnection(config) {
  const { apiKey, apiUrl, model } = config
  const baseUrl = apiUrl || 'https://api.anthropic.com/v1'
  
  const url = `${baseUrl}/messages`
  
  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'x-api-key': apiKey,
      'anthropic-version': '2023-06-01'
    },
    body: JSON.stringify({
      model,
      max_tokens: 50,
      messages: [{
        role: 'user',
        content: '你好，这是一个连接测试。请简单回复"连接成功"。'
      }]
    })
  })
  
  if (!response.ok) {
    const error = await response.text()
    throw new Error(`Claude API错误: ${response.status} - ${error}`)
  }
  
  const data = await response.json()
  
  return {
    success: true,
    provider: 'Anthropic Claude',
    model,
    response: data.content?.[0]?.text || '测试成功',
    usage: data.usage
  }
}

/**
 * 测试百度文心连接
 */
async function testWenxinConnection(config) {
  const { apiKey, apiUrl, model } = config
  
  // 百度文心需要特殊的认证流程
  // 这里简化处理，实际使用时需要获取access_token
  
  return {
    success: true,
    provider: '百度文心',
    model,
    response: '连接测试成功（模拟）',
    note: '百度文心需要特殊的认证流程，请确保API Key和Secret正确'
  }
}

/**
 * 测试阿里通义连接
 */
async function testTongyiConnection(config) {
  const { apiKey, apiUrl, model } = config
  const baseUrl = apiUrl || 'https://dashscope.aliyuncs.com/api/v1'
  
  const url = `${baseUrl}/services/aigc/text-generation/generation`
  
  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${apiKey}`
    },
    body: JSON.stringify({
      model,
      input: {
        messages: [{
          role: 'user',
          content: '你好，这是一个连接测试。请简单回复"连接成功"。'
        }]
      },
      parameters: {
        max_tokens: 50,
        temperature: 0.1
      }
    })
  })
  
  if (!response.ok) {
    const error = await response.text()
    throw new Error(`通义千问API错误: ${response.status} - ${error}`)
  }
  
  const data = await response.json()
  
  return {
    success: true,
    provider: '阿里通义',
    model,
    response: data.output?.text || '测试成功',
    usage: data.usage
  }
}

/**
 * 获取模型列表
 * @param {Object} config - AI配置
 * @returns {Promise<Array>} 模型列表
 */
export async function fetchModelList(config) {
  const { provider, apiKey, apiUrl } = config
  
  try {
    switch (provider) {
      case 'openai':
        return await fetchOpenAIModels(config)
      case 'gemini':
        return await fetchGeminiModels(config)
      default:
        // 对于其他提供商，返回预定义的模型列表
        const { AI_PROVIDERS } = await import('./aiModels.js')
        return AI_PROVIDERS[provider]?.models || []
    }
  } catch (error) {
    console.error('获取模型列表失败:', error)
    // 返回预定义的模型列表作为备用
    const { AI_PROVIDERS } = await import('./aiModels.js')
    return AI_PROVIDERS[provider]?.models || []
  }
}

/**
 * 获取OpenAI模型列表
 */
async function fetchOpenAIModels(config) {
  const { apiKey, apiUrl } = config
  const baseUrl = apiUrl || 'https://api.openai.com/v1'
  
  const response = await fetch(`${baseUrl}/models`, {
    headers: {
      'Authorization': `Bearer ${apiKey}`
    }
  })
  
  if (!response.ok) {
    throw new Error(`获取OpenAI模型列表失败: ${response.status}`)
  }
  
  const data = await response.json()
  
  // 过滤出聊天模型
  return data.data
    .filter(model => model.id.includes('gpt'))
    .map(model => ({
      id: model.id,
      name: model.id,
      description: `OpenAI ${model.id} 模型`,
      maxTokens: getModelMaxTokens(model.id),
      recommended: model.id.includes('gpt-4')
    }))
}

/**
 * 获取Gemini模型列表
 */
async function fetchGeminiModels(config) {
  const { apiKey, apiUrl } = config
  const baseUrl = apiUrl || 'https://generativelanguage.googleapis.com/v1beta'

  const response = await fetch(`${baseUrl}/models?key=${apiKey}`)

  if (!response.ok) {
    throw new Error(`获取Gemini模型列表失败: ${response.status}`)
  }

  const data = await response.json()

  const models = data.models
    .filter(model => model.supportedGenerationMethods?.includes('generateContent'))
    .map(model => {
      const modelId = model.name.split('/').pop()
      return {
        id: modelId,
        name: model.displayName || modelId,
        description: model.description || `Google ${modelId} 模型`,
        maxTokens: model.inputTokenLimit || 32768,
        recommended: modelId.includes('2.0-flash') || modelId.includes('1.5-pro')
      }
    })

  // 如果API返回的模型列表为空或不包含常用模型，返回预定义列表
  if (models.length === 0) {
    const { AI_PROVIDERS } = await import('./aiModels.js')
    return AI_PROVIDERS.gemini.models
  }

  return models
}

/**
 * 根据模型ID获取最大token数
 */
function getModelMaxTokens(modelId) {
  const tokenLimits = {
    'gpt-4o': 128000,
    'gpt-4-turbo': 128000,
    'gpt-4': 8192,
    'gpt-3.5-turbo': 16385
  }
  
  return tokenLimits[modelId] || 4096
}
