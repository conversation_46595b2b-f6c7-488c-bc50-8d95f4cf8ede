<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI小说创作助手</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background: #2c3e50; color: white; padding: 20px; text-align: center; margin-bottom: 20px; }
        .card { background: white; border-radius: 8px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .button { background: #3498db; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 5px; }
        .button:hover { background: #2980b9; }
        .form-group { margin-bottom: 15px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input, .form-group select, .form-group textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 AI小说创作助手</h1>
            <p>基于Google Gemini AI的智能创作工具</p>
        </div>

        <div class="card">
            <h2>🎉 应用启动成功！</h2>
            <div class="status success">
                <strong>恭喜！</strong> 如果您能看到这个页面，说明应用已经可以正常访问了。
            </div>
            <p>这是一个静态演示版本，展示了应用的基本功能和界面。</p>
        </div>

        <div class="card">
            <h3>📝 项目管理</h3>
            <div class="form-group">
                <label>项目名称:</label>
                <input type="text" placeholder="请输入项目名称" />
            </div>
            <div class="form-group">
                <label>小说题材:</label>
                <select>
                    <option>玄幻</option>
                    <option>都市</option>
                    <option>科幻</option>
                    <option>历史</option>
                </select>
            </div>
            <div class="form-group">
                <label>小说类型:</label>
                <select>
                    <option>男频</option>
                    <option>女频</option>
                </select>
            </div>
            <button class="button" onclick="alert('项目创建成功！(演示)')">创建项目</button>
        </div>

        <div class="card">
            <h3>🚀 全自动创作</h3>
            <div class="status warning">
                <strong>注意：</strong> 全自动创作功能需要配置Google Gemini API Key。
            </div>
            <div class="form-group">
                <label>总章节数:</label>
                <input type="number" value="50" />
            </div>
            <div class="form-group">
                <label>单章字数:</label>
                <input type="number" value="3000" />
            </div>
            <button class="button" onclick="alert('需要先配置API Key')">开始创作</button>
        </div>

        <div class="card">
            <h3>✏️ 智能编辑器</h3>
            <div class="form-group">
                <label>章节标题:</label>
                <input type="text" placeholder="第一章：开始" />
            </div>
            <div class="form-group">
                <label>章节内容:</label>
                <textarea rows="10" placeholder="开始您的创作..."></textarea>
            </div>
            <button class="button" onclick="alert('保存成功！(演示)')">保存</button>
            <button class="button" onclick="alert('AI续写功能需要API Key')">AI续写</button>
            <button class="button" onclick="alert('文笔优化功能需要API Key')">文笔优化</button>
        </div>

        <div class="card">
            <h3>⚙️ 设置</h3>
            <div class="form-group">
                <label>Google Gemini API Key:</label>
                <input type="password" placeholder="请输入API Key" />
            </div>
            <button class="button" onclick="alert('设置保存成功！(演示)')">保存设置</button>
            <p style="margin-top: 10px; color: #666;">
                <a href="https://ai.google.dev/" target="_blank">获取API Key</a>
            </p>
        </div>

        <div class="card">
            <h3>💡 下一步</h3>
            <p>要运行完整的Vue.js应用，请：</p>
            <ol>
                <li>在Windows PowerShell中运行：<code>cd "\\wsl$\Ubuntu\home\miluo\NOVEL\AI-Novel-Creator"</code></li>
                <li>运行：<code>npm run dev:renderer</code></li>
                <li>访问：<code>http://localhost:3000</code></li>
            </ol>
        </div>
    </div>
</body>
</html>