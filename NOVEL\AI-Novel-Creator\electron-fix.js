// AI小说创作助手 - Electron修复脚本
// 如果Electron窗口显示空白，请在开发者工具Console中执行此脚本

console.log('🔧 正在修复Electron显示问题...');

// 清除页面内容并创建启动页面
document.documentElement.innerHTML = `
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI小说创作助手</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }
        .container {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 20px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            max-width: 500px;
            width: 90%;
        }
        .logo { font-size: 48px; margin-bottom: 20px; }
        h1 { font-size: 28px; margin-bottom: 15px; font-weight: 300; }
        .subtitle { font-size: 16px; opacity: 0.8; margin-bottom: 30px; }
        .status {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4CAF50;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 30px;
        }
        button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 16px;
            border-radius: 25px;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
        }
        .secondary {
            background: linear-gradient(45deg, #2196F3, #1976D2);
            box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
        }
        .secondary:hover {
            box-shadow: 0 6px 20px rgba(33, 150, 243, 0.4);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">📚</div>
        <h1>AI小说创作助手</h1>
        <p class="subtitle">墨韵文轩 · 智能创作平台</p>
        
        <div class="status">
            <div style="font-size: 24px; margin-bottom: 10px;">✅</div>
            <div>Electron 修复成功</div>
            <div id="countdown" style="font-size: 12px; margin-top: 5px;">准备跳转到主应用...</div>
        </div>
        
        <button onclick="goToApp()">立即进入主应用</button>
        <button class="secondary" onclick="openInBrowser()">在浏览器中打开</button>
        
        <div style="margin-top: 20px; font-size: 12px; opacity: 0.7;">
            如果跳转失败，请确保Vite服务器正在运行
        </div>
    </div>

    <script>
        function goToApp() {
            console.log('🚀 跳转到主应用: http://localhost:3000');
            window.location.href = 'http://localhost:3000';
        }
        
        function openInBrowser() {
            console.log('🌐 在浏览器中打开主应用');
            if (window.require) {
                try {
                    const { shell } = window.require('electron');
                    shell.openExternal('http://localhost:3000');
                } catch (e) {
                    console.log('无法调用shell，使用window.open');
                    window.open('http://localhost:3000', '_blank');
                }
            } else {
                window.open('http://localhost:3000', '_blank');
            }
        }
        
        // 3秒后自动跳转
        let countdown = 3;
        function updateCountdown() {
            if (countdown > 0) {
                document.getElementById('countdown').textContent = 
                    countdown + ' 秒后自动跳转到主应用...';
                countdown--;
                setTimeout(updateCountdown, 1000);
            } else {
                goToApp();
            }
        }
        
        // 开始倒计时
        setTimeout(updateCountdown, 1000);
        
        console.log('✅ Electron修复脚本执行完成！');
    </script>
</body>
`;

console.log('🎉 修复完成！页面将在3秒后自动跳转到主应用。');
