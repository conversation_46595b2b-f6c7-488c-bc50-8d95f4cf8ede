// AI创作服务 - 完整迁移自NOVEL项目
export class AICreationService {
  constructor() {
    this.config = null
    this.loadConfig()
  }

  // 加载AI配置
  loadConfig() {
    try {
      const savedSettings = localStorage.getItem('complete-novel-settings')
      if (savedSettings) {
        const settings = JSON.parse(savedSettings)
        this.config = settings.ai
      }
    } catch (error) {
      console.error('加载AI配置失败:', error)
    }
  }

  // 检查配置是否有效
  isConfigured() {
    this.loadConfig()
    const isValid = this.config &&
           this.config.provider &&
           this.config.apiKey &&
           this.config.model

    console.log('AI配置检查:', {
      config: this.config,
      isValid,
      provider: this.config?.provider,
      hasApiKey: !!this.config?.apiKey,
      model: this.config?.model
    })

    return isValid
  }

  // 调用AI API的通用方法（带重试机制）
  async callAI(prompt, options = {}) {
    const maxRetries = 3
    let lastError = null

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await this._callAIInternal(prompt, options)
      } catch (error) {
        lastError = error
        console.warn(`AI调用失败 (尝试 ${attempt}/${maxRetries}):`, error.message)

        // 如果是503错误（服务过载），等待后重试
        if (error.message.includes('503') || error.message.includes('overloaded')) {
          if (attempt < maxRetries) {
            const delay = attempt * 5000 // 5秒、10秒、15秒
            console.log(`等待 ${delay/1000} 秒后重试...`)
            await new Promise(resolve => setTimeout(resolve, delay))
            continue
          }
        }

        // 其他错误直接抛出
        if (attempt === maxRetries) {
          throw lastError
        }
      }
    }

    throw lastError
  }

  // 内部AI调用方法
  async _callAIInternal(prompt, options = {}) {
    if (!this.isConfigured()) {
      throw new Error('AI服务未配置，请先在设置中配置AI服务')
    }

    const { temperature = 0.7, maxTokens = 4000, stream = false, onProgress } = options
    const { provider } = this.config

    try {
      switch (provider) {
        case 'gemini':
          if (stream && onProgress) {
            return await this.callGeminiStream(prompt, { temperature, onProgress, maxTokens })
          }
          return await this.callGemini(prompt, { temperature, maxTokens })
        case 'openai':
          if (stream && onProgress) {
            return await this.callOpenAIStream(prompt, { temperature, onProgress, maxTokens })
          }
          return await this.callOpenAI(prompt, { temperature, maxTokens })
        case 'claude':
          if (stream && onProgress) {
            return await this.callClaudeStream(prompt, { temperature, onProgress, maxTokens })
          }
          return await this.callClaude(prompt, { temperature, maxTokens })
        default:
          throw new Error(`不支持的AI服务商: ${provider}`)
      }
    } catch (error) {
      console.error('AI调用失败:', error)
      throw new Error(`AI服务调用失败: ${error.message}`)
    }
  }

  // 调用Gemini API
  async callGemini(prompt, options) {
    const { apiKey, apiUrl, model } = this.config
    const baseUrl = apiUrl || 'https://generativelanguage.googleapis.com/v1beta'
    
    const url = `${baseUrl}/models/${model}:generateContent?key=${apiKey}`
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: prompt
          }]
        }],
        generationConfig: {
          ...(options.maxTokens && { maxOutputTokens: options.maxTokens }),
          temperature: options.temperature,
          topP: 0.8,
          topK: 40
        }
      })
    })

    if (!response.ok) {
      const error = await response.text()
      throw new Error(`Gemini API错误: ${response.status} - ${error}`)
    }

    const data = await response.json()
    return data.candidates?.[0]?.content?.parts?.[0]?.text || ''
  }

  // 调用Gemini流式API
  async callGeminiStream(prompt, options) {
    const { apiKey, apiUrl, model } = this.config
    const { onProgress, temperature, maxTokens } = options
    const baseUrl = apiUrl || 'https://generativelanguage.googleapis.com/v1beta'
    
    const url = `${baseUrl}/models/${model}:streamGenerateContent?key=${apiKey}`
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: prompt
          }]
        }],
        generationConfig: {
          ...(maxTokens && { maxOutputTokens: maxTokens }),
          temperature,
          topP: 0.8,
          topK: 40
        }
      })
    })

    if (!response.ok) {
      const error = await response.text()
      throw new Error(`Gemini API错误: ${response.status} - ${error}`)
    }

    const reader = response.body.getReader()
    const decoder = new TextDecoder()
    let fullText = ''

    try {
      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value, { stream: true })
        const lines = chunk.split('\n').filter(line => line.trim())

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const jsonStr = line.slice(6)
              if (jsonStr === '[DONE]') continue
              
              const data = JSON.parse(jsonStr)
              const text = data.candidates?.[0]?.content?.parts?.[0]?.text
              
              if (text) {
                fullText += text
                if (onProgress) {
                  onProgress(text)
                }
              }
            } catch (e) {
              console.warn('解析流式响应失败:', e)
            }
          }
        }
      }
    } finally {
      reader.releaseLock()
    }

    return fullText
  }

  // 调用OpenAI API
  async callOpenAI(prompt, options) {
    const { apiKey, apiUrl, model } = this.config
    const baseUrl = apiUrl || 'https://api.openai.com/v1'
    
    const url = `${baseUrl}/chat/completions`
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model,
        messages: [{
          role: 'user',
          content: prompt
        }],
        max_tokens: options.maxTokens,
        temperature: options.temperature
      })
    })

    if (!response.ok) {
      const error = await response.text()
      throw new Error(`OpenAI API错误: ${response.status} - ${error}`)
    }

    const data = await response.json()
    return data.choices?.[0]?.message?.content || ''
  }

  // 调用OpenAI流式API
  async callOpenAIStream(prompt, options) {
    const { apiKey, apiUrl, model } = this.config
    const { onProgress, temperature, maxTokens } = options
    const baseUrl = apiUrl || 'https://api.openai.com/v1'
    
    const url = `${baseUrl}/chat/completions`
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model,
        messages: [{
          role: 'user',
          content: prompt
        }],
        max_tokens: maxTokens,
        temperature,
        stream: true
      })
    })

    if (!response.ok) {
      const error = await response.text()
      throw new Error(`OpenAI API错误: ${response.status} - ${error}`)
    }

    const reader = response.body.getReader()
    const decoder = new TextDecoder()
    let fullText = ''

    try {
      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value, { stream: true })
        const lines = chunk.split('\n').filter(line => line.trim())

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const jsonStr = line.slice(6)
              if (jsonStr === '[DONE]') continue
              
              const data = JSON.parse(jsonStr)
              const text = data.choices?.[0]?.delta?.content
              
              if (text) {
                fullText += text
                if (onProgress) {
                  onProgress(text)
                }
              }
            } catch (e) {
              console.warn('解析流式响应失败:', e)
            }
          }
        }
      }
    } finally {
      reader.releaseLock()
    }

    return fullText
  }

  // 调用Claude API
  async callClaude(prompt, options) {
    const { apiKey, apiUrl, model } = this.config
    const baseUrl = apiUrl || 'https://api.anthropic.com/v1'
    
    const url = `${baseUrl}/messages`
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': apiKey,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify({
        model,
        max_tokens: options.maxTokens,
        messages: [{
          role: 'user',
          content: prompt
        }]
      })
    })

    if (!response.ok) {
      const error = await response.text()
      throw new Error(`Claude API错误: ${response.status} - ${error}`)
    }

    const data = await response.json()
    return data.content?.[0]?.text || ''
  }

  // 生成故事大纲
  async generateOutline(createForm, onProgress = null) {
    const { title, genre, category, requirements, creativity, chapterCount, wordsPerChapter } = createForm

    const outlinePrompt = `请为以下小说创作详细的故事大纲：

## 基本信息
- 小说名称：${title}
- 题材类型：${genre}
- 读者群体：${category}
- 总章节数：${chapterCount}章
- 单章字数：${wordsPerChapter}字

## 创作要求
${requirements}

## 创意灵感
${creativity}

## 请生成完整的故事大纲，包含：

### 1. 故事背景
- 世界观设定
- 时代背景
- 主要场景

### 2. 主线剧情
- 故事起因
- 发展过程
- 高潮冲突
- 结局走向

### 3. 核心冲突
- 主要矛盾
- 对立关系
- 解决方式

### 4. 分卷规划（如适用）
- 各卷主题
- 重要节点
- 发展脉络

要求：大纲详细完整，符合${genre}风格，适合${category}读者，能支撑${chapterCount}章的内容展开。`

    if (onProgress) onProgress('正在生成故事大纲...')

    const outline = await this.callAI(outlinePrompt, {
      maxTokens: 6000,
      temperature: 0.8,
      stream: true,
      onProgress: (chunk) => {
        if (onProgress) onProgress(chunk)
      }
    })

    return outline
  }

  // 生成角色设定
  async generateCharacter(params, onProgress = null) {
    const { name, role, genre, background } = params

    const characterPrompt = `请为${genre}小说创作角色设定：

## 角色信息
- 姓名：${name}
- 角色：${role}
- 背景：${background}

## 请生成完整角色设定：

### 基本信息
- 年龄、身份、职业
- 性格特点（主要特征）
- 外貌特征（简洁描述）

### 背景故事
- 出身经历
- 核心动机
- 重要关系

### 能力特长
- 主要技能
- 特殊能力（如适用）

要求：内容完整但简洁，符合${genre}风格。`

    if (onProgress) onProgress('正在生成角色设定...')

    const characterInfo = await this.callAI(characterPrompt, {
      maxTokens: 4000,
      temperature: 0.8,
      stream: true,
      onProgress: (chunk) => {
        if (onProgress) onProgress(chunk)
      }
    })

    return characterInfo
  }

  // 智能续写
  async continueWriting(selectedText, context, requirements = '', length = 1000) {
    const prompt = `请基于以下内容进行智能续写：

上下文：
${context}

选中文本：
${selectedText}

续写要求：${requirements}
续写长度：约${length}字

请保持文风一致，情节自然衔接，继续推进故事发展。`

    return await this.callAI(prompt, {
      maxTokens: length * 1.5,
      temperature: 0.7
    })
  }

  // 优化文本
  async optimizeText(params) {
    const { text, optimizeType = 'general' } = params

    const optimizePrompts = {
      general: '请优化以下文本，使其更加生动、流畅、富有感染力',
      dialogue: '请优化以下对话，使其更加自然、生动、符合人物性格',
      description: '请优化以下描写，使其更加细腻、形象、富有画面感',
      plot: '请优化以下情节，使其更加紧凑、合理、引人入胜'
    }

    const prompt = `${optimizePrompts[optimizeType]}：

原文：
${text}

请保持原意的基础上进行优化，直接输出优化后的内容。`

    return await this.callAI(prompt, {
      maxTokens: text.length * 1.5,
      temperature: 0.6
    })
  }

  // 生成情节建议
  async generatePlotSuggestions(context, requirements = '') {
    const prompt = `基于以下内容，请提供4个具体的情节发展建议：

当前内容：
${context}

创作要求：${requirements}

请提供4个不同方向的情节建议，每个建议包含：
1. 具体的情节发展
2. 可能的人物互动
3. 预期的戏剧效果

要求简洁明了，每个建议50-100字。`

    const suggestions = await this.callAI(prompt, {
      maxTokens: 1000,
      temperature: 0.8
    })

    // 解析建议并返回数组
    return suggestions.split('\n').filter(line => line.trim()).slice(0, 4)
  }

  // 生成角色对话
  async generateDialogue(character, context, situation = '') {
    const prompt = `请为角色"${character}"生成对话：

角色信息：${character}
对话情境：${situation}
上下文：${context}

请生成符合角色性格的自然对话，包含：
1. 角色的话语
2. 适当的动作描写
3. 心理活动（如需要）

要求对话生动自然，符合角色设定。`

    return await this.callAI(prompt, {
      maxTokens: 800,
      temperature: 0.7
    })
  }
}

// 创建全局实例
export const aiCreationService = new AICreationService()
